{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\basf\\platform\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\basf\\platform\\index.vue", "mtime": 1752737748515}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\.babelrc", "mtime": 1752737748316}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["Platform", "Common", "autoTableHeight", "name", "data", "loading", "saving", "modalVisible", "modalViewVisible", "modalTitle", "statusOps", "pageInfo", "code", "status", "total", "page", "limit", "formItemRules", "required", "message", "trigger", "formItem", "id", "remark", "shopCount", "baseSiteList", "columns", "type", "width", "title", "key", "min<PERSON><PERSON><PERSON>", "slot", "fixed", "siteColumns", "methods", "handleModal", "Object", "assign", "handleView", "_this", "getById", "then", "res", "handleResetForm", "form", "$refs", "resetFields", "handleReset", "handleSubmit", "_this2", "validate", "valid", "edit", "$Message", "success", "handleSearch", "finally", "add", "_this3", "listPage", "records", "parseInt", "handlePage", "current", "handlePageSize", "size", "handleRemove", "_this4", "$Modal", "confirm", "onOk", "remove", "handleClick", "row", "mounted"], "sources": ["src/view/module/basf/platform/index.vue"], "sourcesContent": ["<template>\r\n    <div>\r\n        <Card shadow=\"true\">\r\n          <Form ref=\"searchForm\" class=\"searchForm\" :model=\"pageInfo\" inline @submit.native.prevent>\r\n            <FormItem prop=\"code\" >\r\n              <Input type=\"text\" v-model=\"pageInfo.code\" placeholder=\"请输入平台编码\"/>\r\n            </FormItem>\r\n            <FormItem prop=\"name\" >\r\n              <Input type=\"text\" v-model=\"pageInfo.name\" placeholder=\"请输入平台名称\"/>\r\n            </FormItem>\r\n            <FormItem prop=\"status\">\r\n              <Select v-model=\"pageInfo.status\" style=\"width: 100px\" placeholder=\"请选择状态\" :clearable=\"false\" transfer=\"true\">\r\n                <Option v-for=\"v in statusOps\" :value=\"v.key\" :key=\"v.key\">{{v.name }}</Option>\r\n              </Select>\r\n            </FormItem>\r\n            <FormItem>\r\n              <Button type=\"primary\" @click=\"handleSearch(1)\">查询</Button>&nbsp;\r\n              <Button @click=\"handleResetForm('searchForm')\">重置</Button>\r\n            </FormItem>\r\n          </Form>\r\n          <div class=\"search-con search-con-top\">\r\n            <ButtonGroup>\r\n              <Button :disabled=\"!hasAuthority('platformAdd')\" class=\"search-btn\" type=\"primary\" @click=\"handleModal()\">\r\n                <span>添加</span>\r\n              </Button>\r\n            </ButtonGroup>\r\n          </div>\r\n            <Table border=\"true\" ref=\"autoTableRef\" :max-height=\"autoTableHeight($refs.autoTableRef)\" :columns=\"columns\" :data=\"data\" :loading=\"loading\">\r\n              <template v-slot:status=\"{row}\">\r\n                  <Badge v-for=\"v in statusOps\" :text=\"v.name\" v-if=\"v.key === row.status\" :status=\"v.key ===0?'success':'warning'\" v-bind:key=\"v.key\"></Badge>\r\n              </template>\r\n              <template v-slot:action=\"{row}\">\r\n                  <a v-if=\"hasAuthority('platformEdit')\" @click=\"handleModal(row)\">编辑</a>&nbsp;\r\n                  <a border=\"true\" @click=\"handleView(row.id)\">查看</a>&nbsp;\r\n                  <a border=\"true\" v-if=\"hasAuthority('platformEdit')\" @click=\"handleClick('remove',row)\">删除</a>\r\n              </template>\r\n            </Table>\r\n            <Page :total=\"pageInfo.total\" size=\"small\" :current=\"pageInfo.page\" :page-size=\"pageInfo.limit\"\r\n              show-elevator=\"true\" show-sizer=\"true\" show-total=\"true\" @on-change=\"handlePage\" @on-page-size-change='handlePageSize'></Page>\r\n        </Card>\r\n        <Modal v-model=\"modalVisible\" :title=\"modalTitle\" width=\"40\" @on-cancel=\"handleReset\">\r\n          <div>\r\n            <Form ref=\"form\" :model=\"formItem\" :rules=\"formItemRules\" :label-width=\"100\">\r\n              <FormItem label=\"平台名称\" prop=\"name\">\r\n                  <Input v-model=\"formItem.name\"  placeholder=\"请输入内容\"></Input>\r\n              </FormItem>\r\n              <FormItem label=\"平台编码\" prop=\"code\">\r\n                  <Input v-model=\"formItem.code\" type = 'number' placeholder=\"请输入内容\"></Input>\r\n              </FormItem>\r\n              <FormItem label=\"备注\" prop=\"remark\">\r\n                  <Input v-model=\"formItem.remark\" type=\"textarea\" placeholder=\"请输入内容\"></Input>\r\n              </FormItem>\r\n              <FormItem label=\"状态\">\r\n                <RadioGroup v-model=\"formItem.status\" type=\"button\">\r\n                  <Radio v-for=\"v in statusOps\" :label=\"v.key\" v-if=\"v.key !== -1\" v-bind:key=\"v.key\">{{v.name }}</Radio>\r\n                </RadioGroup>\r\n              </FormItem>\r\n            </Form>\r\n            <div class=\"drawer-footer\">\r\n              <Button type=\"default\" @click=\"handleReset\">取消</Button>&nbsp;\r\n              <Button type=\"primary\" @click=\"handleSubmit\" :loading=\"saving\">保存</Button>\r\n            </div>\r\n          </div>\r\n        </Modal>\r\n        <Modal v-model=\"modalViewVisible\" :title=\"modalTitle\" width=\"40\" @on-cancel=\"handleReset\">\r\n          <div>\r\n            <Form ref=\"viewForm\" :model=\"formItem\" :label-width=\"100\">\r\n              <FormItem label=\"平台编码\" prop=\"code\">\r\n                <Input v-model=\"formItem.code\" readonly=\"true\"></Input>\r\n              </FormItem>\r\n              <FormItem label=\"平台名称\" prop=\"name\">\r\n                <Input v-model=\"formItem.name\" readonly=\"true\"></Input>\r\n              </FormItem>\r\n              <FormItem label=\"店铺数量\" prop=\"shopCount\">\r\n                <Input v-model=\"formItem.shopCount\" readonly=\"true\"></Input>\r\n              </FormItem>\r\n              <FormItem label=\"备注\" prop=\"remark\">\r\n                <Input v-model=\"formItem.remark\" type=\"textarea\" readonly=\"true\"></Input>\r\n              </FormItem>\r\n              <FormItem label=\"状态\">\r\n                <RadioGroup v-model=\"formItem.status\" type=\"button\" readonly=\"true\">\r\n                  <Radio v-for=\"v in statusOps\" :label=\"v.key\" v-if=\"v.key !== -1\" v-bind:key=\"v.key\">{{v.name }}</Radio>\r\n                </RadioGroup>\r\n              </FormItem>\r\n              <FormItem label=\"站点列表\" prop=\"baseSiteList\">\r\n                <Table border=\"true\" :columns=\"siteColumns\" :data=\"formItem.baseSiteList\" :loading=\"loading\">\r\n                  <template v-slot:status=\"{ row }\">\r\n                    <Badge v-for=\"v in statusOps\" :text=\"v.name\" v-if=\"v.key === row.status\" :status=\"v.key ===0?'success':'warning'\" v-bind:key=\"v.key\"></Badge>\r\n                  </template>\r\n                </Table>\r\n              </FormItem>\r\n            </Form>\r\n          </div>\r\n          <div slot=\"footer\"></div>\r\n        </Modal>\r\n    </div>\r\n</template>\r\n<style>\r\n  .drawer-footer{\r\n    border: 0;\r\n  }\r\n</style>\r\n<script>\r\nimport Platform from '@/api/basf/platform'\r\nimport Common from '@/api/basic/common'\r\nimport {autoTableHeight} from '@/libs/tools.js'\r\n\r\nexport default {\r\n        name: 'platformList',\r\n        data () {\r\n            return {\r\n                autoTableHeight,\r\n                loading: false,\r\n                saving: false,\r\n                modalVisible: false,\r\n                modalViewVisible: false,\r\n                modalTitle: '',\r\n                statusOps:Common.statusOps,\r\n                pageInfo: {\r\n                  code:'',\r\n                  name:'',\r\n                  status:-1,\r\n                  total: 0,\r\n                  page: 1,\r\n                  limit: 10\r\n                },\r\n                formItemRules: {\r\n                  code: [\r\n                      {required: true, message: '平台编号不能为空'}\r\n                  ],\r\n                  name: [\r\n                      {required: true, message: '平台名称不能为空', trigger: 'blur'}\r\n                  ],\r\n                },\r\n                formItem: {\r\n                  id: '',\r\n                  name: '',\r\n                  code: '',\r\n                  remark: '',\r\n                  status: '',\r\n                  shopCount: '',\r\n                  baseSiteList: [],\r\n                },\r\n                columns: [\r\n                    {\r\n                        type: 'selection',\r\n                        width: 60,\r\n                    },\r\n                    {\r\n                      title: '平台编码',\r\n                      key: 'code',\r\n                      minWidth: 100\r\n                    },\r\n                    {\r\n                        title: '平台名称',\r\n                        key: 'name',\r\n                        minWidth: 100\r\n                    },\r\n                    {\r\n                      title: '备注',\r\n                      key: 'remark',\r\n                      minWidth: 200\r\n                    },\r\n                    {\r\n                        title: '网店数量',\r\n                        key: 'shopCount',\r\n                        width: 100\r\n                    },\r\n                    {\r\n                        title: '状态',\r\n                        key: 'status',\r\n                        slot: 'status',\r\n                        width: 100,\r\n                    },\r\n                    {\r\n                        title: '操作',\r\n                        slot: 'action',\r\n                        fixed: 'right',\r\n                        width: 220\r\n                    }\r\n                ],\r\n                siteColumns: [\r\n                    {\r\n                        title: '站点编码',\r\n                        key: 'code',\r\n                        minWidth: 100\r\n                    },\r\n                    {\r\n                        title: '站点名称',\r\n                        key: 'name',\r\n                        width: 200\r\n                    },\r\n                    {\r\n                        title: '状态',\r\n                        key: 'status',\r\n                        slot: 'status',\r\n                        width: 200,\r\n                    },\r\n                ],\r\n                data: []\r\n            }\r\n        },\r\n        methods: {\r\n            handleModal (data) {\r\n                if (data) {\r\n                    this.modalTitle = '编辑平台';\r\n                    this.formItem = Object.assign({}, this.formItem, data);\r\n                } else {\r\n                    this.modalTitle = '添加平台'\r\n                }\r\n                this.modalVisible = true\r\n            },\r\n            handleView (data) {\r\n              Platform.getById(data).then(res => {\r\n                               this.formItem = res.data;\r\n                            });\r\n                if (data) {\r\n                    this.modalTitle = '查看平台'\r\n                    this.formItem = Object.assign({}, this.formItem, data)\r\n                }\r\n                this.modalViewVisible = true\r\n            },\r\n            handleResetForm(form) {\r\n              if (this.$refs[form]){\r\n                this.$refs[form].resetFields();\r\n              }\r\n            },\r\n            handleReset () {\r\n              this.formItem = {\r\n                  id: '',\r\n                  code: '',\r\n                  name: '',\r\n                  remark: '',\r\n                  status: ''\r\n                }\r\n                //重置验证\r\n                let form  = this.$refs['form']\r\n                form.resetFields()\r\n                this.modalVisible = false\r\n                this.saving = false\r\n            },\r\n            handleSubmit () {\r\n                let form  = this.$refs['form']\r\n                form.validate((valid) => {\r\n                    if (valid) {\r\n                        this.saving = true\r\n                        if (this.formItem.id) {\r\n                            Platform.edit(this.formItem).then(res => {\r\n                              if (res['code'] === 0) {\r\n                                this.$Message.success('保存成功')\r\n                                this.handleReset()\r\n                                this.handleSearch()\r\n                              }\r\n                            }).finally(() => {this.saving = false})\r\n                        } else {\r\n                              Platform.add(this.formItem).then(res => {\r\n                                if (res['code'] === 0) {\r\n                                  this.$Message.success('保存成功')\r\n                                  this.handleReset()\r\n                                  this.handleSearch()\r\n                                }\r\n                              }).finally(() => {this.saving = false})\r\n                        }\r\n                   }\r\n                })\r\n            },\r\n            handleSearch (page) {\r\n                if (page) {\r\n                    this.pageInfo.page = page\r\n                }\r\n                this.loading = true\r\n              Platform.listPage({page: this.pageInfo.page,limit: this.pageInfo.limit,name:this.pageInfo.name,code:this.pageInfo.code,status:this.pageInfo.status}).then(res => {\r\n                    this.data = res.data.records\r\n                    this.pageInfo.total = parseInt(res.data.total)\r\n                }).finally(() => {this.loading = false})\r\n            },\r\n            handlePage (current) {\r\n                this.pageInfo.page = current\r\n                this.handleSearch()\r\n            },\r\n            handlePageSize (size) {\r\n                this.pageInfo.limit = size\r\n                this.handleSearch()\r\n            },\r\n            handleRemove (data) {\r\n              this.$Modal.confirm({\r\n                    title: '确定删除吗？',\r\n                    onOk: () => {\r\n                      Platform.remove(data.id).then(res => {\r\n                            if (res['code'] === 0) {\r\n                                this.pageInfo.page = 1;\r\n                                this.$Message.success('删除成功');\r\n                            }\r\n                            this.handleSearch();\r\n                        })\r\n                    }\r\n                })\r\n            },\r\n            handleClick (name, row) {\r\n                switch (name) {\r\n                    case 'remove':\r\n                        this.handleRemove(row)\r\n                        break\r\n                }\r\n            }\r\n        },\r\n        mounted: function () {\r\n            this.handleSearch()\r\n        }\r\n    }\r\n</script>\r\n"], "mappings": ";;AAuGA,OAAAA,QAAA;AACA,OAAAC,MAAA;AACA,SAAAC,eAAA;AAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAF,eAAA,EAAAA,eAAA;MACAG,OAAA;MACAC,MAAA;MACAC,YAAA;MACAC,gBAAA;MACAC,UAAA;MACAC,SAAA,EAAAT,MAAA,CAAAS,SAAA;MACAC,QAAA;QACAC,IAAA;QACAT,IAAA;QACAU,MAAA;QACAC,KAAA;QACAC,IAAA;QACAC,KAAA;MACA;MACAC,aAAA;QACAL,IAAA,GACA;UAAAM,QAAA;UAAAC,OAAA;QAAA,EACA;QACAhB,IAAA,GACA;UAAAe,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAC,QAAA;QACAC,EAAA;QACAnB,IAAA;QACAS,IAAA;QACAW,MAAA;QACAV,MAAA;QACAW,SAAA;QACAC,YAAA;MACA;MACAC,OAAA,GACA;QACAC,IAAA;QACAC,KAAA;MACA,GACA;QACAC,KAAA;QACAC,GAAA;QACAC,QAAA;MACA,GACA;QACAF,KAAA;QACAC,GAAA;QACAC,QAAA;MACA,GACA;QACAF,KAAA;QACAC,GAAA;QACAC,QAAA;MACA,GACA;QACAF,KAAA;QACAC,GAAA;QACAF,KAAA;MACA,GACA;QACAC,KAAA;QACAC,GAAA;QACAE,IAAA;QACAJ,KAAA;MACA,GACA;QACAC,KAAA;QACAG,IAAA;QACAC,KAAA;QACAL,KAAA;MACA,EACA;MACAM,WAAA,GACA;QACAL,KAAA;QACAC,GAAA;QACAC,QAAA;MACA,GACA;QACAF,KAAA;QACAC,GAAA;QACAF,KAAA;MACA,GACA;QACAC,KAAA;QACAC,GAAA;QACAE,IAAA;QACAJ,KAAA;MACA,EACA;MACAxB,IAAA;IACA;EACA;EACA+B,OAAA;IACAC,WAAA,WAAAA,YAAAhC,IAAA;MACA,IAAAA,IAAA;QACA,KAAAK,UAAA;QACA,KAAAY,QAAA,GAAAgB,MAAA,CAAAC,MAAA,UAAAjB,QAAA,EAAAjB,IAAA;MACA;QACA,KAAAK,UAAA;MACA;MACA,KAAAF,YAAA;IACA;IACAgC,UAAA,WAAAA,WAAAnC,IAAA;MAAA,IAAAoC,KAAA;MACAxC,QAAA,CAAAyC,OAAA,CAAArC,IAAA,EAAAsC,IAAA,WAAAC,GAAA;QACAH,KAAA,CAAAnB,QAAA,GAAAsB,GAAA,CAAAvC,IAAA;MACA;MACA,IAAAA,IAAA;QACA,KAAAK,UAAA;QACA,KAAAY,QAAA,GAAAgB,MAAA,CAAAC,MAAA,UAAAjB,QAAA,EAAAjB,IAAA;MACA;MACA,KAAAI,gBAAA;IACA;IACAoC,eAAA,WAAAA,gBAAAC,IAAA;MACA,SAAAC,KAAA,CAAAD,IAAA;QACA,KAAAC,KAAA,CAAAD,IAAA,EAAAE,WAAA;MACA;IACA;IACAC,WAAA,WAAAA,YAAA;MACA,KAAA3B,QAAA;QACAC,EAAA;QACAV,IAAA;QACAT,IAAA;QACAoB,MAAA;QACAV,MAAA;MACA;MACA;MACA,IAAAgC,IAAA,QAAAC,KAAA;MACAD,IAAA,CAAAE,WAAA;MACA,KAAAxC,YAAA;MACA,KAAAD,MAAA;IACA;IACA2C,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,IAAAL,IAAA,QAAAC,KAAA;MACAD,IAAA,CAAAM,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAF,MAAA,CAAA5C,MAAA;UACA,IAAA4C,MAAA,CAAA7B,QAAA,CAAAC,EAAA;YACAtB,QAAA,CAAAqD,IAAA,CAAAH,MAAA,CAAA7B,QAAA,EAAAqB,IAAA,WAAAC,GAAA;cACA,IAAAA,GAAA;gBACAO,MAAA,CAAAI,QAAA,CAAAC,OAAA;gBACAL,MAAA,CAAAF,WAAA;gBACAE,MAAA,CAAAM,YAAA;cACA;YACA,GAAAC,OAAA;cAAAP,MAAA,CAAA5C,MAAA;YAAA;UACA;YACAN,QAAA,CAAA0D,GAAA,CAAAR,MAAA,CAAA7B,QAAA,EAAAqB,IAAA,WAAAC,GAAA;cACA,IAAAA,GAAA;gBACAO,MAAA,CAAAI,QAAA,CAAAC,OAAA;gBACAL,MAAA,CAAAF,WAAA;gBACAE,MAAA,CAAAM,YAAA;cACA;YACA,GAAAC,OAAA;cAAAP,MAAA,CAAA5C,MAAA;YAAA;UACA;QACA;MACA;IACA;IACAkD,YAAA,WAAAA,aAAAzC,IAAA;MAAA,IAAA4C,MAAA;MACA,IAAA5C,IAAA;QACA,KAAAJ,QAAA,CAAAI,IAAA,GAAAA,IAAA;MACA;MACA,KAAAV,OAAA;MACAL,QAAA,CAAA4D,QAAA;QAAA7C,IAAA,OAAAJ,QAAA,CAAAI,IAAA;QAAAC,KAAA,OAAAL,QAAA,CAAAK,KAAA;QAAAb,IAAA,OAAAQ,QAAA,CAAAR,IAAA;QAAAS,IAAA,OAAAD,QAAA,CAAAC,IAAA;QAAAC,MAAA,OAAAF,QAAA,CAAAE;MAAA,GAAA6B,IAAA,WAAAC,GAAA;QACAgB,MAAA,CAAAvD,IAAA,GAAAuC,GAAA,CAAAvC,IAAA,CAAAyD,OAAA;QACAF,MAAA,CAAAhD,QAAA,CAAAG,KAAA,GAAAgD,QAAA,CAAAnB,GAAA,CAAAvC,IAAA,CAAAU,KAAA;MACA,GAAA2C,OAAA;QAAAE,MAAA,CAAAtD,OAAA;MAAA;IACA;IACA0D,UAAA,WAAAA,WAAAC,OAAA;MACA,KAAArD,QAAA,CAAAI,IAAA,GAAAiD,OAAA;MACA,KAAAR,YAAA;IACA;IACAS,cAAA,WAAAA,eAAAC,IAAA;MACA,KAAAvD,QAAA,CAAAK,KAAA,GAAAkD,IAAA;MACA,KAAAV,YAAA;IACA;IACAW,YAAA,WAAAA,aAAA/D,IAAA;MAAA,IAAAgE,MAAA;MACA,KAAAC,MAAA,CAAAC,OAAA;QACAzC,KAAA;QACA0C,IAAA,WAAAA,KAAA;UACAvE,QAAA,CAAAwE,MAAA,CAAApE,IAAA,CAAAkB,EAAA,EAAAoB,IAAA,WAAAC,GAAA;YACA,IAAAA,GAAA;cACAyB,MAAA,CAAAzD,QAAA,CAAAI,IAAA;cACAqD,MAAA,CAAAd,QAAA,CAAAC,OAAA;YACA;YACAa,MAAA,CAAAZ,YAAA;UACA;QACA;MACA;IACA;IACAiB,WAAA,WAAAA,YAAAtE,IAAA,EAAAuE,GAAA;MACA,QAAAvE,IAAA;QACA;UACA,KAAAgE,YAAA,CAAAO,GAAA;UACA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAnB,YAAA;EACA;AACA"}]}