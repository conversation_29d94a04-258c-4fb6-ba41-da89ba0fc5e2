package com.sy.erp.server.activiti;

import com.aimo.common.model.ResultBody;
import com.sy.erp.server.service.WorkflowService;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.Expression;
import org.activiti.engine.delegate.JavaDelegate;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;
import javax.annotation.PostConstruct;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class NotificationDelegate implements JavaDelegate, ApplicationContextAware {
    // 字段名必须与XML配置完全一致（包括大小写）
    private Expression recipients;
    private Expression notificationTitle;
    private Expression notificationContent;

    @Autowired
    private WorkflowService workflowService;

    private static ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        NotificationDelegate.applicationContext = applicationContext;
    }

    @PostConstruct
    public void init() {
        log.info("NotificationDelegate 初始化完成");
        log.info("WorkflowService 注入状态: {}", workflowService != null ? "成功" : "失败");
    }

    @Override
    public void execute(DelegateExecution execution) {
        String userIds = cleanExpressionValue(recipients.getValue(execution).toString());
        String title = cleanExpressionValue(notificationTitle.getValue(execution).toString());
        String content = cleanExpressionValue(notificationContent.getValue(execution).toString())
                + "--"
                + new SimpleDateFormat("yyyy/MM/dd HH:mm:ss").format(new Date());
        log.info("发送通知给: {}", userIds);
        log.info("标题: {}", title);
        log.info("内容: {}", content);
        try {
            // 获取 WorkflowService，优先使用注入的实例，如果为null则从ApplicationContext获取
            WorkflowService service = workflowService;
            if (service == null && applicationContext != null) {
                log.info("从ApplicationContext获取WorkflowService");
                service = applicationContext.getBean(WorkflowService.class);
            }
            if (service == null) {
                log.error("无法获取WorkflowService实例");
                return;
            }
            ResultBody<Boolean> resultBody = service.getAimoNotification(userIds, title, content);
            if (resultBody != null && resultBody.isOk() && resultBody.getData() != null && resultBody.getData()) {
                log.info("通知发送成功");
            } else {
                String errorMsg = resultBody != null ? resultBody.getMessage() : "未知错误";
                System.out.println("通知发送失败: " + errorMsg);
                log.info("通知发送失败: {}", errorMsg);
                if (errorMsg.contains("401") || errorMsg.contains("Unauthorized")) {
                    log.error("认证失败提示：工作流调用外部服务时缺少认证信息");
                    log.error("建议检查：1. Feign 配置 2. 服务间认证配置 3. noAuth 标记是否生效");
                }
            }
        } catch (Exception e) {
            log.error("调用通知接口失败: {}", e.getMessage());
            // 针对不同类型的异常给出不同的处理建议
            if (e.getMessage() != null) {
                if (e.getMessage().contains("401") || e.getMessage().contains("Unauthorized")) {
                    log.error("认证失败：建议检查服务间认证配置或使用 noAuth 标记");
                } else if (e.getMessage().contains("HystrixRuntimeException")) {
                    log.error("Hystrix 熔断：建议检查目标服务是否可用，或配置降级处理");
                } else if (e.getMessage().contains("FeignException")) {
                    log.error("Feign 调用失败：建议检查目标服务地址和网络连接");
                }
            }
        }
    }

    /**
     * 清理表达式残留符号（支持${xxx}和#{}格式）
     * @param value 原始字符串
     * @return 清理后的纯净值
     */
    private String cleanExpressionValue(String value) {
        if (value == null) return null;
        // 正则匹配并移除 ${...} 和 #{...} 包装
        return value.replaceAll("^[\\$#]?\\{(.*)\\}$", "$1");
    }
}
