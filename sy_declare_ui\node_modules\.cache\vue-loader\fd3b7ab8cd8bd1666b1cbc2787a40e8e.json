{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\basf\\shop\\index.vue?vue&type=template&id=77c54dd8&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\basf\\shop\\index.vue", "mtime": 1752737748516}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}