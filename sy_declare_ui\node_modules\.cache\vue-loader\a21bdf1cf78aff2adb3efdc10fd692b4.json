{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\base\\department\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\base\\department\\index.vue", "mtime": 1752737748509}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AA4GA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/view/module/base/department", "sourcesContent": ["<template>\r\n  <div>\r\n    <Row :gutter=\"8\">\r\n      <Col :xs=\"8\" :sm=\"8\" :md=\"8\" :lg=\"6\">\r\n      <Card :shadow=\"true\">\r\n        <tree-table style=\"max-height:700px;overflow: auto\" expand-key=\"companyName\" @radio-click=\"rowCompanyClick\" :expand-type=\"false\" :is-fold=\"false\" :tree-type=\"true\" :selectable=\"false\" :columns=\"companyColumns\" :data=\"companyList\">\r\n          <template v-slot:status=\"scope\">\r\n            <Badge v-if=\"scope.row.status===0\" status=\"success\" />\r\n            <Badge v-else status=\"error\" />\r\n          </template>\r\n        </tree-table>\r\n      </Card>\r\n      </Col>\r\n      <Col :xs=\"10\" :sm=\"10\" :md=\"10\" :lg=\"8\">\r\n      <Card :shadow=\"true\">\r\n        <tree-table style=\"max-height:700px;overflow: auto\" expand-key=\"departmentName\" @radio-click=\"rowDepartmentClick\" :expand-type=\"false\" :is-fold=\"false\" :tree-type=\"true\" :selectable=\"false\" :columns=\"departmentColumns\" :data=\"departmentList\">\r\n          <template v-slot:status=\"scope\">\r\n            <Badge v-if=\"scope.row.status===0\" status=\"success\" />\r\n            <Badge v-else status=\"error\" />\r\n          </template>\r\n        </tree-table>\r\n      </Card>\r\n      </Col>\r\n      <Col :xs=\"16\" :sm=\"16\" :md=\"16\" :lg=\"8\">\r\n      <Card :shadow=\"true\" v-if=\"this.cd === true\" style=\"position: relative\">\r\n        <div class=\"search-con search-con-top\">\r\n          <ButtonGroup>\r\n            <Button type=\"primary\" :disabled=\"!hasAuthority('departmentAdd')\" @click=\"setClickType(true, 'add')\">添加\r\n            </Button>\r\n            <Button type=\"primary\" :disabled=\"!(formItem.id && hasAuthority('departmentDel'))\" @click=\"confirmModal = true\" style=\"margin-left: 8px\">删除\r\n            </Button>\r\n          </ButtonGroup>\r\n          <Modal v-model=\"confirmModal\" title=\"提示\" @on-ok=\"handleRemove\">\r\n            确定删除,部门【{{formItem.departmentName}}】吗?\r\n          </Modal>\r\n        </div>\r\n        <Form ref=\"menuForm\" :model=\"formItem\" :rules=\"formItemRules\" :label-width=\"80\">\r\n          <FormItem label=\"所属公司\" prop=\"companyId\">\r\n            <Select type=\"text\" v-model=\"formItem.companyId\" @on-change=\"change\" filterable>\r\n              <Option v-for=\"(item,index) in companyList\" :value=\"item.id\" :key=\"index\">{{ item.companyName }}</Option>\r\n            </Select>\r\n          </FormItem>\r\n          <FormItem label=\"上级部门\" prop=\"parentId\">\r\n            <treeselect v-model=\"formItem.parentId\" :options=\"selectTreeData\" :default-expand-level=\"1\" :normalizer=\"treeSelectNormalizer\" />\r\n          </FormItem>\r\n          <FormItem label=\"负责人\" prop=\"managerId\">\r\n            <Select type=\"text\" filterable v-model=\"formItem.managerId\" :loading=\"positionLoading\">\r\n              <Option v-for=\"(item,index) in userList\" :key=\"index\" :value=\"item.id\">{{ item.nickName }}</Option>\r\n            </Select>\r\n          </FormItem>\r\n          <FormItem label=\"部门名称\" prop=\"departmentName\">\r\n            <Input v-model=\"formItem.departmentName\" placeholder=\"请输入内容\"></Input>\r\n          </FormItem>\r\n          <FormItem label=\"状态\">\r\n            <RadioGroup v-model=\"formItem.status\" type=\"button\">\r\n              <Radio label=\"0\">正常</Radio>\r\n              <Radio label=\"1\">关闭</Radio>\r\n            </RadioGroup>\r\n          </FormItem>\r\n          <FormItem>\r\n            <Button v-if=\"hasAuthority('departmentAdd') && clickType === 'add'\" @click=\"handleSubmit('add')\" :loading=\"saving\" type=\"primary\">添加</Button>\r\n            <Button v-if=\"hasAuthority('departmentEdit') && clickType === 'edit'\" @click=\"handleSubmit('edit')\" :loading=\"saving\" type=\"primary\">保存</Button>\r\n            <Button v-if=\"hasAuthority('departmentEdit') || hasAuthority('departmentAdd')\" @click=\"setEnabled(true)\" style=\"margin-left: 8px\">重置</Button>\r\n          </FormItem>\r\n        </Form>\r\n      </Card>\r\n      <Spin :fix=\"true\" v-if=\"deLoading\"/>\r\n      <!-- 公司表单 -->\r\n      <Card :shadow=\"true\" v-if=\"this.cd === false\">\r\n        <div class=\"search-con search-con-top\">\r\n          <ButtonGroup>\r\n            <Button type=\"primary\" :disabled=\"!hasAuthority('companyAdd')\" @click=\"setClickType(false, 'add')\">添加\r\n            </Button>\r\n            <Button type=\"primary\" :disabled=\"!(comItem.id && hasAuthority('companyDel'))\" @click=\"confirmModal = true\">删除\r\n            </Button>\r\n          </ButtonGroup>\r\n          <Modal v-model=\"confirmModal\" title=\"提示\" @on-ok=\"handleComRemove\">\r\n            确定删除,公司【{{comItem.companyName}}】吗?是否继续?\r\n          </Modal>\r\n        </div>\r\n        <Form ref=\"comForm\" :model=\"comItem\" :rules=\"comItemRules\" :label-width=\"80\">\r\n          <FormItem label=\"公司名称\" prop=\"companyName\">\r\n            <Input v-model=\"comItem.companyName\" placeholder=\"请输入内容\"></Input>\r\n          </FormItem>\r\n          <FormItem label=\"简称\" prop=\"companyNameSimple\">\r\n            <Input v-model=\"comItem.companyNameSimple\" placeholder=\"请输入内容\"></Input>\r\n          </FormItem>\r\n          <FormItem label=\"英文名称\" prop=\"companyNameEn\">\r\n            <Input v-model=\"comItem.companyNameEn\" placeholder=\"请输入内容\"></Input>\r\n          </FormItem>\r\n          <FormItem label=\"状态\">\r\n            <RadioGroup v-model=\"comItem.status\" type=\"button\">\r\n              <Radio label=\"0\">正常</Radio>\r\n              <Radio label=\"1\">关闭</Radio>\r\n            </RadioGroup>\r\n          </FormItem>\r\n          <FormItem>\r\n            <Button v-if=\"hasAuthority('companyAdd') && clickType === 'add'\" @click=\"handleComSubmit('add')\" :loading=\"saving\" type=\"primary\">添加</Button>\r\n            <Button v-if=\"hasAuthority('companyEdit') && clickType === 'edit'\" @click=\"handleComSubmit('edit')\" :loading=\"saving\" type=\"primary\">保存</Button>\r\n            <Button v-if=\"hasAuthority('companyAdd') || hasAuthority('companyEdit')\" @click=\"setEnabled(false)\" style=\"margin-left: 8px\">重置</Button>\r\n          </FormItem>\r\n        </Form>\r\n      </Card>\r\n      </Col>\r\n    </Row>\r\n  </div>\r\n</template>\r\n<script>\r\nimport {listConvertTree} from '@/libs/util'\r\nimport Department from '@/api/base/department'\r\nimport {getAll}  from '@/api/base/user'\r\nimport Company from '@/api/system/company_1'\r\n\r\nexport default {\r\n  name: 'companyDepartment',\r\n  data() {\r\n    return {\r\n      clickType: 'add', //当前操作类型\r\n      confirmModal: false,\r\n      saving: false,\r\n      visible: false,\r\n      cd: false,\r\n      companyList: [],\r\n      userList: [],\r\n      selectTreeData: [{\r\n        id: '0',\r\n        parentId: '0',\r\n        departmentName: '无'\r\n      }],\r\n      formItemRules: {\r\n        companyId: [\r\n          { required: true, message: '请选择所属公司', trigger: 'change' }\r\n        ],\r\n        parentId: [\r\n          { required: true, message: '请选择上级部门', trigger: 'change' }\r\n        ],\r\n        departmentName: [\r\n          { required: true, message: '部门名称不能为空', trigger: 'blur' }\r\n        ]\r\n      },\r\n      comItemRules: {\r\n        companyName: [\r\n          { required: true, message: '公司名称不能为空', trigger: 'blur' }\r\n        ]\r\n      },\r\n      formItem: {\r\n        id: '',\r\n        companyId: '',\r\n        status: 1,\r\n        parentId: '0',\r\n        managerId:0,\r\n        priority: 0,\r\n        departmentName: ''\r\n      },\r\n      positionLoading: false,\r\n      comItem: {\r\n        id: '',\r\n        parentId: '',\r\n        companyName: '',\r\n        companyNameSimple: '',\r\n        companyNameEn: '',\r\n        status: ''\r\n      },\r\n      infoItem: {\r\n        companyId: ''\r\n      },\r\n      departmentColumns: [{\r\n          title: '部门名称',\r\n          key: 'departmentName',\r\n          minWidth: '200px'\r\n        },\r\n        {\r\n          title: '状态',\r\n          key: 'status',\r\n          type: 'template',\r\n          minWidth: '100px',\r\n          template: 'status'\r\n        }\r\n      ],\r\n      companyColumns: [{\r\n          title: '公司名称',\r\n          key: 'companyName',\r\n          minWidth: '200px'\r\n        },\r\n        {\r\n          title: '状态',\r\n          key: 'status',\r\n          type: 'template',\r\n          minWidth: '100px',\r\n          template: 'status'\r\n        }\r\n      ],\r\n      departmentList: [],\r\n      deLoading: false\r\n    }\r\n  },\r\n  methods: {\r\n    treeSelectNormalizer(node) {\r\n      return {\r\n        id: node.id,\r\n        label: node.departmentName,\r\n        children: node.children\r\n      }\r\n    },\r\n    setSelectTree(data) {\r\n      this.selectTreeData = data\r\n    },\r\n    setEnabled(enabled) {\r\n      if (enabled) {\r\n        this.handleReset()\r\n      } else {\r\n        this.handleComReset()\r\n      }\r\n    },\r\n    rowDepartmentClick(data) {\r\n      this.clickType = 'edit';\r\n      if (this.cd === false) {\r\n        this.$refs['comForm'].resetFields()\r\n      }\r\n      this.cd = true\r\n      this.formItem.parentId = undefined;\r\n      this.deLoading = true;\r\n      this.change(data.row.companyId, data.row.id, data.row.parentId, (success) => {\r\n        if (success === true) {\r\n          if (data) {\r\n            this.formItem = Object.assign({}, data.row)\r\n          }\r\n          this.formItem.status = this.formItem.status + ''\r\n        }\r\n      })\r\n    },\r\n    rowCompanyClick(data) {\r\n      this.clickType = 'edit';\r\n      if (this.cd === false) {\r\n        this.$refs['comForm'].resetFields()\r\n      }\r\n      this.cd = false\r\n      if (data) {\r\n        this.comItem = Object.assign({}, data.row)\r\n        this.comItem.status = this.comItem.status + ''\r\n      }\r\n      this.infoItem.companyId = data.row.id\r\n      this.handleSearch()\r\n    },\r\n    handleReset() {\r\n      this.formItem = {\r\n        id: '',\r\n        companyId: '',\r\n        status: '0',\r\n        parentId: '0',\r\n        priority: 0,\r\n        departmentName: ''\r\n      }\r\n      //this.$refs['menuForm'].resetFields()\r\n      this.saving = false\r\n    },\r\n    handleComReset() {\r\n      this.comItem = {\r\n        id: '',\r\n        parentId: '',\r\n        companyName: '',\r\n        companyNameSimple: '',\r\n        companyNameEn: '',\r\n        status: '0'\r\n      }\r\n      this.$refs['comForm'].resetFields()\r\n      this.saving = false\r\n    },\r\n    handleSubmit(type) {\r\n      this.clickType = type;\r\n      this.$refs['menuForm'].validate((valid) => {\r\n        if (valid) {\r\n          this.saving = true\r\n          if (this.clickType === 'edit') {\r\n            Department.edit(this.formItem).then(res => {\r\n              if (res[\"code\"] === 0) {\r\n                this.$Message.success('保存成功')\r\n              }\r\n              this.handleSearch()\r\n            }).finally(() => {\r\n              this.saving = false\r\n            })\r\n          } else if(this.clickType === 'add'){\r\n            Department.add(this.formItem).then(res => {\r\n              if (res[\"code\"] === 0) {\r\n                this.$Message.success('保存成功')\r\n              }\r\n              this.handleSearch()\r\n            }).finally(() => {\r\n              this.saving = false\r\n            })\r\n          }\r\n        }\r\n      })\r\n    },\r\n    handleComSubmit(type) {\r\n      this.clickType = type;\r\n      this.$refs['comForm'].validate((valid) => {\r\n        if (valid) {\r\n          this.saving = true\r\n          if (this.clickType === 'edit') {\r\n            Company.edit(this.comItem).then(res => {\r\n              if (res[\"code\"] === 0) {\r\n                this.$Message.success('保存成功')\r\n              }\r\n              this.handleCompany()\r\n            }).finally(() => {\r\n              this.saving = false\r\n            })\r\n          } else if(this.clickType === 'add'){\r\n            Company.add(this.comItem).then(res => {\r\n              if (res[\"code\"] === 0) {\r\n                this.$Message.success('保存成功')\r\n              }\r\n              this.handleCompany()\r\n            }).finally(() => {\r\n              this.saving = false\r\n            })\r\n          }\r\n        }\r\n      })\r\n    },\r\n    setClickType(reset,type) {\r\n      this.clickType = type;\r\n      this.setEnabled(reset);\r\n    },\r\n    handleRemove() {\r\n      Department.remove(this.formItem.id).then(res => {\r\n        this.handleReset()\r\n        this.handleSearch()\r\n        if (res[\"code\"] === 0) {\r\n          this.$Message.success('删除成功')\r\n        }\r\n      })\r\n    },\r\n    handleComRemove() {\r\n      Company.remove(this.comItem.id).then(res => {\r\n        this.handleComReset()\r\n        this.handleCompany()\r\n        if (res[\"code\"] === 0) {\r\n          this.$Message.success('删除成功')\r\n        }\r\n      })\r\n    },\r\n    handleCompany() {\r\n      Company.getAll().then(res => {\r\n        this.companyList = res.data;\r\n        this.companyList.push({\r\n          id: 0,\r\n          companyName: '全部公司',\r\n          status: 0\r\n        })\r\n      })\r\n    },\r\n    change(item, id, pid, callback) {\r\n      Department.getByCompanyId(item).then(res => {\r\n        let opt = {\r\n          primaryKey: 'id',\r\n          parentKey: 'parentId',\r\n          startPid: '0'\r\n        }\r\n        let response = res.data;\r\n        response.map((item, index) => {\r\n          if (item.id === id) { response.splice(index, 1) }\r\n        })\r\n        this.selectTreeData = listConvertTree(response, opt)\r\n        this.selectTreeData.push({\r\n          id: '0',\r\n          parentId: '0',\r\n          departmentName: '无'\r\n        })\r\n        if(callback) callback(true);\r\n      }).finally(()=>{\r\n        this.deLoading = false;\r\n      })\r\n    },\r\n    handleSearch() {\r\n      Department.getByCompanyId(this.infoItem.companyId).then(res => {\r\n        let opt = {\r\n          primaryKey: 'id',\r\n          parentKey: 'parentId',\r\n          startPid: '0'\r\n        }\r\n        this.departmentList = listConvertTree(this.uniqueData(res.data, 'id'), opt)\r\n        this.setSelectTree(this.departmentList)\r\n      })\r\n    },\r\n    uniqueData (arr = [], field) { // 根据field字段对对象数组去重\r\n      const fieldArr = [];\r\n      const result = [];\r\n      for(const item of arr) {\r\n        if(!fieldArr.includes(item[field])) {\r\n          result.push(item);\r\n          fieldArr.push(item[field]);\r\n        }\r\n      }\r\n      return result;\r\n\r\n  },\r\n  getPosition(){//获取负责人列表\r\n      this.positionLoading =true;\r\n      getAll().then(res=>{\r\n        if(res&&res[\"code\"] === 0){\r\n          this.userList = res.data || [];\r\n        }\r\n      }).catch(()=>{\r\n        this.userList = [];\r\n      }).finally(()=>{\r\n        this.positionLoading = false;\r\n      })\r\n    }\r\n  },\r\n  mounted: function() {\r\n    this.handleSearch()\r\n    this.handleCompany()\r\n    this.getPosition()\r\n  }\r\n}\r\n\r\n</script>\r\n"]}]}