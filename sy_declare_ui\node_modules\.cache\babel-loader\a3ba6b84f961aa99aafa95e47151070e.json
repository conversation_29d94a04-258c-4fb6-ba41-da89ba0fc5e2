{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\api\\base\\action.js", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\api\\base\\action.js", "mtime": 1752737748397}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\.babelrc", "mtime": 1752737748316}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC9saWJzL3JlcXVlc3QnOwp2YXIgYWN0aW9uUGF0aCA9ICIvYmFzZS9hY3Rpb24iOwoKLyoqDQogKiDojrflj5boj5zljZXkuIvmk43kvZzliJfooagNCiAqIEBwYXJhbSBtZW51SWQNCiAqLwp2YXIgbGlzdEFjdGlvbiA9IGZ1bmN0aW9uIGxpc3RBY3Rpb24obWVudUlkKSB7CiAgdmFyIHBhcmFtcyA9IHsKICAgIG1lbnVJZDogbWVudUlkCiAgfTsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6IGFjdGlvblBhdGggKyAnL2xpc3RBY3Rpb24nLAogICAgcGFyYW1zOiBwYXJhbXMsCiAgICBtZXRob2Q6ICdnZXQnCiAgfSk7Cn07CgovKioNCiAqIOa3u+WKoOaVsOaNrg0KICovCnZhciBhZGQgPSBmdW5jdGlvbiBhZGQoZGF0YSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogYWN0aW9uUGF0aCArICcvYWRkQWN0aW9uJywKICAgIGRhdGE6IGRhdGEsCiAgICBtZXRob2Q6ICdwb3N0JwogIH0pOwp9OwovKioNCiAqIOabtOaWsOaVsOaNrg0KICovCnZhciBlZGl0ID0gZnVuY3Rpb24gZWRpdChkYXRhKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiBhY3Rpb25QYXRoICsgJy9lZGl0QWN0aW9uJywKICAgIGRhdGE6IGRhdGEsCiAgICBtZXRob2Q6ICdwb3N0JwogIH0pOwp9OwovKioNCiAqIOabtOaWsOaVsOaNrg0KICovCnZhciByZW1vdmUgPSBmdW5jdGlvbiByZW1vdmUoaWQpIHsKICB2YXIgZGF0YSA9IHsKICAgIGlkOiBpZAogIH07CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiBhY3Rpb25QYXRoICsgJy9yZW1vdmVBY3Rpb24nLAogICAgZGF0YTogZGF0YSwKICAgIG1ldGhvZDogJ3Bvc3QnCiAgfSk7Cn07CmV4cG9ydCBkZWZhdWx0IHsKICBsaXN0QWN0aW9uOiBsaXN0QWN0aW9uLAogIGFkZDogYWRkLAogIGVkaXQ6IGVkaXQsCiAgcmVtb3ZlOiByZW1vdmUKfTs="}, {"version": 3, "names": ["request", "actionPath", "listAction", "menuId", "params", "url", "method", "add", "data", "edit", "remove", "id"], "sources": ["D:/Users/<USER>/Desktop/dev/sy_declare_ui/src/api/base/action.js"], "sourcesContent": ["import request from '@/libs/request'\r\n\r\nconst actionPath = \"/base/action\";\r\n\r\n/**\r\n * 获取菜单下操作列表\r\n * @param menuId\r\n */\r\nconst listAction = (menuId) => {\r\n    const params = { menuId: menuId }\r\n    return request({\r\n        url: actionPath+'/listAction',\r\n        params,\r\n        method: 'get'\r\n    })\r\n}\r\n\r\n/**\r\n * 添加数据\r\n */\r\nconst add = (data) => {\r\n    return request({\r\n        url: actionPath + '/addAction',\r\n        data,\r\n        method: 'post'\r\n    })\r\n}\r\n/**\r\n * 更新数据\r\n */\r\nconst edit = (data) => {\r\n    return request({\r\n        url: actionPath + '/editAction',\r\n        data,\r\n        method: 'post'\r\n    })\r\n}\r\n/**\r\n * 更新数据\r\n */\r\nconst remove = (id) => {\r\n    const data = {\r\n        id: id\r\n    }\r\n    return request({\r\n        url: actionPath + '/removeAction',\r\n        data,\r\n        method: 'post'\r\n    })\r\n}\r\nexport default {\r\n    listAction,\r\n    add,\r\n    edit,\r\n    remove\r\n}\r\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,gBAAgB;AAEpC,IAAMC,UAAU,GAAG,cAAc;;AAEjC;AACA;AACA;AACA;AACA,IAAMC,UAAU,GAAG,SAAbA,UAAUA,CAAIC,MAAM,EAAK;EAC3B,IAAMC,MAAM,GAAG;IAAED,MAAM,EAAEA;EAAO,CAAC;EACjC,OAAOH,OAAO,CAAC;IACXK,GAAG,EAAEJ,UAAU,GAAC,aAAa;IAC7BG,MAAM,EAANA,MAAM;IACNE,MAAM,EAAE;EACZ,CAAC,CAAC;AACN,CAAC;;AAED;AACA;AACA;AACA,IAAMC,GAAG,GAAG,SAANA,GAAGA,CAAIC,IAAI,EAAK;EAClB,OAAOR,OAAO,CAAC;IACXK,GAAG,EAAEJ,UAAU,GAAG,YAAY;IAC9BO,IAAI,EAAJA,IAAI;IACJF,MAAM,EAAE;EACZ,CAAC,CAAC;AACN,CAAC;AACD;AACA;AACA;AACA,IAAMG,IAAI,GAAG,SAAPA,IAAIA,CAAID,IAAI,EAAK;EACnB,OAAOR,OAAO,CAAC;IACXK,GAAG,EAAEJ,UAAU,GAAG,aAAa;IAC/BO,IAAI,EAAJA,IAAI;IACJF,MAAM,EAAE;EACZ,CAAC,CAAC;AACN,CAAC;AACD;AACA;AACA;AACA,IAAMI,MAAM,GAAG,SAATA,MAAMA,CAAIC,EAAE,EAAK;EACnB,IAAMH,IAAI,GAAG;IACTG,EAAE,EAAEA;EACR,CAAC;EACD,OAAOX,OAAO,CAAC;IACXK,GAAG,EAAEJ,UAAU,GAAG,eAAe;IACjCO,IAAI,EAAJA,IAAI;IACJF,MAAM,EAAE;EACZ,CAAC,CAAC;AACN,CAAC;AACD,eAAe;EACXJ,UAAU,EAAVA,UAAU;EACVK,GAAG,EAAHA,GAAG;EACHE,IAAI,EAAJA,IAAI;EACJC,MAAM,EAANA;AACJ,CAAC"}]}