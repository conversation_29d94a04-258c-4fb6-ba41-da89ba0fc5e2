{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\base\\gateway\\logs\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\base\\gateway\\logs\\index.vue", "mtime": 1752737748511}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAsCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/view/module/base/gateway/logs", "sourcesContent": ["<template>\r\n  <div>\r\n    <Card :shadow=\"true\">\r\n      <Form ref=\"searchFormRef\" class=\"searchForm\" :model=\"searchForm\" inline>\r\n        <FormItem prop=\"path\">\r\n          <Input type=\"text\" v-model=\"searchForm.path\" placeholder=\"请输入请求路径\"/>\r\n        </FormItem>\r\n        <FormItem prop=\"ip\">\r\n          <Input type=\"text\" v-model=\"searchForm.ip\" placeholder=\"请输入IP\"/>\r\n        </FormItem>\r\n        <FormItem prop=\"serviceId\">\r\n          <Input type=\"text\" v-model=\"searchForm.serviceId\" placeholder=\"请输入服务名\"/>\r\n        </FormItem>\r\n        <FormItem>\r\n          <Button type=\"primary\" @click=\"handleSearch(1)\">查询</Button>&nbsp;\r\n          <Button @click=\"handleResetForm()\">重置</Button>\r\n        </FormItem>\r\n      </Form>\r\n      <Table :border=\"true\" :max-height=\"autoTableHeight($refs.autoTableRef)\" ref=\"autoTableRef\" :columns=\"columns\" :data=\"data\" :loading=\"loading\" :page-size-opts=\"pageSizeOpts\">\r\n        <template v-slot:httpStatus=\"{ row }\">\r\n          <Badge v-if=\"row['httpStatus']==='200'\" status=\"success\"/>\r\n          <Badge v-else status=\"error\"/>\r\n          <span>{{row['httpStatus']}}</span>\r\n        </template>\r\n        <template v-slot:params=\"{row,index}\">\r\n          <Poptip :word-wrap=\"true\" width=\"200\" trigger=\"hover\" :content=\"row['params']\">\r\n            <div  v-copytext=\"row['params']\" style=\"width:100px; overflow: hidden; text-overflow:ellipsis; white-space: nowrap;\">{{row['params']}}</div>\r\n          </Poptip>\r\n        </template>\r\n      </Table>\r\n      <Page :transfer=\"true\" size=\"small\" :total=\"pageInfo.total\" :current=\"pageInfo.page\" :page-size=\"pageInfo.limit\"\r\n            :show-elevator=\"true\" :show-sizer=\"true\" :show-total=\"true\"\r\n            @on-change=\"handlePage\" @on-page-size-change='handlePageSize'></Page>\r\n    </Card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  import gatewayLog from '@/api/base/gateway/gatewayLog'\r\n  import {readUserAgent} from '@/libs/util'\r\n  import { autoTableHeight } from '@/libs/tools.js'\r\n  import Api from \"@/api/base/gateway/api\";\r\n  export default {\r\n    name: 'gatewayLogs',\r\n    data () {\r\n      return {\r\n        autoTableHeight,\r\n        drawer: false,\r\n        currentRow: {},\r\n        pageSizeOpts:[20,50,100],\r\n        loading: false,\r\n        searchForm:{\r\n          path: '',\r\n          ip: '',\r\n          serviceId: ''},\r\n        pageInfo: {\r\n          total: 0,\r\n          page: 1,\r\n          limit: 20,\r\n        },\r\n        columns: [\r\n          {\r\n            title: '日志模块',\r\n            width: 180,\r\n            align: 'center',\r\n            key: 'logModule'\r\n          },\r\n          {\r\n            title: '操作类型',\r\n            width: 100,\r\n            align: 'center',\r\n            key: 'logType'\r\n          },\r\n          {\r\n            title: '请求地址',\r\n            key: 'path',\r\n            width: 320\r\n          },\r\n          {\r\n            title: '请求参数',\r\n            align: 'center',\r\n            key: 'params',\r\n            slot:'params',\r\n            width: 220\r\n          },\r\n          {\r\n            title: '接口名称',\r\n            align: 'center',\r\n            key: 'apiName',\r\n            width: 220\r\n          },\r\n          {\r\n            title: '请求方式',\r\n            key: 'method',\r\n            align: 'center',\r\n            width: 120,\r\n            filters: [\r\n              {\r\n                label: 'POST',\r\n                value: 0\r\n              },\r\n              {\r\n                label: 'GET',\r\n                value: 1\r\n              },\r\n              {\r\n                label: 'DELETE',\r\n                value: 2\r\n              },\r\n              {\r\n                label: 'OPTIONS',\r\n                value: 3\r\n              },\r\n              {\r\n                label: 'PATCH',\r\n                value: 4\r\n              }\r\n            ],\r\n            filterMultiple: false,\r\n            filterMethod (value, row) {\r\n              if (value === 0) {\r\n                return row.method === 'POST'\r\n              } else if (value === 1) {\r\n                return row.method === 'GET'\r\n              } else if (value === 2) {\r\n                return row.method === 'DELETE'\r\n              } else if (value === 3) {\r\n                return row.method === 'OPTIONS'\r\n              } else if (value === 4) {\r\n                return row.method === 'PATCH'\r\n              }\r\n            }\r\n          },\r\n          {\r\n            title: 'IP',\r\n            key: 'ip',\r\n            width: 150\r\n          },\r\n          {\r\n            title: '区域',\r\n            key: 'region',\r\n            minWidth: 100\r\n          },\r\n          {\r\n            title: '终端',\r\n            width: 80,\r\n            render: (h, params) => {\r\n              return h('div', readUserAgent(params.row.userAgent).terminal)\r\n            }\r\n          },\r\n          {\r\n            title: '浏览器',\r\n            width: 100,\r\n            render: (h, params) => {\r\n              return h('div', readUserAgent(params.row.userAgent).browser)\r\n            }\r\n          },\r\n          {\r\n            title: '服务名',\r\n            key: 'serviceId',\r\n            width: 160\r\n          },\r\n          {\r\n            title: '用户名',\r\n            align: 'center',\r\n            width: 120,\r\n            key: 'userName'\r\n          },\r\n          {\r\n            title: '响应状态',\r\n            key: 'httpStatus',\r\n            slot: 'httpStatus',\r\n            width: 100\r\n          },\r\n          {\r\n            title: '耗时',\r\n            key: 'useTime',\r\n            render: (h, params) => {\r\n              return h('div', (params.row['useTime'] ? params.row['useTime'] : 0) + ' ms')\r\n            },\r\n\r\n            width: 100\r\n          },\r\n          {\r\n            title: '请求时间',\r\n            key: 'requestTime',\r\n            width: 160\r\n          }\r\n        ],\r\n        data: []\r\n      }\r\n    },\r\n    methods: {\r\n      handleSearch () {\r\n        this.loading = true\r\n        gatewayLog.listPage({...this.pageInfo,...this.searchForm}).then(res => {\r\n          this.data = res.data.records\r\n          this.pageInfo.total = parseInt(res.data.total)\r\n        }).finally(() => {\r\n          this.loading = false\r\n        })\r\n      },\r\n      handleResetForm () {\r\n        this.pageInfo = {total: 0, page: 1, limit: 20,}\r\n        this.$refs['searchFormRef'].resetFields()\r\n      },\r\n      handlePage (current) {\r\n        this.pageInfo.page = current\r\n        this.handleSearch()\r\n      },\r\n      handlePageSize (size) {\r\n        this.pageInfo.limit = size\r\n        this.handleSearch()\r\n      }\r\n    },\r\n    mounted: function () {\r\n      this.handleSearch()\r\n    }\r\n  }\r\n</script>\r\n"]}]}