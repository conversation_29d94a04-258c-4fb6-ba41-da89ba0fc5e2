{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\base\\menus\\menu-action.vue?vue&type=style&index=0&id=cbf51e86&lang=less&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\base\\menus\\menu-action.vue", "mtime": 1752737748512}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1752744819993}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQoubWVudS1hY3Rpb24tY29udGFpbmVyew0KICAuaXZ1LXRhYmxlLXdyYXBwZXIgew0KICAgICAgLml2dS10YWJsZS1jZWxsew0KICAgICAgICBwYWRkaW5nLWxlZnQ6IDhweDsNCiAgICAgICAgcGFkZGluZy1yaWdodDogNHB4Ow0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["menu-action.vue"], "names": [], "mappings": ";AAwTA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "menu-action.vue", "sourceRoot": "src/view/module/base/menus", "sourcesContent": ["<template>\r\n  <div class=\"menu-action-container\">\r\n    <div class=\"search-con search-con-top\">\r\n      <ButtonGroup>\r\n        <Button\r\n          :disabled=\"!(value.id && value.id!=='0' && !value.hasChild)\"\r\n          class=\"search-btn\" type=\"primary\" @click=\"handleModal()\">\r\n          <span>添加功能按钮</span>\r\n        </Button>\r\n      </ButtonGroup>\r\n    </div>\r\n    <Alert type=\"info\" :show-icon=\"true\">请绑定相关接口资源。否则请求网关服务器将提示<code>\"权限不足,拒绝访问!\"</code></Alert>\r\n    <Table :border=\"true\" :columns=\"columns\" :data=\"data\" :loading=\"loading\" :max-height=\"690\">\r\n      <template v-slot:status=\"{ row }\">\r\n        <Badge v-for=\"v in statusOps\" v-if=\"v.key === row.status\"\r\n               :status=\"v.key === 0?'success':'error'\" v-bind:key=\"v.key\"></Badge>\r\n        <span v-copytext=\"row.actionName\">{{row.actionName}}</span>\r\n      </template>\r\n      <template v-slot:action=\"{ row }\">\r\n        <a @click=\"handleModal(row)\"  v-if=\"hasAuthority('menuActionEdit')\">编辑</a> &nbsp;\r\n        <a @click=\"handleModal(row,forms[1])\" v-if=\"hasAuthority('menuActionSet')\">接口权限</a> &nbsp;\r\n        <a @click=\"handleRemove(row)\" v-if=\"hasAuthority('menuActionDel')\">删除</a>\r\n      </template>\r\n    </Table>\r\n    <Modal v-model=\"modalVisible\"\r\n           :title=\"modalTitle\"\r\n           width=\"40\"\r\n           @on-cancel=\"handleReset\">\r\n      <div>\r\n        <Form ref=\"form1\" v-show=\"current==='form1'\" :model=\"formItem\" :rules=\"formItemRules\" :label-width=\"100\">\r\n          <FormItem label=\"上级菜单\">\r\n            <Input :disabled=\"true\" v-model=\"value.menuName\"></Input>\r\n          </FormItem>\r\n          <FormItem label=\"功能标识\" prop=\"actionCode\">\r\n            <Input v-model=\"formItem.actionCode\" placeholder=\"请输入内容\"></Input>\r\n            <span>菜单标识+自定义标识.默认后缀：View、Edit</span>\r\n          </FormItem>\r\n          <FormItem label=\"功能名称\" prop=\"actionName\">\r\n            <Input v-model=\"formItem.actionName\" placeholder=\"请输入内容\"></Input>\r\n          </FormItem>\r\n          <FormItem label=\"优先级\">\r\n            <InputNumber v-model=\"formItem.priority\"></InputNumber>\r\n          </FormItem>\r\n          <FormItem label=\"状态\">\r\n            <RadioGroup v-model=\"formItem.status\" type=\"button\">\r\n              <Radio v-for=\"v in statusOps\" :label=\"v.key\" v-if=\"v.key !== -1\" v-bind:key=\"v.key\">{{ v.name }}</Radio>\r\n            </RadioGroup>\r\n          </FormItem>\r\n          <FormItem label=\"描述\">\r\n            <Input v-model=\"formItem.actionDesc\" type=\"textarea\" placeholder=\"请输入内容\"></Input>\r\n          </FormItem>\r\n        </Form>\r\n        <Form ref=\"form2\" v-show=\"current==='form2'\" :model=\"formItem\" :rules=\"formItemRules\">\r\n          <FormItem prop=\"authorities\">\r\n            <Transfer\r\n              :data=\"selectApis\"\r\n              :list-style=\"{width: '45%',height: '480px'}\"\r\n              :titles=\"['选择接口', '已选择接口']\"\r\n              :render-format=\"transferRender\"\r\n              :target-keys=\"formItem.authorityIds\"\r\n              @on-change=\"handleTransferChange\"\r\n              :filterable=\"true\">\r\n            </Transfer>\r\n          </FormItem>\r\n        </Form>\r\n        <div class=\"drawer-footer\">\r\n          <Button type=\"default\" @click=\"handleReset\">取消</Button>&nbsp;\r\n          <Button type=\"primary\" @click=\"handleSubmit\" :loading=\"saving\">保存</Button>\r\n        </div>\r\n      </div>\r\n    </Modal>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Action from '@/api/base/action';\r\nimport Authority from '@/api/system/authority_1';\r\nimport Common from \"@/api/basic/common\";\r\nexport default {\r\n    name: 'MenuAction',\r\n    props: {\r\n      value: Object\r\n    },\r\n    data () {\r\n      const validateEn = (rule, value, callback) => {\r\n        let reg = /^[_a-zA-Z0-9]+$/\r\n        if (value === '') {\r\n          callback(new Error('功能标识不能为空'))\r\n        } else if (value !== '' && !reg.test(value)) {\r\n          callback(new Error('只允许字母、数字、下划线'))\r\n        } else {\r\n          callback()\r\n        }\r\n      }\r\n      return {\r\n        statusOps: Common.statusOps,\r\n        modalVisible: false,\r\n        saving: false,\r\n        loading: false,\r\n        current: 'form1',\r\n        forms: [\r\n          'form1',\r\n          'form2'\r\n        ],\r\n        modalTitle: '',\r\n        confirmModal: false,\r\n        selectApis: [],\r\n        formItemRules: {\r\n          actionCode: [\r\n            {required: true, validator: validateEn, message: '功能编码不能为空', trigger: 'blur'}\r\n          ],\r\n          actionName: [\r\n            {required: true, message: '功能名称不能为空', trigger: 'blur'}\r\n          ]\r\n        },\r\n        formItem: {\r\n          id: '',\r\n          actionCode: '',\r\n          actionName: '',\r\n          authorityIds: [],\r\n          status: 0,\r\n          menuId: '',\r\n          priority: 0,\r\n          actionDesc: ''\r\n        },\r\n        columns: [\r\n          {\r\n            title: '功能名称',\r\n            slot: 'status',\r\n            width: 150\r\n          },\r\n          {\r\n            title: '功能编码',\r\n            key: 'actionCode',\r\n            align: 'center',\r\n            render: (_, { row }) => <div v-copytext={row.actionCode} style=\"text-align:left\">{row.actionCode}</div>\r\n          },\r\n          {\r\n            title: '操作',\r\n            slot: 'action',\r\n            fixed: 'right',\r\n            align: 'center',\r\n            width: 150\r\n          }\r\n        ],\r\n        data: []\r\n      }\r\n    },\r\n    methods: {\r\n      handleModal (data, step) {\r\n        if (data) {\r\n          this.formItem = Object.assign({}, this.formItem, data)\r\n        }\r\n        if (!step) {\r\n          step = this.forms[0]\r\n        }\r\n        if (step === this.forms[0]) {\r\n          this.modalTitle = data ? '编辑功能 - ' + this.value.menuName + ' > ' + data.actionName : '添加功能 - ' + this.value.menuName\r\n          this.modalVisible = true\r\n          this.formItem.actionCode = this.formItem.id ? this.formItem.actionCode : this.value.menuCode\r\n        }\r\n        if (step === this.forms[1]) {\r\n          this.modalTitle = data ? '接口授权 - ' + this.value.menuName + ' > ' + data.actionName : '接口授权'\r\n          this.handleLoadActionApi(this.formItem.id)\r\n        }\r\n        this.current = step\r\n      },\r\n      handleReset () {\r\n        this.formItem = {\r\n          id: '',\r\n          actionCode: '',\r\n          actionName: '',\r\n          authorityIds: [],\r\n          status: 0,\r\n          priority: 0,\r\n          actionDesc: ''\r\n        }\r\n        //重置验证\r\n        // this.forms.map(form => {\r\n        //   this.$refs[form].resetFields()\r\n        // })\r\n        // this.current = this.forms[0]\r\n        this.modalVisible = false\r\n        this.saving = false\r\n      },\r\n      handleSubmit () {\r\n        if (this.current === this.forms[0]) {\r\n          this.$refs[this.current].validate((valid) => {\r\n            if (valid) {\r\n              this.saving = true\r\n              if (this.formItem.id) {\r\n                Action.edit(this.formItem).then(res => {\r\n                  this.handleReset()\r\n                  this.handleSearch()\r\n                  if (res['code'] === 0) {\r\n                    this.$Message.success('保存成功')\r\n                  }\r\n                }).finally(() => {\r\n                  this.saving = false\r\n                })\r\n              } else {\r\n                Action.add(this.formItem).then(res => {\r\n                  this.handleReset()\r\n                  this.handleSearch()\r\n                  if (res['code'] === 0) {\r\n                    this.$Message.success('保存成功')\r\n                  }\r\n                }).finally(() => {\r\n                  this.saving = false\r\n                })\r\n              }\r\n            }\r\n          })\r\n        }\r\n        if (this.current === this.forms[1]) {\r\n          this.$refs[this.current].validate((valid) => {\r\n            if (valid) {\r\n              this.saving = true\r\n              Authority.grantAuthorityForAction({\r\n                actionId: this.formItem.id,\r\n                authorityIds: this.formItem.authorityIds\r\n              }).then(res => {\r\n                this.handleReset()\r\n                this.handleSearch()\r\n                if (res['code'] === 0) {\r\n                  this.$Message.success('绑定成功')\r\n                }\r\n              }).finally(() => {\r\n                this.saving = false\r\n              })\r\n            }\r\n          })\r\n        }\r\n\r\n      },\r\n      handleSearch () {\r\n        if (!this.value || !this.value.id) {\r\n          return\r\n        }\r\n        this.formItem.menuId = this.value.id\r\n        this.loading = true\r\n        Action.listAction(this.formItem.menuId).then(res => {\r\n          this.data = res.data\r\n        }).finally(() => {\r\n          this.loading = false\r\n        })\r\n      },\r\n      handleRemove (data) {\r\n        this.$Modal.confirm({\r\n          title: '确定删除吗？',\r\n          onOk: () => {\r\n            Action.remove(data.id).then(res => {\r\n              this.handleSearch()\r\n              if (res['code'] === 0) {\r\n                this.pageInfo.page = 1\r\n                this.$Message.success('删除成功')\r\n              }\r\n            })\r\n          }\r\n        })\r\n      },\r\n      handleLoadActionApi (id) {\r\n        if (!id) {\r\n          return\r\n        }\r\n        const that = this\r\n        const p1 = Authority.getAllApi(0)\r\n        const p2 = Authority.getAuthorityForAction(id)\r\n        Promise.all([p1, p2]).then(function (values) {\r\n          let res1 = values[0]\r\n          let res2 = values[1]\r\n          if (res1.code === 0) {\r\n            res1.data.map(item => {\r\n              item.key = item.authorityId\r\n              item.label = `${item.prefix.replace('/**', '')}${item.path} - ${item.apiName}`\r\n              item.disabled = item.path === '/**'\r\n            })\r\n            that.selectApis = res1.data\r\n          }\r\n          if (res2.code === 0) {\r\n            const result = []\r\n            res2.data.map(item => {\r\n              if (!result.includes(item.authorityId)) {\r\n                result.push(item.authorityId)\r\n              }\r\n            })\r\n            that.formItem.authorityIds = result\r\n          }\r\n          that.modalVisible = true\r\n        })\r\n      },\r\n      transferRender (item) {\r\n        return `<span  title=\"${item.label}\">${item.label}</span>`\r\n      },\r\n      handleTransferChange (newTargetKeys) {\r\n        if (newTargetKeys.indexOf('1') !== -1) {\r\n          this.formItem.authorityIds = ['1']\r\n        } else {\r\n          this.formItem.authorityIds = newTargetKeys\r\n        }\r\n      },\r\n    },\r\n    watch: {\r\n      value () {\r\n        this.handleSearch()\r\n      }\r\n    },\r\n    mounted: function () {\r\n    }\r\n  }\r\n</script>\r\n<style lang=\"less\">\r\n.menu-action-container{\r\n  .ivu-table-wrapper {\r\n      .ivu-table-cell{\r\n        padding-left: 8px;\r\n        padding-right: 4px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}