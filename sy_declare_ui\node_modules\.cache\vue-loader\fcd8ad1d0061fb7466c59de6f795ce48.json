{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\custom\\base\\vatNo\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\custom\\base\\vatNo\\index.vue", "mtime": 1754360258641}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAyFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/view/module/custom/base/vatNo", "sourcesContent": ["<!--\r\n@create date 2020-12-14\r\n<AUTHOR>\r\n@desc 清关税号维护表\r\n-->\r\n<template>\r\n  <Card>\r\n    <Form ref=\"formInfo\" :model=\"formInfo\" inline :label-width=\"60\">\r\n      <FormItem prop=\"number1\" label=\"税号\">\r\n        <Input v-model=\"formInfo.vatNo\" placeholder=\"请输入税号搜索\"/>\r\n      </FormItem>\r\n      <FormItem prop=\"shopIds\" label=\"店铺\">\r\n        <ShopSelect v-model=\"formInfo.shopIds\" placeholder=\"店铺\" width=\"205px\" valueField=\"id\"/>\r\n      </FormItem>\r\n      <FormItem label=\"目的国\" prop=\"country\">\r\n        <Select v-model=\"formInfo.country\" filterable clearable placeholder=\"请选择目的国\" style=\"width:150px\">\r\n          <Option v-for=\"(item,index) in countryList\" :value=\"item['two_code']\" :key=\"index\">{{ item['name_cn'] }}\r\n          </Option>\r\n        </Select>\r\n      </FormItem>\r\n      <FormItem>\r\n        <Button type=\"primary\" @click=\"handleSearch()\">查询</Button>\r\n        <Button @click=\"handleReset()\" style=\"margin-left:10px\">重置</Button>\r\n      </FormItem>\r\n    </Form>\r\n    <div style=\"margin-bottom:10px\">\r\n      <div style=\"float:left\">\r\n        <Upload ref=\"uploadFileRef\" name=\"importFile\" :action=\"importURl\" :max-size=\"10240\"\r\n                :on-success=\"handleImportSuccess\" :format=\"['xls', 'xlsx']\" :show-upload-list=\"false\"\r\n                :on-format-error=\"handleImportFormatError\"\r\n                :on-error=\"handleImportError\" :headers=\"loginInfo\" :on-exceeded-size=\"handleMaxSize\">\r\n          <Button class=\"search-btn\" type=\"primary\">导入</Button>\r\n        </Upload>\r\n      </div>\r\n      <Button style=\"margin-left:10px;\" @click=\"addVatNo\" :loading=\"saving\">新增</Button>\r\n      <Button @click=\"templateExport\" style=\"margin-left:10px;\" :loading=\"saving\">导入模板</Button>\r\n      <Button @click=\"vatNoExport\" style=\"margin-left:10px;\" :loading=\"saving\">导出</Button>\r\n    </div>\r\n    <Table :border=\"true\" :columns=\"columns\" :data=\"data\" :loading=\"loading\">\r\n      <template v-slot:country=\"{row}\">\r\n        <span v-for=\"item in countryList\" v-if=\"item['two_code'] === row.country\">{{ item['name_cn'] }}</span>\r\n      </template>\r\n      <template v-slot:shopName=\"{row}\">\r\n        <span v-for=\"item in shopList\" v-if=\"item['id'] === row.shopId\">{{ item['name'] }}</span>\r\n      </template>\r\n      <template v-slot:action=\"{row}\">\r\n        <Button size=\"small\" type=\"info\" @click=\"editVatNo(row)\" style=\"margin:0 2px\">编辑</Button>\r\n        <Button size=\"small\" type=\"info\" @click=\"delVatNo(row)\" style=\"margin:0 2px\">删除</Button>\r\n      </template>\r\n    </Table>\r\n    <Page :total=\"pageInfo.total \" :current=\"pageInfo.page\" :page-size=\"pageInfo.limit\" :show-elevator=\"true\"\r\n          :show-sizer=\"true\"\r\n          :show-total=\"true\" @on-change=\"handlePage\" @on-page-size-change='handlePageSize' :transfer=\"true\"></Page>\r\n    <Modal :width=\"530\" v-model=\"modal\" :title=\"title\" @on-cancel=\"cancelForm\">\r\n      <Spin :fix=\"true\" v-if=\"spinShow\">加载中...</Spin>\r\n      <Form ref=\"vatNoForm\" :model=\"vatNoForm\" inline label-position=\"right\" :label-width=\"110\">\r\n        <FormItem label=\"店铺\" prop=\"shopId\" :rules=\"{required: true, message: '不能为空', trigger: 'change'}\">\r\n          <Select v-model=\"vatNoForm.shopId\" filterable clearable placeholder=\"网店\" class=\"widthClass\">\r\n            <Option v-for=\"(item,index) in shopList\" :value=\"item['id']\" :key=\"index\">{{ item['name'] }}\r\n            </Option>\r\n          </Select>\r\n        </FormItem>\r\n        <FormItem label=\"目的国\" prop=\"country\" :rules=\"{required: true, message: '不能为空', trigger: 'blur'}\">\r\n          <Select v-model=\"vatNoForm.country\" filterable clearable placeholder=\"请选择目的国\" class=\"widthClass\">\r\n            <Option v-for=\"(item,index) in countryList\" :value=\"item['two_code']\" :key=\"index\">{{ item['name_cn'] }}\r\n            </Option>\r\n          </Select>\r\n        </FormItem>\r\n        <FormItem label=\"税号\" prop=\"vatNo\" :rules=\"{required: true, message: '不能为空', trigger: 'blur'}\">\r\n          <Input v-model.trim=\"vatNoForm.vatNo\" :readonly=\"disabled\" placeholder=\"请输入\" class=\"widthClass\"/>\r\n        </FormItem>\r\n        <FormItem label=\"税号公司\" prop=\"company\">\r\n          <Input v-model.trim=\"vatNoForm.company\" :readonly=\"disabled\" placeholder=\"请输入\" class=\"widthClass\"/>\r\n        </FormItem>\r\n        <FormItem label=\"地址\" prop=\"address\">\r\n          <Input v-model.trim=\"vatNoForm.address\" :readonly=\"disabled\" placeholder=\"请输入\" class=\"widthClass\"/>\r\n        </FormItem>\r\n        <FormItem label=\"EORI\" prop=\"eori\">\r\n          <Input v-model.trim=\"vatNoForm.eori\" :readonly=\"disabled\" placeholder=\"请输入\" class=\"widthClass\"/>\r\n        </FormItem>\r\n      </Form>\r\n      <div slot=\"footer\">\r\n        <Button type=\"primary\" :disabled=\"disabled\" :loading=\"saving\" @click=\"saveVatNo\">保存</Button>\r\n        <Button @click=\"cancelForm\">取消</Button>\r\n      </div>\r\n    </Modal>\r\n  </Card>\r\n</template>\r\n<script>\r\nimport CommonApi from \"@/api/base/commonApi\";\r\nimport VatNo from \"@/api/custom/vatNo\";\r\nimport UploadImg from \"@/view/module/custom/company/common/uploadImg.vue\";\r\nimport Shop from \"@/api/basf/shop\";\r\nimport ShopSelect from \"@/components/shopSelect/index.vue\";\r\nimport {getToken, getUrl} from \"@/libs/util\";\r\nimport ClearanceLink from \"@/api/custom/clearanceLink\";\r\nexport default {\r\n  components: {UploadImg,ShopSelect},\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      saving: false,\r\n      modal: false,\r\n      spinShow: false,\r\n      disabled: false,\r\n      title: '',\r\n      formInfo: {\r\n        vatNo: '',\r\n        shopIds: '',\r\n        country: ''\r\n      },\r\n      pageInfo: {\r\n        total: 0,\r\n        page: 1,\r\n        limit: 10\r\n      },\r\n      loginInfo: {\r\n        Accept: 'application/json,text/plain, */*, */*',\r\n        mode: 'cors',\r\n        Authorization: 'Bearer ' + getToken()\r\n      },\r\n      importURl: getUrl() + \"/base/vatNo/importFile\",\r\n      vatNoForm: {eori:null,shopId:null,country:null,company:null,vatNo:null,address:null},\r\n      countryList: [],\r\n      shopList:[],\r\n      data: [],\r\n      columns: [\r\n        {title: '网店名称', key: 'shopId', minWidth: 100, align: 'center',slot:'shopName'},\r\n        {title: '目的国', key: 'country', minWidth: 100, align: 'center',slot:'country'},\r\n        {title: '税号', key: 'vatNo', minWidth: 100, align: 'center'},\r\n        {title: '税号公司', key: 'company', minWidth: 150, align: 'center'},\r\n        {title: '地址', key: 'address', minWidth: 300, align: 'center'},\r\n        {title: 'EORI', key: 'eori', minWidth: 100, align: 'center'},\r\n        {title: '操作', key: 'action', width: 150, align: 'center', slot: 'action'}],\r\n    }\r\n  },\r\n  mounted() {\r\n    this.handleSearch();\r\n    this.getCountryList();\r\n    this.getAllShop();\r\n  },\r\n  methods: {\r\n    getAllShop() {\r\n      Shop.getAll().then(res => {\r\n        if (res && res['code'] === 0) {\r\n          this.shopList = res.data;\r\n        }\r\n      })\r\n    },\r\n    handleSearch() {\r\n      this.loading = true;\r\n      let params = {...this.formInfo, ...this.pageInfo}\r\n      const getStr = value => value && Array.isArray(value) ? value.join(\",\") : undefined;\r\n      params['shopIds'] = getStr(params['shopIds']);\r\n      VatNo.listPage(params).then(res => {\r\n        if (res['code'] === 0) {\r\n          this.data = res.data.records;\r\n          this.pageInfo.total = Number(res.data.total)\r\n        }\r\n      }).finally(() => {\r\n        this.loading = false;\r\n      })\r\n    },\r\n    handleReset() {\r\n      this.$refs['formInfo'].resetFields();\r\n    },\r\n    delVatNo(row) {\r\n      this.$Modal.confirm({\r\n        title: '提示',\r\n        content: '您确认要删除这条数据吗？',\r\n        onOk: () => {\r\n          VatNo.remove({id: row.id}).then(res => {\r\n            if (res['code'] === 0) {\r\n              this.$Message.success('删除成功!');\r\n              this.handleSearch();\r\n            }\r\n          })\r\n        },\r\n      })\r\n    },\r\n    editVatNo(row) {\r\n      this.title = \"添加清关税号\";\r\n      this.modal = true;\r\n      this.disabled = false;\r\n      this.resetForm();\r\n      this.vatNoForm = Object.assign({},row);\r\n    },\r\n    addVatNo() {\r\n      this.title = \"添加清关税号\";\r\n      this.modal = true;\r\n      this.disabled = false;\r\n      this.resetForm();\r\n    },\r\n    saveVatNo() {\r\n      this.$refs['vatNoForm'].validate((valid) => {\r\n        if (valid) {\r\n          this.saving = true;\r\n          VatNo.saveVatNo(this.vatNoForm).then(res => {\r\n            if (res['code'] === 0) {\r\n              this.saving = false;\r\n              this.$Message.success('保存成功!');\r\n              this.resetForm();\r\n              this.modal = false;\r\n              this.handleSearch();\r\n            }\r\n          }).finally(() => {\r\n            this.saving = false;\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleImportSuccess(res) {\r\n      this.$refs['uploadFileRef'].clearFiles();\r\n      if (res['code'] === 0) {\r\n        this.$Message.success('导入成功');\r\n        this.handleSearch();\r\n      } else {\r\n        this.$Message.warning(res['message']);\r\n      }\r\n    },\r\n    handleImportFormatError(file) {\r\n      //格式验证失败的钩子\r\n      this.$Modal.error({\r\n        title: '文件格式不正确',\r\n        content: '文件 ' + file.name + '格式不正确，支持上传的文件类型：xls,xlsx',\r\n        okText: '确认'\r\n      });\r\n    },\r\n    handleImportError(err, file) {\r\n      this.$Message.warning(file.message);\r\n    },\r\n    handleMaxSize() {\r\n      this.$Message.warning('图片大小不能超过2M.');\r\n    },\r\n    templateExport(){\r\n      this.loading = true;\r\n      VatNo.downloadTemplate({\"fileName\":\"清关税号导入模板.xls\"}, () => {\r\n        this.loading = false\r\n      })\r\n    },\r\n    vatNoExport() {\r\n      this.loading = true;\r\n      let params = {...this.formInfo}\r\n      const getStr = value => value && Array.isArray(value) ? value.join(\",\") : undefined;\r\n      params['shopIds'] = getStr(params['shopIds']);\r\n      params['fileName'] = \"清关税号_\" + new Date().getExportFormat() + \".xls\";\r\n      this.loading = true;\r\n      VatNo.download(params, () => {\r\n        this.loading = false\r\n      })\r\n    },\r\n    //获取目的国家\r\n    getCountryList() {\r\n      CommonApi.ListDictionaryValueBy(\"base_country\").then(res => {\r\n        if (res && res['code'] === 0) {\r\n          let data = res['data']\r\n          if (data) {\r\n            this.countryList = data.map(item => JSON.parse(item.value));\r\n          }\r\n        }\r\n      })\r\n    },\r\n    handlePage(page) {\r\n      this.pageInfo.page = page;\r\n      this.handleSearch();\r\n    },\r\n    handlePageSize(size) {\r\n      this.pageInfo.page = 1;\r\n      this.pageInfo.limit = size;\r\n      this.handleSearch();\r\n    },\r\n    cancelForm() {\r\n      this.modal = false;\r\n      this.resetForm();\r\n    },\r\n    resetForm() {\r\n      this.$refs['vatNoForm'].resetFields();\r\n      this.vatNoForm = {};\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\">\r\n.widthClass {\r\n  width: 350px\r\n}\r\n</style>\r\n"]}]}