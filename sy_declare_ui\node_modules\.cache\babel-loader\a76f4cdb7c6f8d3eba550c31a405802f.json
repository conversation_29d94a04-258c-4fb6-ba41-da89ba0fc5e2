{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\base\\role\\PersonSelectEx.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\base\\role\\PersonSelectEx.vue", "mtime": 1752737748513}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\.babelrc", "mtime": 1752737748316}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "CommonApi", "name", "props", "visible", "type", "Boolean", "onCancel", "Function", "groupName", "String", "multiple", "<PERSON><PERSON><PERSON><PERSON>", "default", "value", "Array", "data", "<PERSON><PERSON><PERSON><PERSON>", "allCompany", "selectedCompany", "getItem", "itemFather", "selectedList", "breadList", "searchValue", "undefined", "multipleY", "myValue", "computed", "multipleR", "mounted", "_this", "getDictionaryValueBy", "then", "res", "itemValue", "JSON", "parse", "roleCodes", "depIds", "isSingle", "companyIds", "company", "split", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "departmentIds", "isArray", "length", "filter", "v", "indexOf", "companyId", "$emit", "personArr", "map", "userId", "id", "nick<PERSON><PERSON>", "getAllCompany", "allComIds", "includes", "watch", "userIds", "newIds", "methods", "isCompany", "firstItem", "companyName", "handleFather", "checkGetItem", "pop", "handleChildren", "item", "push", "children", "checkboxChange", "checked", "$forceUpdate", "setChecked", "arr", "_iterator", "_createForOfIteratorHelper", "_step", "s", "n", "done", "disabled", "err", "e", "f", "getName", "text", "slice", "deleteList", "deleteItem", "find", "delRepeat", "selectAll", "_this2", "for<PERSON>ach", "getIndeterminate", "selectedItems", "getAllChecked", "getDisabled", "disabledItems", "getNewChecked", "_this3", "handleSearch", "toCompanyList", "selectedIds", "_iterator2", "_step2", "check<PERSON><PERSON><PERSON><PERSON>", "arguments", "flag", "_iterator3", "_step3", "indeterminate", "makeDataByFields", "_this4", "fields", "index", "current<PERSON><PERSON>", "getTreeData", "field", "treeArr", "myArr1", "myArr2", "uniqueArr", "x", "obj", "_iterator4", "_step4", "concat", "sortData", "dataArr", "treeData", "_iterator5", "_step5", "every", "changeMenu", "currentIndex", "selectCompany", "getSelectedItems", "idsArr", "getIds", "_iterator6", "_step6", "onVisibleChange", "_this5", "setTimeout", "onOk", "<PERSON><PERSON><PERSON><PERSON>", "getFullName", "parent1Name", "parent2Name", "parent3Name", "companyItem", "companyNameSimple", "result"], "sources": ["src/view/module/base/role/PersonSelectEx.vue"], "sourcesContent": ["<!--\r\n@create date 2020-03-06\r\n@desc 角色管理部门--特殊人员选择控件\r\n-->\r\n<template>\r\n  <Row class=\"personSelect\" :gutter=\"24\">\r\n    <Col span=\"13\" class=\"leftArea\">\r\n      <h3>选择：</h3>\r\n      <Card>\r\n        <div class=\"headWrapper\">\r\n          <div style=\"padding-right: 4px\">\r\n            <Input :search=\"true\" placeholder=\"输入人员姓名搜索\" @on-search=\"handleSearch\" v-model=\"searchValue\"/>\r\n          </div>\r\n          <Breadcrumb separator=\">\">\r\n            <BreadcrumbItem>\r\n              <a @click=\"toCompanyList\">选择人员</a>\r\n            </BreadcrumbItem>\r\n            <BreadcrumbItem v-if=\"selectedCompany.id\">\r\n              <a @click=\"selectCompany(selectedCompany)\">{{\r\n                  getName(selectedCompany.companyNameSimple || selectedCompany.companyName)\r\n                }}</a>\r\n            </BreadcrumbItem>\r\n            <BreadcrumbItem v-for=\"(bread, index) in breadList\" :key=\"index\">\r\n              <span v-if=\"index === breadList.length - 1\" :title=\"bread.name\">{{ getName(bread.name) }}</span>\r\n              <a v-else :title=\"bread.name\" @click=\"changeMenu(index)\">{{ getName(bread.name) }}</a>\r\n            </BreadcrumbItem>\r\n          </Breadcrumb>\r\n        </div>\r\n        <!-- 人员列表 -->\r\n        <div class=\"personList\">\r\n          <div v-if=\"multipleR === true && !isCompany() && getItem.length > 0 && getItem.every(v=>!v.children)\">\r\n            <Checkbox :value=\"getAllChecked()\" :indeterminate=\"getIndeterminate()\" @on-change=\"selectAll\">全选\r\n            </Checkbox>\r\n          </div>\r\n          <ul v-if=\"isCompany()\" style=\"list-style: none;margin-top: 5px\">\r\n            <li v-for=\"item in getItem\" :key=\"item.id\" style=\"font-size: 12px;position: relative\">\r\n              <span>\r\n                <Icon type=\"ios-folder-open\"/>\r\n                <span class=\"name\" :title=\"item.name\">{{ item.name }}</span>\r\n              </span>\r\n              <span style=\"position: absolute;top:6px; right:0; font-size: 12px\">\r\n                <a @click=\"selectCompany(item)\">\r\n                  <Icon type=\"ios-redo-outline\"/>下级\r\n                </a>\r\n              </span>\r\n            </li>\r\n          </ul>\r\n          <ul v-else style=\"list-style: none;margin-top: 5px\">\r\n            <li v-if=\"getItem.length === 0\">暂无数据</li>\r\n            <li v-for=\"item in getItem\" :key=\"item.id\" style=\"font-size: 16px;position: relative\">\r\n              <Checkbox v-if=\"!item.children\" :value=\"item.checked\"\r\n                        @on-change=\"checked => {checkboxChange(checked, item);}\"\r\n                        :disabled=\"!multipleR? (item.children && item.children.length > 0) ||(selectedList.length > 0 &&selectedList[0].id !== item.id): false\">\r\n                <Icon type=\"ios-folder-open\" v-if=\"item.children\"/>\r\n                <Icon type=\"md-person\" v-else/>\r\n                <div class=\"name\" :title=\"item.name\">{{ item.name }}</div>\r\n              </Checkbox>\r\n              <span v-else style=\"font-size: 12px\">\r\n                <Icon type=\"ios-folder-open\" v-if=\"item.children\"/>\r\n                <Icon type=\"md-person\" v-else/>\r\n                <div class=\"name\" :title=\"item.name\">{{ item.name }}</div>\r\n              </span>\r\n              <span style=\"position: absolute;top:6px; right:0; font-size: 12px\" v-if=\"item.children\">\r\n                <a @click=\"handleChildren(item, getItem)\">\r\n                  <Icon type=\"ios-redo-outline\"/>下级\r\n                </a>\r\n              </span>\r\n            </li>\r\n          </ul>\r\n        </div>\r\n      </Card>\r\n    </Col>\r\n\r\n    <Col span=\"11\" class=\"rightArea\">\r\n      <h3>已选{{ selectedList.length > 0 ? `（${selectedList.length}）` : '' }}：\r\n        <a v-if=\"multipleR === true\" @click=\"() => {selectedList = [];$emit('clearSelected');checkGetItem();}\">清空</a>\r\n      </h3>\r\n      <Card>\r\n        <ul style=\"list-style: none\">\r\n          <li v-for=\"(person, index) in selectedList\" :key=\"index\">\r\n            <Icon type=\"ios-folder-open\" v-if=\"person.children\"/>\r\n            <Icon type=\"md-person\" v-else/>\r\n            <div class=\"name\" :title=\"getFullName(person)\">{{ person.name }}</div>\r\n            <Icon class=\"icon\" type=\"ios-close-circle-outline\" title=\"删除\" @click=\"deleteList(person)\"/>\r\n          </li>\r\n        </ul>\r\n      </Card>\r\n    </Col>\r\n  </Row>\r\n</template>\r\n\r\n<script>\r\nimport personApi from '@/api/globalApi/personApi'; //-api\r\nimport CommonApi from '@/api/base/commonApi'; //-api\r\nexport default {\r\n  name: \"PersonSelectEx\",\r\n  props: {\r\n    visible: {\r\n      type: Boolean\r\n    },\r\n    onCancel: {\r\n      type: Function\r\n    },\r\n    groupName: {\r\n      type: String\r\n    },\r\n    multiple: {},\r\n    isQuery: {\r\n      // 是否为查询框\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    value: {\r\n      type: Array\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      allPersons: [], //所有人员\r\n      allCompany: [], //所有公司\r\n      selectedCompany: {}, //当前选中的公司信息\r\n      getItem: [], //存放传过来的子级\r\n      itemFather: [], //存放父级的数据，供返回上一级使用\r\n      selectedList: [],\r\n      breadList: [], //面包屑导航列表\r\n      //部门数据\r\n      searchValue: undefined, //搜索文字\r\n      multipleY: false,\r\n      myValue: [],\r\n    };\r\n  },\r\n  computed: {\r\n    multipleR() {\r\n      const {multiple, multipleY} = this;\r\n      return multiple !== undefined ? multiple : multipleY;\r\n    }\r\n  },\r\n  mounted() {\r\n    const {groupName} = this;\r\n    //字典获取\r\n    CommonApi.getDictionaryValueBy(\"person_select_group\", groupName).then(res => {\r\n      if (res['code'] === 0 && res.data) {\r\n        const itemValue = JSON.parse(res.data);\r\n        const {roleCodes, depIds, isSingle} = itemValue; // 取到角色标志和部门id集合\r\n        const companyIds = itemValue.company ? itemValue.company.split(\",\") : []; // 配置的company ids\r\n        this.multipleY = isSingle === \"false\";\r\n        // 获取所有人员\r\n        personApi.getAllPersons({roleCodes, departmentIds: depIds}).then(allPersons => {\r\n          if (allPersons && Array.isArray(allPersons)) {\r\n            this.allPersons = companyIds.length > 0 ? allPersons.filter(v => companyIds.indexOf(v.companyId) >= 0) : allPersons;\r\n            this.$emit(\"setSelectInfo\", {\r\n              personArr: this.allPersons.map(v => ({userId: v.id, nickName: v.name})),\r\n              multiple: this.multipleY\r\n            });\r\n            // 获取所有公司\r\n            CommonApi.getAllCompany().then(res => {\r\n              if (res && Array.isArray(res.data)) {\r\n                const allComIds = this.allPersons.map(v => v.companyId);\r\n                const allCompany = res.data.filter(v => {\r\n                      v[\"name\"] = v[\"companyName\"];\r\n                      return allComIds.includes(v.id);\r\n                    }\r\n                );\r\n                this.allCompany = allCompany;\r\n                this.getItem = allCompany;\r\n              }\r\n            });\r\n          }\r\n        });\r\n      }\r\n    });\r\n  },\r\n  watch: {\r\n    value(userIds) { //父组件传入\r\n      const {allPersons} = this;\r\n      this.selectedList = allPersons.filter(v => userIds.includes(v.id));\r\n      this.myValue = userIds;\r\n    },\r\n    myValue(newIds) {\r\n      this.$emit(\"input\", newIds);\r\n    }\r\n  },\r\n  methods: {\r\n    isCompany() {\r\n      const {getItem} = this;\r\n      const firstItem = getItem[0] || {};\r\n      return firstItem.companyName;\r\n    },\r\n    //返回上级\r\n    handleFather() {\r\n      this.getItem = this.itemFather[this.itemFather.length - 1];\r\n      this.checkGetItem();\r\n      //返回上级后去掉最后一个\r\n      this.breadList.pop();\r\n      this.itemFather.pop();\r\n    },\r\n    //进入下级\r\n    handleChildren(item, itemFather) {\r\n      this.itemFather.push(itemFather);\r\n      this.breadList.push(item);\r\n      //如果存在子级，则根据子级的子级的选中状态确定自己的状态\r\n      if (item.children) {\r\n        this.getItem = item.children;\r\n      }\r\n      this.checkGetItem();\r\n    },\r\n\r\n    // checkbox改变时 || 将右边的已选项与左边的getItem关联\r\n    checkboxChange(checked, item) {\r\n      item.checked = checked;\r\n      if (checked === true) {\r\n        this.selectedList.push(item);\r\n      } else {\r\n        this.selectedList = this.selectedList.filter(v => v.id !== item.id);\r\n      }\r\n      this.myValue = this.selectedList.map(v => v.id);\r\n      this.$forceUpdate();\r\n    },\r\n\r\n    //递归勾选或取消所有子节点， checked为true代表勾选，false代表取消\r\n    setChecked(arr, checked) {\r\n      if (arr && Array.isArray(arr)) {\r\n        for (const item of arr) {\r\n          item.checked = checked;\r\n          item.disabled = checked; //checked为true父节点被勾选，子节点禁用，不能被取消\r\n          this.setChecked(item.children, checked);\r\n        }\r\n      }\r\n    },\r\n\r\n    //多余文字省略号显示\r\n    getName(text) {\r\n      if (text && text.length > 10) {\r\n        return text.slice(0, 10) + \"...\";\r\n      } else return text;\r\n    },\r\n\r\n    //删除已选中项\r\n    deleteList(item) {\r\n      const deleteItem = this.getItem.find(v => v.id === item.id) || {};\r\n      if (deleteItem.children) {\r\n        this.setChecked(deleteItem.children, false);\r\n      }\r\n      this.selectedList = this.selectedList.filter(v => v.id !== item.id);\r\n      this.myValue = this.selectedList.map(v => v.id);\r\n      this.checkGetItem();\r\n    },\r\n    //过滤方法：过滤重复的元素\r\n    delRepeat(checked, item) {\r\n      if (checked === true) {\r\n        //选中\r\n        this.selectedList.push(item);\r\n      } else {\r\n        //取消选择\r\n        this.selectedList = this.selectedList.filter(v => v.id !== item.id);\r\n      }\r\n      this.myValue = this.selectedList.map(v => v.id);\r\n    },\r\n    //点击全选\r\n    selectAll(checked) {\r\n      this.getItem.forEach(item => {\r\n        item.checked = checked;\r\n        //封装起点\r\n        this.delRepeat(checked, item);\r\n      });\r\n      this.$forceUpdate();\r\n    },\r\n\r\n    //是否半选状态\r\n    getIndeterminate() {\r\n      const {getItem} = this;\r\n      const selectedItems = getItem.filter(v => v.checked === true);\r\n      return !(\r\n          selectedItems.length === getItem.length || selectedItems.length === 0\r\n      );\r\n    },\r\n    //是否全选\r\n    getAllChecked() {\r\n      const {getItem} = this;\r\n      const selectedItems = getItem.filter(v => v.checked === true);\r\n      return (\r\n          selectedItems.length > 0 && selectedItems.length === getItem.length\r\n      );\r\n    },\r\n    //是否禁用全选\r\n    getDisabled() {\r\n      const {getItem} = this;\r\n      const disabledItems = getItem.filter(v => v.disabled === true);\r\n      return disabledItems.length !== 0;\r\n    },\r\n    //根据已选列表递归确定getItem及其子级的checked\r\n    getNewChecked(getItem) {\r\n      getItem.forEach(v => {\r\n        this.selectedList.forEach(s => {\r\n          if (s.id === v.id) {\r\n            v.checked = true;\r\n            this.setChecked(v.children, true);\r\n          }\r\n          if (v.children) {\r\n            this.getNewChecked(v.children);\r\n          }\r\n        });\r\n      });\r\n    },\r\n    //搜索回调\r\n    handleSearch() {\r\n      const {searchValue, allPersons} = this;\r\n      if (!searchValue) {\r\n        this.toCompanyList();\r\n        return;\r\n      }\r\n      this.selectedCompany = {};\r\n      this.itemFather = [];\r\n      this.breadList = [];\r\n      this.getItem = allPersons.filter(v => v.name.includes(searchValue));\r\n      this.checkGetItem();\r\n    },\r\n\r\n    //设置当前展示项的选中状态\r\n    checkGetItem() {\r\n      const {getItem, selectedList} = this;\r\n      const selectedIds = selectedList.map(v => v.id);\r\n      for (const item of getItem) {\r\n        if (selectedIds.includes(item.id)) {\r\n          item.checked = true;\r\n        } else if (!selectedIds.includes(item.id)) {\r\n          item.checked = false;\r\n        }\r\n      }\r\n      this.$forceUpdate(); //强制重新渲染\r\n    },\r\n\r\n    //检测子节点中是否有被选中的，有则设置选中状态并且返回true给父节点设置半选状态\r\n    checkChildren(arr = [], selectedIds) {\r\n      let flag = false;\r\n      for (const item of arr) {\r\n        if (selectedIds.includes(item.id)) {\r\n          item.checked = true;\r\n          item.indeterminate = false;\r\n          flag = true;\r\n        } else if (item.children) {\r\n          item.checked = false;\r\n          flag = this.checkChildren(item.children, selectedIds);\r\n        } else item.checked = false;\r\n      }\r\n      return flag;\r\n    },\r\n\r\n    //根据字段名递归构造树形数据\r\n    makeDataByFields(data, fields = [\"parent1\", \"parent2\", \"parent3\"], index = 0) {\r\n      const currentField = fields[index];\r\n      const getTreeData = (arr = [], field) => {\r\n        const treeArr = [];\r\n        const myArr1 = arr.filter(v => v[field]);\r\n        const myArr2 = arr.filter(v => !v[field]); //过滤掉不存在子项的数据\r\n        myArr1.forEach(item => {\r\n          const uniqueArr = treeArr.map(x => x.id); //获取唯一标志数组用于比较是否存在某个标志的对象\r\n          if (uniqueArr.indexOf(item[field]) === -1) {\r\n            const obj = {\r\n              name: item[field + \"Name\"],\r\n              id: item[field],\r\n              children: []\r\n            };\r\n            //如果有父级，则放进obj的children里面，最后把obj放进treeArr\r\n            for (const v of myArr1) {\r\n              if (v[field] && v[field] === obj.id) obj.children.push(v);\r\n            }\r\n            treeArr.push(obj);\r\n          }\r\n        });\r\n        return treeArr.concat(myArr2);\r\n      };\r\n\r\n      //自定义函数-对数据进行拼装组合成children形式\r\n      const sortData = dataArr => {\r\n        const treeData = getTreeData(dataArr, currentField);\r\n        for (const every of treeData) {\r\n          if (every.children) {\r\n            if (fields[index + 1])\r\n              every.children = this.makeDataByFields(\r\n                  every.children,\r\n                  fields,\r\n                  index + 1\r\n              ); //递归调用获取重设children\r\n          }\r\n        }\r\n        return treeData;\r\n      };\r\n\r\n      //函数主体部分：\r\n      return sortData(data);\r\n    },\r\n\r\n    changeMenu(currentIndex) {\r\n      this.getItem = this.itemFather[currentIndex + 1];\r\n      this.itemFather = this.itemFather.slice(0, currentIndex + 1);\r\n      this.breadList = this.breadList.slice(0, currentIndex + 1);\r\n    },\r\n\r\n    toCompanyList() {\r\n      //回到公司列表\r\n      this.getItem = this.allCompany;\r\n      this.selectedCompany = {};\r\n      this.itemFather = [];\r\n      this.breadList = [];\r\n    },\r\n\r\n    //选择公司\r\n    selectCompany(selectedCompany = {}) {\r\n      const {allPersons} = this;\r\n      this.selectedCompany = selectedCompany;\r\n      this.getItem = this.makeDataByFields(\r\n          allPersons.filter(v => v.companyId === selectedCompany.id)\r\n      );\r\n      this.itemFather = [];\r\n      this.breadList = [];\r\n      this.checkGetItem();\r\n    },\r\n\r\n    //获取当前选中的人员数组\r\n    getSelectedItems() {\r\n      const {selectedList} = this;\r\n      const idsArr = [];\r\n      const personArr = [];\r\n      const getIds = arr => {\r\n        for (const item of arr) {\r\n          if (item && item.children && item.children.length > 0) {\r\n            getIds(item.children);\r\n          } else if (!idsArr.includes(item.id)) {\r\n            idsArr.push(item.id);\r\n            personArr.push({name: item.name, id: item.id});\r\n          }\r\n        }\r\n      };\r\n      getIds(selectedList);\r\n      return personArr; //返回人员id数组\r\n    },\r\n\r\n    onVisibleChange(visible) {\r\n      //关闭弹窗的回调操作\r\n      if (visible === false) {\r\n        setTimeout(() => {\r\n          this.toCompanyList();\r\n          // this.selectedList = [];\r\n          this.searchValue = undefined;\r\n        }, 300);\r\n      }\r\n    },\r\n\r\n    onOk() {\r\n      //点击确定\r\n      const personArr = this.getSelectedItems();\r\n      this.$emit(\"setPerson\", personArr);\r\n      // this.onCancel();\r\n    },\r\n\r\n    //外部给组件设置默认选中人员\r\n    setDefault(arr = []) {\r\n      this.selectedList = arr;\r\n    },\r\n\r\n    getFullName(item = {}) {\r\n      const {allCompany} = this;\r\n      const {companyId, parent1Name, parent2Name, parent3Name, name} = item;\r\n      if (!name) return '';\r\n      const companyItem = allCompany.find(v => v.id === companyId) || {};\r\n      const companyName = companyItem.companyNameSimple || companyItem.companyName;\r\n      const getName = (arr = []) => {\r\n        let result = '';\r\n        arr.forEach((item, index) => {\r\n          result += item ? ((index === 0 ? '' : ' / ') + item) : '';\r\n        });\r\n        return result;\r\n      };\r\n      return getName([companyName, parent1Name, parent2Name, parent3Name, name]);\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"less\" src=\"./index.less\"/>\r\n"], "mappings": ";;;;;;;;;;;;;AA4FA,OAAAA,SAAA;AACA,OAAAC,SAAA;AACA;EACAC,IAAA;EACAC,KAAA;IACAC,OAAA;MACAC,IAAA,EAAAC;IACA;IACAC,QAAA;MACAF,IAAA,EAAAG;IACA;IACAC,SAAA;MACAJ,IAAA,EAAAK;IACA;IACAC,QAAA;IACAC,OAAA;MACA;MACAP,IAAA,EAAAC,OAAA;MACAO,OAAA;IACA;IACAC,KAAA;MACAT,IAAA,EAAAU;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,UAAA;MAAA;MACAC,UAAA;MAAA;MACAC,eAAA;MAAA;MACAC,OAAA;MAAA;MACAC,UAAA;MAAA;MACAC,YAAA;MACAC,SAAA;MAAA;MACA;MACAC,WAAA,EAAAC,SAAA;MAAA;MACAC,SAAA;MACAC,OAAA;IACA;EACA;EACAC,QAAA;IACAC,SAAA,WAAAA,UAAA;MACA,IAAAlB,QAAA,QAAAA,QAAA;QAAAe,SAAA,QAAAA,SAAA;MACA,OAAAf,QAAA,KAAAc,SAAA,GAAAd,QAAA,GAAAe,SAAA;IACA;EACA;EACAI,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,IAAAtB,SAAA,QAAAA,SAAA;IACA;IACAR,SAAA,CAAA+B,oBAAA,wBAAAvB,SAAA,EAAAwB,IAAA,WAAAC,GAAA;MACA,IAAAA,GAAA,kBAAAA,GAAA,CAAAlB,IAAA;QACA,IAAAmB,SAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAH,GAAA,CAAAlB,IAAA;QACA,IAAAsB,SAAA,GAAAH,SAAA,CAAAG,SAAA;UAAAC,MAAA,GAAAJ,SAAA,CAAAI,MAAA;UAAAC,QAAA,GAAAL,SAAA,CAAAK,QAAA;QACA,IAAAC,UAAA,GAAAN,SAAA,CAAAO,OAAA,GAAAP,SAAA,CAAAO,OAAA,CAAAC,KAAA;QACAZ,KAAA,CAAAL,SAAA,GAAAc,QAAA;QACA;QACAxC,SAAA,CAAA4C,aAAA;UAAAN,SAAA,EAAAA,SAAA;UAAAO,aAAA,EAAAN;QAAA,GAAAN,IAAA,WAAAhB,UAAA;UACA,IAAAA,UAAA,IAAAF,KAAA,CAAA+B,OAAA,CAAA7B,UAAA;YACAc,KAAA,CAAAd,UAAA,GAAAwB,UAAA,CAAAM,MAAA,OAAA9B,UAAA,CAAA+B,MAAA,WAAAC,CAAA;cAAA,OAAAR,UAAA,CAAAS,OAAA,CAAAD,CAAA,CAAAE,SAAA;YAAA,KAAAlC,UAAA;YACAc,KAAA,CAAAqB,KAAA;cACAC,SAAA,EAAAtB,KAAA,CAAAd,UAAA,CAAAqC,GAAA,WAAAL,CAAA;gBAAA;kBAAAM,MAAA,EAAAN,CAAA,CAAAO,EAAA;kBAAAC,QAAA,EAAAR,CAAA,CAAA/C;gBAAA;cAAA;cACAS,QAAA,EAAAoB,KAAA,CAAAL;YACA;YACA;YACAzB,SAAA,CAAAyD,aAAA,GAAAzB,IAAA,WAAAC,GAAA;cACA,IAAAA,GAAA,IAAAnB,KAAA,CAAA+B,OAAA,CAAAZ,GAAA,CAAAlB,IAAA;gBACA,IAAA2C,SAAA,GAAA5B,KAAA,CAAAd,UAAA,CAAAqC,GAAA,WAAAL,CAAA;kBAAA,OAAAA,CAAA,CAAAE,SAAA;gBAAA;gBACA,IAAAjC,UAAA,GAAAgB,GAAA,CAAAlB,IAAA,CAAAgC,MAAA,WAAAC,CAAA;kBACAA,CAAA,WAAAA,CAAA;kBACA,OAAAU,SAAA,CAAAC,QAAA,CAAAX,CAAA,CAAAO,EAAA;gBACA,CACA;gBACAzB,KAAA,CAAAb,UAAA,GAAAA,UAAA;gBACAa,KAAA,CAAAX,OAAA,GAAAF,UAAA;cACA;YACA;UACA;QACA;MACA;IACA;EACA;EACA2C,KAAA;IACA/C,KAAA,WAAAA,MAAAgD,OAAA;MAAA;MACA,IAAA7C,UAAA,QAAAA,UAAA;MACA,KAAAK,YAAA,GAAAL,UAAA,CAAA+B,MAAA,WAAAC,CAAA;QAAA,OAAAa,OAAA,CAAAF,QAAA,CAAAX,CAAA,CAAAO,EAAA;MAAA;MACA,KAAA7B,OAAA,GAAAmC,OAAA;IACA;IACAnC,OAAA,WAAAA,QAAAoC,MAAA;MACA,KAAAX,KAAA,UAAAW,MAAA;IACA;EACA;EACAC,OAAA;IACAC,SAAA,WAAAA,UAAA;MACA,IAAA7C,OAAA,QAAAA,OAAA;MACA,IAAA8C,SAAA,GAAA9C,OAAA;MACA,OAAA8C,SAAA,CAAAC,WAAA;IACA;IACA;IACAC,YAAA,WAAAA,aAAA;MACA,KAAAhD,OAAA,QAAAC,UAAA,MAAAA,UAAA,CAAA0B,MAAA;MACA,KAAAsB,YAAA;MACA;MACA,KAAA9C,SAAA,CAAA+C,GAAA;MACA,KAAAjD,UAAA,CAAAiD,GAAA;IACA;IACA;IACAC,cAAA,WAAAA,eAAAC,IAAA,EAAAnD,UAAA;MACA,KAAAA,UAAA,CAAAoD,IAAA,CAAApD,UAAA;MACA,KAAAE,SAAA,CAAAkD,IAAA,CAAAD,IAAA;MACA;MACA,IAAAA,IAAA,CAAAE,QAAA;QACA,KAAAtD,OAAA,GAAAoD,IAAA,CAAAE,QAAA;MACA;MACA,KAAAL,YAAA;IACA;IAEA;IACAM,cAAA,WAAAA,eAAAC,OAAA,EAAAJ,IAAA;MACAA,IAAA,CAAAI,OAAA,GAAAA,OAAA;MACA,IAAAA,OAAA;QACA,KAAAtD,YAAA,CAAAmD,IAAA,CAAAD,IAAA;MACA;QACA,KAAAlD,YAAA,QAAAA,YAAA,CAAA0B,MAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAO,EAAA,KAAAgB,IAAA,CAAAhB,EAAA;QAAA;MACA;MACA,KAAA7B,OAAA,QAAAL,YAAA,CAAAgC,GAAA,WAAAL,CAAA;QAAA,OAAAA,CAAA,CAAAO,EAAA;MAAA;MACA,KAAAqB,YAAA;IACA;IAEA;IACAC,UAAA,WAAAA,WAAAC,GAAA,EAAAH,OAAA;MACA,IAAAG,GAAA,IAAAhE,KAAA,CAAA+B,OAAA,CAAAiC,GAAA;QAAA,IAAAC,SAAA,GAAAC,0BAAA,CACAF,GAAA;UAAAG,KAAA;QAAA;UAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAI,CAAA,IAAAC,IAAA;YAAA,IAAAb,IAAA,GAAAU,KAAA,CAAApE,KAAA;YACA0D,IAAA,CAAAI,OAAA,GAAAA,OAAA;YACAJ,IAAA,CAAAc,QAAA,GAAAV,OAAA;YACA,KAAAE,UAAA,CAAAN,IAAA,CAAAE,QAAA,EAAAE,OAAA;UACA;QAAA,SAAAW,GAAA;UAAAP,SAAA,CAAAQ,CAAA,CAAAD,GAAA;QAAA;UAAAP,SAAA,CAAAS,CAAA;QAAA;MACA;IACA;IAEA;IACAC,OAAA,WAAAA,QAAAC,IAAA;MACA,IAAAA,IAAA,IAAAA,IAAA,CAAA5C,MAAA;QACA,OAAA4C,IAAA,CAAAC,KAAA;MACA,cAAAD,IAAA;IACA;IAEA;IACAE,UAAA,WAAAA,WAAArB,IAAA;MACA,IAAAsB,UAAA,QAAA1E,OAAA,CAAA2E,IAAA,WAAA9C,CAAA;QAAA,OAAAA,CAAA,CAAAO,EAAA,KAAAgB,IAAA,CAAAhB,EAAA;MAAA;MACA,IAAAsC,UAAA,CAAApB,QAAA;QACA,KAAAI,UAAA,CAAAgB,UAAA,CAAApB,QAAA;MACA;MACA,KAAApD,YAAA,QAAAA,YAAA,CAAA0B,MAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAO,EAAA,KAAAgB,IAAA,CAAAhB,EAAA;MAAA;MACA,KAAA7B,OAAA,QAAAL,YAAA,CAAAgC,GAAA,WAAAL,CAAA;QAAA,OAAAA,CAAA,CAAAO,EAAA;MAAA;MACA,KAAAa,YAAA;IACA;IACA;IACA2B,SAAA,WAAAA,UAAApB,OAAA,EAAAJ,IAAA;MACA,IAAAI,OAAA;QACA;QACA,KAAAtD,YAAA,CAAAmD,IAAA,CAAAD,IAAA;MACA;QACA;QACA,KAAAlD,YAAA,QAAAA,YAAA,CAAA0B,MAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAO,EAAA,KAAAgB,IAAA,CAAAhB,EAAA;QAAA;MACA;MACA,KAAA7B,OAAA,QAAAL,YAAA,CAAAgC,GAAA,WAAAL,CAAA;QAAA,OAAAA,CAAA,CAAAO,EAAA;MAAA;IACA;IACA;IACAyC,SAAA,WAAAA,UAAArB,OAAA;MAAA,IAAAsB,MAAA;MACA,KAAA9E,OAAA,CAAA+E,OAAA,WAAA3B,IAAA;QACAA,IAAA,CAAAI,OAAA,GAAAA,OAAA;QACA;QACAsB,MAAA,CAAAF,SAAA,CAAApB,OAAA,EAAAJ,IAAA;MACA;MACA,KAAAK,YAAA;IACA;IAEA;IACAuB,gBAAA,WAAAA,iBAAA;MACA,IAAAhF,OAAA,QAAAA,OAAA;MACA,IAAAiF,aAAA,GAAAjF,OAAA,CAAA4B,MAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAA2B,OAAA;MAAA;MACA,SACAyB,aAAA,CAAAtD,MAAA,KAAA3B,OAAA,CAAA2B,MAAA,IAAAsD,aAAA,CAAAtD,MAAA,OACA;IACA;IACA;IACAuD,aAAA,WAAAA,cAAA;MACA,IAAAlF,OAAA,QAAAA,OAAA;MACA,IAAAiF,aAAA,GAAAjF,OAAA,CAAA4B,MAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAA2B,OAAA;MAAA;MACA,OACAyB,aAAA,CAAAtD,MAAA,QAAAsD,aAAA,CAAAtD,MAAA,KAAA3B,OAAA,CAAA2B,MAAA;IAEA;IACA;IACAwD,WAAA,WAAAA,YAAA;MACA,IAAAnF,OAAA,QAAAA,OAAA;MACA,IAAAoF,aAAA,GAAApF,OAAA,CAAA4B,MAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAqC,QAAA;MAAA;MACA,OAAAkB,aAAA,CAAAzD,MAAA;IACA;IACA;IACA0D,aAAA,WAAAA,cAAArF,OAAA;MAAA,IAAAsF,MAAA;MACAtF,OAAA,CAAA+E,OAAA,WAAAlD,CAAA;QACAyD,MAAA,CAAApF,YAAA,CAAA6E,OAAA,WAAAhB,CAAA;UACA,IAAAA,CAAA,CAAA3B,EAAA,KAAAP,CAAA,CAAAO,EAAA;YACAP,CAAA,CAAA2B,OAAA;YACA8B,MAAA,CAAA5B,UAAA,CAAA7B,CAAA,CAAAyB,QAAA;UACA;UACA,IAAAzB,CAAA,CAAAyB,QAAA;YACAgC,MAAA,CAAAD,aAAA,CAAAxD,CAAA,CAAAyB,QAAA;UACA;QACA;MACA;IACA;IACA;IACAiC,YAAA,WAAAA,aAAA;MACA,IAAAnF,WAAA,QAAAA,WAAA;QAAAP,UAAA,QAAAA,UAAA;MACA,KAAAO,WAAA;QACA,KAAAoF,aAAA;QACA;MACA;MACA,KAAAzF,eAAA;MACA,KAAAE,UAAA;MACA,KAAAE,SAAA;MACA,KAAAH,OAAA,GAAAH,UAAA,CAAA+B,MAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAA/C,IAAA,CAAA0D,QAAA,CAAApC,WAAA;MAAA;MACA,KAAA6C,YAAA;IACA;IAEA;IACAA,YAAA,WAAAA,aAAA;MACA,IAAAjD,OAAA,QAAAA,OAAA;QAAAE,YAAA,QAAAA,YAAA;MACA,IAAAuF,WAAA,GAAAvF,YAAA,CAAAgC,GAAA,WAAAL,CAAA;QAAA,OAAAA,CAAA,CAAAO,EAAA;MAAA;MAAA,IAAAsD,UAAA,GAAA7B,0BAAA,CACA7D,OAAA;QAAA2F,MAAA;MAAA;QAAA,KAAAD,UAAA,CAAA3B,CAAA,MAAA4B,MAAA,GAAAD,UAAA,CAAA1B,CAAA,IAAAC,IAAA;UAAA,IAAAb,IAAA,GAAAuC,MAAA,CAAAjG,KAAA;UACA,IAAA+F,WAAA,CAAAjD,QAAA,CAAAY,IAAA,CAAAhB,EAAA;YACAgB,IAAA,CAAAI,OAAA;UACA,YAAAiC,WAAA,CAAAjD,QAAA,CAAAY,IAAA,CAAAhB,EAAA;YACAgB,IAAA,CAAAI,OAAA;UACA;QACA;MAAA,SAAAW,GAAA;QAAAuB,UAAA,CAAAtB,CAAA,CAAAD,GAAA;MAAA;QAAAuB,UAAA,CAAArB,CAAA;MAAA;MACA,KAAAZ,YAAA;IACA;IAEA;IACAmC,aAAA,WAAAA,cAAA;MAAA,IAAAjC,GAAA,GAAAkC,SAAA,CAAAlE,MAAA,QAAAkE,SAAA,QAAAxF,SAAA,GAAAwF,SAAA;MAAA,IAAAJ,WAAA,GAAAI,SAAA,CAAAlE,MAAA,OAAAkE,SAAA,MAAAxF,SAAA;MACA,IAAAyF,IAAA;MAAA,IAAAC,UAAA,GAAAlC,0BAAA,CACAF,GAAA;QAAAqC,MAAA;MAAA;QAAA,KAAAD,UAAA,CAAAhC,CAAA,MAAAiC,MAAA,GAAAD,UAAA,CAAA/B,CAAA,IAAAC,IAAA;UAAA,IAAAb,IAAA,GAAA4C,MAAA,CAAAtG,KAAA;UACA,IAAA+F,WAAA,CAAAjD,QAAA,CAAAY,IAAA,CAAAhB,EAAA;YACAgB,IAAA,CAAAI,OAAA;YACAJ,IAAA,CAAA6C,aAAA;YACAH,IAAA;UACA,WAAA1C,IAAA,CAAAE,QAAA;YACAF,IAAA,CAAAI,OAAA;YACAsC,IAAA,QAAAF,aAAA,CAAAxC,IAAA,CAAAE,QAAA,EAAAmC,WAAA;UACA,OAAArC,IAAA,CAAAI,OAAA;QACA;MAAA,SAAAW,GAAA;QAAA4B,UAAA,CAAA3B,CAAA,CAAAD,GAAA;MAAA;QAAA4B,UAAA,CAAA1B,CAAA;MAAA;MACA,OAAAyB,IAAA;IACA;IAEA;IACAI,gBAAA,WAAAA,iBAAAtG,IAAA;MAAA,IAAAuG,MAAA;MAAA,IAAAC,MAAA,GAAAP,SAAA,CAAAlE,MAAA,QAAAkE,SAAA,QAAAxF,SAAA,GAAAwF,SAAA;MAAA,IAAAQ,KAAA,GAAAR,SAAA,CAAAlE,MAAA,QAAAkE,SAAA,QAAAxF,SAAA,GAAAwF,SAAA;MACA,IAAAS,YAAA,GAAAF,MAAA,CAAAC,KAAA;MACA,IAAAE,WAAA,YAAAA,YAAA;QAAA,IAAA5C,GAAA,GAAAkC,SAAA,CAAAlE,MAAA,QAAAkE,SAAA,QAAAxF,SAAA,GAAAwF,SAAA;QAAA,IAAAW,KAAA,GAAAX,SAAA,CAAAlE,MAAA,OAAAkE,SAAA,MAAAxF,SAAA;QACA,IAAAoG,OAAA;QACA,IAAAC,MAAA,GAAA/C,GAAA,CAAA/B,MAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAA2E,KAAA;QAAA;QACA,IAAAG,MAAA,GAAAhD,GAAA,CAAA/B,MAAA,WAAAC,CAAA;UAAA,QAAAA,CAAA,CAAA2E,KAAA;QAAA;QACAE,MAAA,CAAA3B,OAAA,WAAA3B,IAAA;UACA,IAAAwD,SAAA,GAAAH,OAAA,CAAAvE,GAAA,WAAA2E,CAAA;YAAA,OAAAA,CAAA,CAAAzE,EAAA;UAAA;UACA,IAAAwE,SAAA,CAAA9E,OAAA,CAAAsB,IAAA,CAAAoD,KAAA;YACA,IAAAM,GAAA;cACAhI,IAAA,EAAAsE,IAAA,CAAAoD,KAAA;cACApE,EAAA,EAAAgB,IAAA,CAAAoD,KAAA;cACAlD,QAAA;YACA;YACA;YAAA,IAAAyD,UAAA,GAAAlD,0BAAA,CACA6C,MAAA;cAAAM,MAAA;YAAA;cAAA,KAAAD,UAAA,CAAAhD,CAAA,MAAAiD,MAAA,GAAAD,UAAA,CAAA/C,CAAA,IAAAC,IAAA;gBAAA,IAAApC,CAAA,GAAAmF,MAAA,CAAAtH,KAAA;gBACA,IAAAmC,CAAA,CAAA2E,KAAA,KAAA3E,CAAA,CAAA2E,KAAA,MAAAM,GAAA,CAAA1E,EAAA,EAAA0E,GAAA,CAAAxD,QAAA,CAAAD,IAAA,CAAAxB,CAAA;cACA;YAAA,SAAAsC,GAAA;cAAA4C,UAAA,CAAA3C,CAAA,CAAAD,GAAA;YAAA;cAAA4C,UAAA,CAAA1C,CAAA;YAAA;YACAoC,OAAA,CAAApD,IAAA,CAAAyD,GAAA;UACA;QACA;QACA,OAAAL,OAAA,CAAAQ,MAAA,CAAAN,MAAA;MACA;;MAEA;MACA,IAAAO,QAAA,YAAAA,SAAAC,OAAA;QACA,IAAAC,QAAA,GAAAb,WAAA,CAAAY,OAAA,EAAAb,YAAA;QAAA,IAAAe,UAAA,GAAAxD,0BAAA,CACAuD,QAAA;UAAAE,MAAA;QAAA;UAAA,KAAAD,UAAA,CAAAtD,CAAA,MAAAuD,MAAA,GAAAD,UAAA,CAAArD,CAAA,IAAAC,IAAA;YAAA,IAAAsD,KAAA,GAAAD,MAAA,CAAA5H,KAAA;YACA,IAAA6H,KAAA,CAAAjE,QAAA;cACA,IAAA8C,MAAA,CAAAC,KAAA,OACAkB,KAAA,CAAAjE,QAAA,GAAA6C,MAAA,CAAAD,gBAAA,CACAqB,KAAA,CAAAjE,QAAA,EACA8C,MAAA,EACAC,KAAA,IACA;YACA;UACA;QAAA,SAAAlC,GAAA;UAAAkD,UAAA,CAAAjD,CAAA,CAAAD,GAAA;QAAA;UAAAkD,UAAA,CAAAhD,CAAA;QAAA;QACA,OAAA+C,QAAA;MACA;;MAEA;MACA,OAAAF,QAAA,CAAAtH,IAAA;IACA;IAEA4H,UAAA,WAAAA,WAAAC,YAAA;MACA,KAAAzH,OAAA,QAAAC,UAAA,CAAAwH,YAAA;MACA,KAAAxH,UAAA,QAAAA,UAAA,CAAAuE,KAAA,IAAAiD,YAAA;MACA,KAAAtH,SAAA,QAAAA,SAAA,CAAAqE,KAAA,IAAAiD,YAAA;IACA;IAEAjC,aAAA,WAAAA,cAAA;MACA;MACA,KAAAxF,OAAA,QAAAF,UAAA;MACA,KAAAC,eAAA;MACA,KAAAE,UAAA;MACA,KAAAE,SAAA;IACA;IAEA;IACAuH,aAAA,WAAAA,cAAA;MAAA,IAAA3H,eAAA,GAAA8F,SAAA,CAAAlE,MAAA,QAAAkE,SAAA,QAAAxF,SAAA,GAAAwF,SAAA;MACA,IAAAhG,UAAA,QAAAA,UAAA;MACA,KAAAE,eAAA,GAAAA,eAAA;MACA,KAAAC,OAAA,QAAAkG,gBAAA,CACArG,UAAA,CAAA+B,MAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAE,SAAA,KAAAhC,eAAA,CAAAqC,EAAA;MAAA,EACA;MACA,KAAAnC,UAAA;MACA,KAAAE,SAAA;MACA,KAAA8C,YAAA;IACA;IAEA;IACA0E,gBAAA,WAAAA,iBAAA;MACA,IAAAzH,YAAA,QAAAA,YAAA;MACA,IAAA0H,MAAA;MACA,IAAA3F,SAAA;MACA,IAAA4F,MAAA,YAAAA,OAAAlE,GAAA;QAAA,IAAAmE,UAAA,GAAAjE,0BAAA,CACAF,GAAA;UAAAoE,MAAA;QAAA;UAAA,KAAAD,UAAA,CAAA/D,CAAA,MAAAgE,MAAA,GAAAD,UAAA,CAAA9D,CAAA,IAAAC,IAAA;YAAA,IAAAb,IAAA,GAAA2E,MAAA,CAAArI,KAAA;YACA,IAAA0D,IAAA,IAAAA,IAAA,CAAAE,QAAA,IAAAF,IAAA,CAAAE,QAAA,CAAA3B,MAAA;cACAkG,MAAA,CAAAzE,IAAA,CAAAE,QAAA;YACA,YAAAsE,MAAA,CAAApF,QAAA,CAAAY,IAAA,CAAAhB,EAAA;cACAwF,MAAA,CAAAvE,IAAA,CAAAD,IAAA,CAAAhB,EAAA;cACAH,SAAA,CAAAoB,IAAA;gBAAAvE,IAAA,EAAAsE,IAAA,CAAAtE,IAAA;gBAAAsD,EAAA,EAAAgB,IAAA,CAAAhB;cAAA;YACA;UACA;QAAA,SAAA+B,GAAA;UAAA2D,UAAA,CAAA1D,CAAA,CAAAD,GAAA;QAAA;UAAA2D,UAAA,CAAAzD,CAAA;QAAA;MACA;MACAwD,MAAA,CAAA3H,YAAA;MACA,OAAA+B,SAAA;IACA;IAEA+F,eAAA,WAAAA,gBAAAhJ,OAAA;MAAA,IAAAiJ,MAAA;MACA;MACA,IAAAjJ,OAAA;QACAkJ,UAAA;UACAD,MAAA,CAAAzC,aAAA;UACA;UACAyC,MAAA,CAAA7H,WAAA,GAAAC,SAAA;QACA;MACA;IACA;IAEA8H,IAAA,WAAAA,KAAA;MACA;MACA,IAAAlG,SAAA,QAAA0F,gBAAA;MACA,KAAA3F,KAAA,cAAAC,SAAA;MACA;IACA;IAEA;IACAmG,UAAA,WAAAA,WAAA;MAAA,IAAAzE,GAAA,GAAAkC,SAAA,CAAAlE,MAAA,QAAAkE,SAAA,QAAAxF,SAAA,GAAAwF,SAAA;MACA,KAAA3F,YAAA,GAAAyD,GAAA;IACA;IAEA0E,WAAA,WAAAA,YAAA;MAAA,IAAAjF,IAAA,GAAAyC,SAAA,CAAAlE,MAAA,QAAAkE,SAAA,QAAAxF,SAAA,GAAAwF,SAAA;MACA,IAAA/F,UAAA,QAAAA,UAAA;MACA,IAAAiC,SAAA,GAAAqB,IAAA,CAAArB,SAAA;QAAAuG,WAAA,GAAAlF,IAAA,CAAAkF,WAAA;QAAAC,WAAA,GAAAnF,IAAA,CAAAmF,WAAA;QAAAC,WAAA,GAAApF,IAAA,CAAAoF,WAAA;QAAA1J,IAAA,GAAAsE,IAAA,CAAAtE,IAAA;MACA,KAAAA,IAAA;MACA,IAAA2J,WAAA,GAAA3I,UAAA,CAAA6E,IAAA,WAAA9C,CAAA;QAAA,OAAAA,CAAA,CAAAO,EAAA,KAAAL,SAAA;MAAA;MACA,IAAAgB,WAAA,GAAA0F,WAAA,CAAAC,iBAAA,IAAAD,WAAA,CAAA1F,WAAA;MACA,IAAAuB,OAAA,YAAAA,QAAA;QAAA,IAAAX,GAAA,GAAAkC,SAAA,CAAAlE,MAAA,QAAAkE,SAAA,QAAAxF,SAAA,GAAAwF,SAAA;QACA,IAAA8C,MAAA;QACAhF,GAAA,CAAAoB,OAAA,WAAA3B,IAAA,EAAAiD,KAAA;UACAsC,MAAA,IAAAvF,IAAA,IAAAiD,KAAA,uBAAAjD,IAAA;QACA;QACA,OAAAuF,MAAA;MACA;MACA,OAAArE,OAAA,EAAAvB,WAAA,EAAAuF,WAAA,EAAAC,WAAA,EAAAC,WAAA,EAAA1J,IAAA;IACA;EACA;AACA"}]}