{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\error-page\\401.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\error-page\\401.vue", "mtime": 1752737748506}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\.babelrc", "mtime": 1752737748316}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IGVycm9yNDAxIGZyb20gJ0AvYXNzZXRzL2ltYWdlcy9lcnJvci1wYWdlL2Vycm9yLTQwMS5zdmcnOwppbXBvcnQgZXJyb3JDb250ZW50IGZyb20gJy4vZXJyb3ItY29udGVudC52dWUnOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ2Vycm9yXzQwMScsCiAgY29tcG9uZW50czogewogICAgZXJyb3JDb250ZW50OiBlcnJvckNvbnRlbnQKICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBzcmM6IGVycm9yNDAxCiAgICB9OwogIH0KfTs="}, {"version": 3, "names": ["error401", "errorContent", "name", "components", "data", "src"], "sources": ["src/view/error-page/401.vue"], "sourcesContent": ["<template>\r\n  <error-content code=\"401\" desc=\"Oh~~您没有浏览这个页面的权限~\" :src=\"src\"/>\r\n</template>\r\n\r\n<script>\r\nimport error401 from '@/assets/images/error-page/error-401.svg'\r\nimport errorContent from './error-content.vue'\r\nexport default {\r\n  name: 'error_401',\r\n  components: {\r\n    errorContent\r\n  },\r\n  data () {\r\n    return {\r\n      src: error401\r\n    }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": "AAKA,OAAAA,QAAA;AACA,OAAAC,YAAA;AACA;EACAC,IAAA;EACAC,UAAA;IACAF,YAAA,EAAAA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,GAAA,EAAAL;IACA;EACA;AACA"}]}