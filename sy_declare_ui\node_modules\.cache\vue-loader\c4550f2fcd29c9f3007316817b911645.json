{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\base\\gateway\\ipLimit\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\base\\gateway\\ipLimit\\index.vue", "mtime": 1752737748510}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AA6FA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/view/module/base/gateway/ipLimit", "sourcesContent": ["<template>\r\n  <div>\r\n    <Card :shadow=\"true\">\r\n      <Form ref=\"searchForm\"\r\n        class=\"searchForm\"\r\n        :model=\"pageInfo\"\r\n        inline\r\n      >\r\n        <FormItem prop=\"policyName\">\r\n          <Input type=\"text\" v-model=\"pageInfo.policyName\" placeholder=\"请输入策略名称\"/>\r\n        </FormItem>\r\n        <FormItem>\r\n          <Button type=\"primary\" @click=\"handleSearch(1)\">查询</Button>&nbsp;\r\n          <Button @click=\"handleResetForm('searchForm')\">重置</Button>\r\n        </FormItem>\r\n      </Form>\r\n      <div class=\"search-con search-con-top\">\r\n        <ButtonGroup>\r\n          <Button :disabled=\"!hasAuthority('IpLimitAdd')\" class=\"search-btn\" type=\"primary\"\r\n                  @click=\"handleModal()\">\r\n            <span>添加</span>\r\n          </Button>\r\n        </ButtonGroup>\r\n      </div>\r\n      <Table :border=\"true\" :max-height=\"autoTableHeight($refs.autoTableRef)\" ref=\"autoTableRef\" :columns=\"columns\" :data=\"data\" :loading=\"loading\">\r\n        <template v-slot:policyType=\"{ row }\">\r\n          <Tag color=\"green\" v-if=\"row.policyType===1\">允许-白名单</Tag>\r\n          <Tag color=\"red\" v-if=\"row.policyType===0\">拒绝-黑名单</Tag>\r\n          <Tag color=\"green\" v-if=\"row.policyType===2\" >登录白名单</Tag>\r\n        </template>\r\n        <template v-slot:action=\"{ row }\">\r\n          <a v-if=\"hasAuthority('IpLimitEdit')\" @click=\"handleModal(row)\">\r\n            编辑</a>&nbsp;\r\n          <a v-if=\"hasAuthority('IpLimitDel')\" @click=\"handleRemove(row)\">\r\n            删除\r\n          </a>\r\n        </template>\r\n      </Table>\r\n      <Page :total=\"pageInfo.total\" size=\"small\" :current=\"pageInfo.page\" :page-size=\"pageInfo.limit\" :show-elevator=\"true\" :show-sizer=\"true\"\r\n            :show-total=\"true\"\r\n            @on-change=\"handlePage\" @on-page-size-change='handlePageSize'></Page>\r\n    </Card>\r\n    <Modal v-model=\"modalVisible\"\r\n           :title=\"modalTitle\"\r\n           width=\"40\"\r\n           @on-cancel=\"handleReset\">\r\n      <div>\r\n        <Tabs :value=\"current\" @on-click=\"handleTabClick\">\r\n          <TabPane label=\"策略信息\" name=\"form1\">\r\n            <Form ref=\"form1\" v-show=\"current==='form1'\" :model=\"formItem\" :rules=\"formItemRules\" :label-width=\"100\">\r\n              <FormItem label=\"策略名称\" prop=\"policyName\">\r\n                <Input v-model=\"formItem.policyName\" placeholder=\"请输入内容\"></Input>\r\n              </FormItem>\r\n              <FormItem label=\"策略类型\" prop=\"policyType\">\r\n                <Select v-model=\"formItem.policyType\" >\r\n                  <Option value=\"2\" label=\"登录白名单\"></Option>\r\n                  <Option value=\"0\" label=\"拒绝黑名单\"></Option>\r\n                  <Option value=\"1\" label=\"允许白名单\"></Option>\r\n                </Select>\r\n              </FormItem>\r\n              <FormItem label=\"IP地址/域名\" prop=\"ipAddress\">\r\n                <Input v-model=\"formItem.ipAddress\" type=\"textarea\"\r\n                       placeholder=\"***********;***********;baidu.com;\"></Input> 同时支持Ip和域名,多个用分号\";\"隔开。示例：***********;baidu.com;\r\n              </FormItem>\r\n            </Form>\r\n          </TabPane>\r\n          <TabPane :disabled=\"!formItem.id\" label=\"绑定接口\" name=\"form2\">\r\n            <Form ref=\"form2\" v-show=\"current==='form2'\" :model=\"formItem\" :rules=\"formItemRules\">\r\n              <Alert type=\"warning\" :show-icon=\"true\">请注意：如果API上原来已经绑定了一个策略，则会被本策略覆盖，请慎重选择！</Alert>\r\n              <FormItem prop=\"authorities\">\r\n                <Transfer\r\n                  :data=\"selectApis\"\r\n                  :list-style=\"{width: '45%',height: '480px'}\"\r\n                  :titles=\"['选择接口', '已选择接口']\"\r\n                  :render-format=\"transferRender\"\r\n                  :target-keys=\"formItem.apiIds\"\r\n                  @on-change=\"handleTransferChange\"\r\n                  :filterable=\"true\">\r\n                </Transfer>\r\n              </FormItem>\r\n            </Form>\r\n          </TabPane>\r\n        </Tabs>\r\n        <div class=\"drawer-footer\">\r\n          <Button type=\"default\" @click=\"handleReset\">取消</Button>&nbsp;\r\n          <Button type=\"primary\" @click=\"handleSubmit\" :loading=\"saving\" v-if=\"hasAuthority('IpLimitAdd') ||hasAuthority('IpLimitEdit')\"> 保存</Button>\r\n        </div>\r\n      </div>\r\n    </Modal>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport ipLimit from '@/api/base/gateway/ipLimit'\r\nimport {getAuthorityApi} from '@/api/system/authority'\r\nimport {autoTableHeight} from '@/libs/tools.js'\r\n\r\nexport default {\r\n    name: 'gatewayIpLimit',\r\n    data () {\r\n      return {\r\n        autoTableHeight,\r\n        loading: false,\r\n        saving: false,\r\n        modalVisible: false,\r\n        modalTitle: '',\r\n        pageInfo: {\r\n          total: 0,\r\n          page: 1,\r\n          limit: 10,\r\n          policyName: ''\r\n        },\r\n        current: 'form1',\r\n        forms: [\r\n          'form1',\r\n          'form2'\r\n        ],\r\n        selectApis: [],\r\n        formItemRules: {\r\n          policyName: [\r\n            {required: true, message: '策略名称不能为空', trigger: 'blur'}\r\n          ],\r\n          policyType: [\r\n            {required: true, message: '策略类型不能为空', trigger: 'blur'}\r\n          ],\r\n          ipAddress: [\r\n            {required: true, message: 'Ip地址不能为空', trigger: 'blur'}\r\n          ]\r\n        },\r\n        formItem: {\r\n          id: '',\r\n          policyName: '',\r\n          policyType: '0',\r\n          ipAddress: '',\r\n          apiIds: [],\r\n        },\r\n        columns: [\r\n          {\r\n            title: '策略名称',\r\n            key: 'policyName',\r\n            width: 200\r\n          },\r\n          {\r\n            title: '策略类型',\r\n            width: 300,\r\n            slot: 'policyType',\r\n            filters: [\r\n              {\r\n                label: '拒绝-黑名单',\r\n                value: 0\r\n              },\r\n              {\r\n                label: '允许-白名单',\r\n                value: 1\r\n              },\r\n              {\r\n                label: '登录白名单',\r\n                value: 2\r\n              }\r\n            ],\r\n            filterMultiple: false,\r\n            filterMethod (value, row) {\r\n              if (value === 0) {\r\n                return row.policyType === 0\r\n              } else if (value === 1) {\r\n                return row.policyType === 1\r\n              }else if (value === 2) {\r\n                return row.policyType === 2\r\n              }\r\n            }\r\n          },\r\n          {\r\n            title: '最后修改时间',\r\n            key: 'updateTime',\r\n            width: 180\r\n          },\r\n          {\r\n            title: 'IP地址/域名',\r\n            key: 'ipAddress'\r\n          },\r\n          {\r\n            title: '操作',\r\n            slot: 'action',\r\n            fixed: 'right',\r\n            width: 150\r\n          }\r\n        ],\r\n        data: []\r\n      }\r\n    },\r\n    methods: {\r\n      handleModal (data) {\r\n        if (data) {\r\n          this.formItem = Object.assign({}, this.formItem, data)\r\n        }\r\n        if (this.current === this.forms[0]) {\r\n          this.modalTitle = data ? '编辑来源限制策略 - ' + this.formItem.policyName : '添加来源限制'\r\n          this.modalVisible = true\r\n        }\r\n        if (this.current === this.forms[1]) {\r\n          this.modalTitle = data ? '绑定接口 - ' + this.formItem.policyName : '绑定接口'\r\n          this.handleIpLimitApi(this.formItem.id)\r\n        }\r\n        this.formItem.policyType = this.formItem.policyType + ''\r\n      },\r\n      handleResetForm (form) {\r\n        this.$refs[form].resetFields()\r\n      },\r\n      handleTabClick(name){\r\n        this.current = name\r\n        this.handleModal();\r\n      },\r\n      handleReset () {\r\n        this.formItem = {\r\n          id: '',\r\n          policyName: '',\r\n          policyType: '0',\r\n          ipAddress: '',\r\n          apiIds: []\r\n        }\r\n        //重置验证\r\n        this.forms.map(form => {\r\n          this.handleResetForm(form)\r\n        })\r\n        this.current = this.forms[0]\r\n        this.modalVisible = false\r\n        this.saving = false\r\n      },\r\n      handleSubmit () {\r\n        if (this.current === this.forms[0]) {\r\n          this.$refs[this.current].validate((valid) => {\r\n            if (valid) {\r\n              this.saving = true\r\n              if (this.formItem.id) {\r\n                ipLimit.updateIpLimit(this.formItem).then(res => {\r\n                  if (res['code'] === 0) {\r\n                    this.$Message.success('保存成功')\r\n                    this.handleReset()\r\n                    this.handleSearch()\r\n                  }\r\n                }).finally(() => {\r\n                  this.saving = false\r\n                })\r\n              } else {\r\n                ipLimit.addIpLimit(this.formItem).then(res => {\r\n                  if (res['code'] === 0) {\r\n                    this.$Message.success('保存成功')\r\n                    this.handleReset()\r\n                    this.handleSearch()\r\n                  }\r\n                }).finally(() => {\r\n                  this.saving = false\r\n                })\r\n              }\r\n            }\r\n          })\r\n        }\r\n        if (this.current === this.forms[1]) {\r\n          this.$refs[this.current].validate((valid) => {\r\n            if (valid) {\r\n              this.saving = true\r\n              ipLimit.addApiDetails({parentId: this.formItem.id, apiIds: this.formItem.apiIds}).then(res => {\r\n                if (res['code'] === 0) {\r\n                  this.$Message.success('绑定成功')\r\n                  this.handleReset()\r\n                  this.handleSearch()\r\n                }\r\n              }).finally(() => {\r\n                this.saving = false\r\n              })\r\n            }\r\n          })\r\n        }\r\n      },\r\n      handleSearch (page) {\r\n        if (page) {\r\n          this.pageInfo.page = page\r\n        }\r\n        this.loading = true\r\n        ipLimit.listPage(this.pageInfo).then(res => {\r\n          this.data = res.data.records\r\n          this.pageInfo.total = parseInt(res.data.total)\r\n        }).finally(() => {\r\n          this.loading = false\r\n        })\r\n      },\r\n      handlePage (current) {\r\n        this.pageInfo.page = current\r\n        this.handleSearch()\r\n      },\r\n      handlePageSize (size) {\r\n        this.pageInfo.limit = size\r\n        this.handleSearch()\r\n      },\r\n      handleRemove (data) {\r\n        this.$Modal.confirm({\r\n          title: '确定删除吗？',\r\n          onOk: () => {\r\n            ipLimit.removeIpLimit(data.id).then(res => {\r\n              if (res['code'] === 0) {\r\n                this.pageInfo.page = 1\r\n                this.$Message.success('删除成功')\r\n                this.handleReset()\r\n                this.handleSearch()\r\n              }\r\n            })\r\n          }\r\n        })\r\n      },\r\n      handleIpLimitApi (id) {\r\n        if (!id) {\r\n          return\r\n        }\r\n        const that = this\r\n        const p1 = getAuthorityApi('')\r\n        const p2 = ipLimit.getApiDetails(id);\r\n        Promise.all([p1, p2]).then(function (values) {\r\n          let res1 = values[0]\r\n          let res2 = values[1]\r\n          if (res1.code === 0) {\r\n            res1.data.map(item => {\r\n              item.key = item.id\r\n              item.label = `${item.prefix.replace('/**', '')}${item.path} - ${item.apiName}`\r\n            })\r\n            that.selectApis = res1.data\r\n          }\r\n          if (res2.code === 0) {\r\n            let apiIds = []\r\n            res2.data.map(item => {\r\n              if (!apiIds.includes(item.apiId)) {\r\n                apiIds.push(item.apiId)\r\n              }\r\n            })\r\n            that.formItem.apiIds = apiIds\r\n          }\r\n          that.modalVisible = true\r\n        })\r\n      },\r\n      transferRender (item) {\r\n        return `<span  title=\"${item.label}\">${item.label}</span>`\r\n      },\r\n      handleTransferChange (newTargetKeys) {\r\n\r\n        if (newTargetKeys.indexOf('1') !== -1) {\r\n          this.formItem.apiIds = ['1']\r\n        } else {\r\n          this.formItem.apiIds = newTargetKeys\r\n        }\r\n      }\r\n    },\r\n    mounted: function () {\r\n      this.handleSearch()\r\n    }\r\n  }\r\n</script>\r\n"]}]}