{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\newApply\\newApply\\newApply\\index.vue?vue&type=template&id=9bb720fa&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\newApply\\newApply\\newApply\\index.vue", "mtime": 1754365176808}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}