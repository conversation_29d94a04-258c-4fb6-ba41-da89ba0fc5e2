{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\basf\\listing\\index.vue?vue&type=style&index=0&id=f0a77188&lang=less&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\basf\\listing\\index.vue", "mtime": 1752737748515}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1752744819993}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouY29weUxhYmVsLCAuaXZ1LXRvb2x0aXAgew0KICBjdXJzb3I6IHBvaW50ZXI7DQp9DQoub3JkZXJCaWxsRHJvcHsNCiAgLnBvcENvbnRlbnRDbGFzc3sNCiAgICBtYXJnaW46IDNweCA4cHggMCA4cHg7DQogICAgdGV4dGFyZWF7DQogICAgICByZXNpemU6IG5vbmU7DQogICAgfQ0KICB9DQp9DQoNCi5zZWFyY2gtY29uLXRvcHsNCiAgcG9zaXRpb246IHJlbGF0aXZlOw0KICBwYWRkaW5nOiAwOw0KICAvL+agh+etvuOAgXNrdeOAgWFzaW7mkJzntKLpobkNCiAgLnNhbGVMaXN0aW5nSW5wdXRJdGVtWHsNCiAgICAuZmxleC1oew0KICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgICAgIG1hcmdpbi10b3A6MnB4Ow0KICAgIH0NCiAgICAucG9wdGlwQ29udGVudEluew0KICAgICAgbWFyZ2luOiAzcHggOHB4IDAgOHB4Ow0KICAgICAgdGV4dGFyZWF7DQogICAgICAgIHJlc2l6ZTogbm9uZTsNCiAgICAgIH0NCiAgICB9DQogIH0NCiAgLmNsaWNrRHJvcGRvd25Gb3Jtew0KICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCiAgICByaWdodDogMTIycHg7DQogICAgdG9wOiA0MnB4Ow0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAwVA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/view/module/basf/listing", "sourcesContent": ["<template>\r\n  <div class=\"saleListingPage\">\r\n    <Card :shadow=\"true\">\r\n      <div class=\"search-con search-con-top\">\r\n        <Form ref=\"searchForm\" class=\"searchForm\" :model=\"formValues\" :inline=\"true\" @submit.native.prevent>\r\n          <FormItem prop=\"shop\">\r\n            <ShopSelect v-model=\"formValues.shops\" placeholder=\"店铺\" width=\"205px\" :valueField=\"'id'\"/>\r\n          </FormItem>\r\n          <FormItem prop=\"site\">\r\n            <i-select :multiple=\"true\" :filterable=\"true\" v-model=\"formValues.sites\"  placeholder=\"站点\" :max-tag-count=\"1\" style=\"width: 160px\" :transfer=\"true\">\r\n              <i-option v-for=\"v in siteArr\" :value=\"v.id\" v-bind:key=\"v.id\">{{ v.name }}</i-option>\r\n            </i-select>\r\n          </FormItem>\r\n          <FormItem class=\"saleListingInputItemX\" prop=\"searchKey\">\r\n            <div class=\"flex-h\">\r\n              <Select v-model=\"formValues.searchKey\" style=\"width:100px\" :transfer=\"true\">\r\n                <Option value=\"SELLER_SKU\">MSKU</Option>\r\n                <Option value=\"ASIN\">ASIN</Option>\r\n                <Option value=\"FNSKU\">fnsku</Option>\r\n                <Option value=\"PARENT_ASIN\">父ASIN</Option>\r\n                <Option value=\"LOCAL_SKU\">料品SKU</Option>\r\n                <Option value=\"LOCAL_NAME\">料品名称</Option>\r\n              </Select>\r\n              <Multiple placeholder=\"请输入(回车分隔)\" @changeValue=\"(values)=>{ multiValues = values || []; }\" ref=\"multipleRef\"></Multiple>\r\n              <Button :visible=\"false\" @click=\"()=>{popVisible=true;}\">输入</Button>\r\n              <Dropdown trigger=\"custom\" :visible=\"popVisible\" style=\"margin-left: 3px\" :transfer=\"true\" transfer-class-name=\"orderBillDrop\">\r\n                <template #list>\r\n                  <DropdownMenu class=\"popContentClass\">\r\n                    <Input v-model=\"popContent\" type=\"textarea\" :autosize=\"{minRows: 4,maxRows: 8}\" placeholder=\"请输入内容，回车或逗号分隔\" style=\"width: 260px\"/>\r\n                    <div style=\"text-align: right; padding-top: 3px\">\r\n                      <Button type=\"info\" size=\"small\" @click=\"closeDropdown\">确定</Button>\r\n                    </div>\r\n                  </DropdownMenu>\r\n                </template>\r\n              </Dropdown>\r\n            </div>\r\n          </FormItem>\r\n          <FormItem prop=\"fulfillChannel\">\r\n            <Select v-model=\"formValues.fulfillChannel\" placeholder=\"配送方式\" style=\"width: 100px\" :clearable=\"true\" :transfer=\"true\">\r\n              <Option value=\"FBA\">FBA</Option>\r\n              <Option value=\"FBM\">FBM</Option>\r\n            </Select>\r\n          </FormItem>\r\n          <FormItem prop=\"sellers\" class=\"sellerSelectItem\">\r\n            <Select multiple type=\"text\" v-model=\"formValues.sellers\" placeholder=\"销售员\" filterable :max-tag-count=\"1\" style=\"width: 233px\" :transfer=\"true\" >\r\n              <Option v-for=\"item in sellerArr\" :value=\"item.id\" :key=\"item.id\">{{ item.nickName }}</Option>\r\n            </Select>\r\n            <Button type=\"dashed\" @click=\"openPerson\" style=\"margin-left: 3px\" size=\"default\">选择</Button>\r\n            <person-select :visible=\"personVisible\" :onCancel=\"()=>personVisible=false\"\r\n                           @setPerson=\"arr => (formValues.sellers = arr.map(v => v.id))\" @setSelectInfo=\"setSelectInfo\"\r\n                           ref=\"personSelectRef\" groupName=\"operations_persons\" :multiple=\"true\" :isQuery=\"true\" />\r\n          </FormItem>\r\n          <FormItem>\r\n            <Button type=\"primary\" @click=\"handleSearch(1)\">查询</Button>&nbsp;\r\n            <Button @click=\"handleResetForm('searchForm')\">重置</Button>\r\n          </FormItem>\r\n        </Form>\r\n      </div>\r\n      <div class=\"search-con search-con-top\">\r\n        <ButtonGroup>\r\n          <Button type=\"primary\" class=\"search-btn\" @click=\"handleModal('sync')\"><span>同步listing</span></Button>\r\n          <Button style=\"margin-left:15px;\" class=\"search-btn\" @click=\"handleModal('importRelax')\"><span>导入关联</span></Button>\r\n          <Button style=\"margin-left:15px;\" @click=\"executeExport();\">导出Listing</Button>\r\n          <Button style=\"margin-left:15px;\" @click=\"handleModal('exportLog')\" >查看导出记录</Button>\r\n        </ButtonGroup>\r\n      </div>\r\n      <Table :border=\"true\" ref=\"autoTableRef\" :max-height=\"autoTableHeight($refs.autoTableRef)\" :columns=\"columns\"\r\n             :data=\"data\" :loading=\"loading\">\r\n        <template v-slot:imageSection=\"{row}\">\r\n          <div class=\"productImgDiv\">\r\n            <span><Img :src=\"row.smallImageUrl\"/></span>\r\n          </div>\r\n        </template>\r\n        <template v-slot:itemName=\"{ row }\">\r\n          <Tooltip :transfer=\"true\" placement=\"right-end\" :max-width=\"500\">\r\n            <div slot=\"content\" style=\"word-break: break-all; white-space: pre-wrap\">\r\n              {{ row.itemName}}\r\n            </div>\r\n            <div class=\"overflowText\" style=\"max-width: 300px\" v-copytext=\"row.itemName\">\r\n              {{row.itemName.length>40?(row.itemName.substring(0,37)+\"...\"):row.itemName }}\r\n            </div>\r\n          </Tooltip>\r\n        </template>\r\n        <template v-slot:description=\"{ row }\">\r\n          <Tooltip :transfer=\"true\" placement=\"right-end\" :max-width=\"500\">\r\n            <div slot=\"content\" style=\"word-break: break-all; white-space: pre-wrap\">\r\n              {{ row.description}}\r\n            </div>\r\n            <div class=\"overflowText\" style=\"max-width: 300px\" v-copytext=\"row.description\">\r\n              {{row.description.length>40?(row.description.substring(0,50)+\"...\"):row.description }}\r\n            </div>\r\n          </Tooltip>\r\n        </template>\r\n      </Table>\r\n      <Page :total=\"pageInfo.total\" size=\"small\" :current=\"pageInfo.page\" :page-size=\"pageInfo.limit\" :show-elevator=\"true\"\r\n            :show-sizer=\"true\" :show-total=\"true\" @on-change=\"handlePage\" @on-page-size-change='handlePageSize'></Page>\r\n    </Card>\r\n    <ImportFile :onCancel=\"()=>{this.modalImportVisible=false;}\" :visible=\"modalImportVisible\" ref=\"ImportModalRef\" :title=\"importTitle\" :taskType=\"importTaskType\"\r\n                :downTemplateUrl=\"templateUrl\" :templateName=\"templateName\" :url=\"importUrl\" :shadow=\"true\" :executeUrl=\"executeUrl\"/>\r\n    <ListingSync :onCancel=\"()=>{this.modalSyncVisible=false;}\" :visible=\"modalSyncVisible\" ref=\"syncModalRef\" :shadow=\"true\"/>\r\n    <ExportFile :onCancel=\"()=>{this.modalExportVisible=false;}\" :visible=\"modalExportVisible\" ref=\"ExportModalRef\" :title=\"exportTitle\" :taskType=\"exportTaskType\"\r\n                :shadow=\"true\" :executeUrl=\"executeUrl\" :fileName=\"exportFileName\"/>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Listing from '@/api/basf/listing'\r\nimport ExportFileJs from '@/api/common/exportFile'\r\nimport {autoTableHeight} from '@/libs/tools.js'\r\nimport ShopSelect from \"@/components/shopSelect/index.vue\";\r\nimport ListingSync from \"./components/ListingSync.vue\";\r\nimport ImportFile from \"../../common/importFile.vue\";\r\nimport ExportFile from \"../../common/exportFile.vue\";\r\nimport Multiple from \"@/view/module/common/multipleInput.vue\";\r\nimport personSelect from \"@/components/person-select-radio/index.vue\";\r\nimport Site from \"@/api/basf/site\";\r\nexport default {\r\n  name: 'listing',\r\n  components: {\r\n    personSelect,\r\n    ShopSelect,\r\n    ListingSync,\r\n    ImportFile,\r\n    ExportFile,\r\n    Multiple\r\n  },\r\n  data() {\r\n    return {\r\n      autoTableHeight,\r\n      siteArr:[],\r\n      sellerArr:[],\r\n      multiValues: [],\r\n      popVisible:false,\r\n      personVisible:false,\r\n      //SKU、ASIN、标签\r\n      popContent: undefined,//popContent输入内容\r\n      loading: false,\r\n      saving: false,\r\n      syncTitle: '同步Listing',\r\n      exportTime:new Date(),\r\n      formValues:{\r\n        searchKey:null,\r\n        shops:[],\r\n        sites:[],\r\n        fulfillChannel:null,\r\n        sellers:[]\r\n      },\r\n      pageInfo: {\r\n        total: 0,\r\n        page: 1,\r\n        limit: 10\r\n      },\r\n      columns: [{type: 'selection',width: 60},\r\n        {  title: '图片',  key: 'smallImageUrl',  width: 100,slot: 'imageSection'},\r\n        {  title: '网店',  key: 'shopName',  width: 90,render: (h, {row}) => (<span v-copytext={row.shopName}>{row.shopName}</span>)},\r\n        {  title: 'MSKU',  key: 'sellerSku',  width: 200,render: (h, {row}) => (<span v-copytext={row.sellerSku}>{row.sellerSku}</span>)},\r\n        {  title: 'asin',  key: 'asin',  width: 120,render: (h, {row}) => (<span v-copytext={row.asin}>{row.asin}</span>)},\r\n        {  title: 'fnsku/gtin',  key: 'fnsku',  minWidth: 150,render: (h, {row}) => (<span v-copytext={row['fnsku']}>{row['fnsku']}</span>)},\r\n        {  title: '父Asin',  key: 'parentAsin',  width: 120,render: (h, {row}) => (<span v-copytext={row['parentAsin']}>{row['parentAsin']}</span>)},\r\n        {  title: 'upc',  key: 'upc',  width: 120,render: (h, {row}) => (<span v-copytext={row['upc']}>{row['upc']}</span>)},\r\n        {  title: '配送方式',  key: 'fulfillmentType',  width: 90},\r\n        {  title: '状态',  key: 'status', width: 80,render: (_, { row })=>{let status = row.status|0;\r\n            let isDelete = row['isDelete']|0;\r\n            let text = isDelete === 1?'已删除':(status ===1?'在售':'停售');\r\n            return (<span>{text}</span>);}},\r\n        {  title: '币种',  key: 'currencyCode', width: 80},\r\n        {  title: '价格',  key: 'price', width: 80},\r\n        {  title: '优惠价',  key: 'listingPrice', width: 80},\r\n        {  title: '总价',  key: 'landedPrice', width: 80},\r\n        {  title: '标题',  key: 'itemName',  width: 200,slot: 'itemName'},\r\n        {  title: '产品编码',  key: 'localSku',  width: 120,render: (h, {row}) => (<span v-copytext={row['localSku']}>{row['localSku']}</span>)},\r\n        {  title: '产品品名',  key: 'localName',  width: 200,render: (h, {row}) => (<span v-copytext={row['localName']}>{row['localName']}</span>)},\r\n        {  title: '描述',  key: 'description',  minWidth: 200,slot: 'description'},\r\n        {  title: '备注',  key: 'remark',  width: 100},\r\n        {  title: 'FBM库存',  key: 'quantity',  width: 80},\r\n        {  title: 'FBA可售',  key: 'afnFulfillableQuantity',  width: 80},\r\n        {  title: 'FBA不可售',  key: 'afnUnsellableQuantity',  width: 80},\r\n        {  title: '待调仓',  key: 'reservedFcTransfers',  width: 80},\r\n        {  title: '调仓中',  key: 'reservedFcProcessing',  width: 80},\r\n        {  title: '待发货',  key: 'reservedCustomerOrders',  width: 80},\r\n        {  title: '在途',  key: 'afnInboundShippedQuantity',  width: 80},\r\n        {  title: '计划入库',  key: 'afnInboundWorkingQuantity',  width: 80},\r\n        {  title: '入库中',  key: 'afnInboundReceivingQuantity',  width: 80},\r\n        {  title: '负责人',  key: 'principalUser',  width: 200,render: (h, {row}) => (<span v-copytext={row['principalUserName']}>{row['principalUserName']}</span>)}\r\n      ],\r\n      data: [],\r\n      modalSyncVisible:false,\r\n      modalImportVisible:false,\r\n      importTitle:\"导入商品关联\",\r\n      importTaskType:\"MSKU_ANS_SKU_RELAX_IMPORT\",\r\n      importUrl:\"/base/listing/importRelaxFile\",\r\n      templateUrl:\"/base/listing/downloadRelaxTemplate\",\r\n      templateName:\"Listing关联本地商品模板\",\r\n      executeUrl:\"/base/listing/execute\",\r\n      modalExportVisible:false,\r\n      exportTitle:\"导出Listing\",\r\n      exportTaskType:\"MSKU_RECORD_EXPORT\",\r\n      interval: null,\r\n      exportFileName:\"Listing列表\"\r\n    }\r\n  },\r\n  methods: {\r\n    loadData(page){\r\n\r\n    },\r\n    openPerson(){\r\n      this.resetMultiple();\r\n      //打开人员选择\r\n      const { personSelectRef } = this.$refs;\r\n      const { sellerArr, pageInfo } = this;\r\n      const selectedIds = pageInfo.sellerId || [];\r\n      if (personSelectRef) personSelectRef.setDefault(\r\n          sellerArr.filter(v => selectedIds.includes(v.id)).map(v=>({ name: v.nickName, id: v.id }))\r\n      );//给组件设置默认选中\r\n      this.personVisible = true;\r\n    },\r\n    resetMultiple() { //重置pop\r\n      this.popContent = undefined;\r\n      this.popVisible = false;\r\n    },\r\n    closeDropdown() { //关闭输入文本框\r\n      const { popContent } = this;\r\n      const { multipleRef } = this.$refs;\r\n      this.popVisible = false;\r\n      if(!popContent) return;\r\n      const content = popContent ? popContent.trim().replace(/，/g, \",\") : '';\r\n      this.multiValues = content.split('\\n').filter(v=>!!v);\r\n      this.multiValues = [...new Set(this.multiValues)];\r\n      if(multipleRef && multipleRef.setValueArray){\r\n        multipleRef.setValueArray(this.multiValues);\r\n      }\r\n      this.popContent = undefined;\r\n    },\r\n    getParams(needPage = false) {\r\n      const { formValues, pageInfo, multiValues } = this;\r\n      const getStr = value => value && Array.isArray(value) ? value.join(\",\") : undefined;\r\n      const pageParam = needPage ? pageInfo : {};\r\n      const params = {\r\n        ...pageParam,\r\n        fulfillChannel:formValues.fulfillChannel,\r\n        searchKey:formValues.searchKey,\r\n        shopId: getStr(formValues.shops),\r\n        siteId: getStr(formValues.sites),\r\n        sellerId: getStr(formValues.sellers)\r\n      };\r\n      if (formValues.searchKey && multiValues.length > 0){\r\n        params[\"searchKey\"] = formValues.searchKey;\r\n        const getStr = value => value && Array.isArray(value) ? value.join(\"&#&\") : undefined;\r\n        params[\"searchValue\"] = getStr(multiValues);\r\n      }\r\n      return params;\r\n    },\r\n    handleSearch(page) {\r\n      if (page) {\r\n        this.pageInfo.page = page\r\n      }\r\n      this.loading = true\r\n      Listing.listPage(this.getParams(true)).then((res)=>{\r\n        if (res && res['code'] === 0) {\r\n          const data = res.data || {};\r\n          this.data = data.records || [];\r\n          const getValue = (value, defaultValue) =>\r\n            value || value === 0 ? +value : defaultValue;\r\n          this.pageInfo = {\r\n            total: getValue(data.total, 0),\r\n            page: getValue(data.page, 1),\r\n            limit: getValue(data.limit, 10)\r\n          };\r\n        } else this.data = [];\r\n      }).catch(() => {\r\n        this.data = [];\r\n      }).finally(() => {\r\n        this.loading = false;\r\n      });\r\n    },\r\n    handlePage(current) {\r\n      this.pageInfo.page = current\r\n      this.handleSearch()\r\n    },\r\n    handlePageSize(size) {\r\n      this.pageInfo.limit = size\r\n      this.handleSearch()\r\n    },\r\n    handleResetForm(form) {\r\n      this.$refs[form].resetFields();\r\n      this.formValues.shops=[];\r\n    },\r\n    // 人员选择相关\r\n    setSelectInfo(info={}){\r\n      this.sellerArr = info.personArr || [];\r\n    },\r\n    loadSite(){\r\n      Site.getAll().then((res)=>{\r\n        if(res['code'] ===0){\r\n          res['data'].forEach(item=>{\r\n            this.siteArr.push({\"id\":item.id,\"name\":item.name});\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleModal(type){\r\n      if(type === 'sync'){\r\n        this.modalSyncVisible = true;\r\n      }else if(type === 'importRelax'){\r\n        this.modalImportVisible = true;\r\n      }else if(type === 'exportLog'){\r\n        this.modalExportVisible = true;\r\n      }\r\n    },\r\n    handleReset(type){\r\n      if(type === 'sync'){\r\n        this.modalSyncVisible = false;\r\n      }\r\n    },\r\n    executeExport () {\r\n      if(new Date() - this.exportTime <= 5000){\r\n        this.$Message.success('导出间隔5S！');\r\n        return;\r\n      }\r\n      this.exportTime = new Date();\r\n      Listing.exportFile(this.getParams(false)).then(res => {\r\n        if (res['code'] === 0) {\r\n          this.$Message.success('导出消息添加成功！');\r\n          this.loading = false;\r\n          ExportFileJs.intervalFunc({\"id\":res.data,\"fileName\":this.exportFileName});\r\n        }else{\r\n          this.$Message.error(res['message']);\r\n        }\r\n      }).catch(() => {\r\n\r\n      }).finally(() => {\r\n        this.loading = false;\r\n      });\r\n    },\r\n  },\r\n\r\n  mounted: function () {\r\n    this.loadSite();\r\n    this.handleSearch(1);\r\n  }\r\n}\r\n\r\n</script>\r\n<style lang=\"less\">\r\n.copyLabel, .ivu-tooltip {\r\n  cursor: pointer;\r\n}\r\n.orderBillDrop{\r\n  .popContentClass{\r\n    margin: 3px 8px 0 8px;\r\n    textarea{\r\n      resize: none;\r\n    }\r\n  }\r\n}\r\n\r\n.search-con-top{\r\n  position: relative;\r\n  padding: 0;\r\n  //标签、sku、asin搜索项\r\n  .saleListingInputItemX{\r\n    .flex-h{\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      margin-top:2px;\r\n    }\r\n    .poptipContentIn{\r\n      margin: 3px 8px 0 8px;\r\n      textarea{\r\n        resize: none;\r\n      }\r\n    }\r\n  }\r\n  .clickDropdownForm{\r\n    position: absolute;\r\n    right: 122px;\r\n    top: 42px;\r\n  }\r\n}\r\n</style>\r\n"]}]}