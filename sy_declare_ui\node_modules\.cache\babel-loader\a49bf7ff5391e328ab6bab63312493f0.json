{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\custom\\base\\whAddress\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\custom\\base\\whAddress\\index.vue", "mtime": 1754360258641}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\.babelrc", "mtime": 1752737748316}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["CommonApi", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "LogModel", "getToken", "getUrl", "components", "data", "loading", "saving", "modal", "spinShow", "disabled", "logVisible", "refType", "title", "searchForm", "whCode", "country", "address", "pageInfo", "total", "page", "limit", "loginInfo", "Accept", "mode", "Authorization", "importURl", "form", "province", "postCode", "column", "key", "min<PERSON><PERSON><PERSON>", "align", "slot", "width", "countryList", "mounted", "handleSearch", "getCountryList", "getLogRefType", "methods", "_this", "params", "_objectSpread", "listPage", "then", "res", "records", "Number", "finally", "handleReset", "$refs", "resetFields", "handleImportSuccess", "clearFiles", "$Message", "success", "warning", "handleImportFormatError", "file", "$Modal", "error", "content", "name", "okText", "handleImportError", "err", "message", "handleMaxSize", "whAddressExport", "_this2", "Date", "getExportFormat", "download", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "row", "_this3", "confirm", "onOk", "remove", "id", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "resetForm", "Object", "assign", "add<PERSON><PERSON><PERSON><PERSON><PERSON>", "save<PERSON>h<PERSON>dd<PERSON>", "_this4", "validate", "valid", "_this5", "ListDictionaryValueBy", "map", "item", "JSON", "parse", "value", "handlePage", "handlePageSize", "size", "cancelForm", "lookLog", "logModelRef", "<PERSON><PERSON><PERSON><PERSON>", "_this6"], "sources": ["src/view/module/custom/base/whAddress/index.vue"], "sourcesContent": ["<!--\r\n@create date 2020-12-14\r\n<AUTHOR>\r\n@desc 报关清关地址维护表\r\n-->\r\n<template>\r\n  <Card>\r\n    <Form ref=\"searchForm\" :model=\"searchForm\" inline>\r\n      <FormItem prop=\"whCode\">\r\n        <Input v-model=\"searchForm.whCode\" placeholder=\"请输入仓库代码\"/>\r\n      </FormItem>\r\n      <FormItem prop=\"country\">\r\n        <Select v-model=\"searchForm.country\" filterable clearable placeholder=\"请选择国家\" style=\"width:150px\">\r\n          <Option v-for=\"(item,index) in countryList\" :value=\"item['two_code']\" :key=\"index\">{{ item['name_cn'] }}\r\n          </Option>\r\n        </Select>\r\n      </FormItem>\r\n      <FormItem prop=\"address\">\r\n        <Input v-model=\"searchForm.address\" placeholder=\"请输入地址\"/>\r\n      </FormItem>\r\n      <FormItem>\r\n        <Button type=\"primary\" @click=\"handleSearch()\">查询</Button>\r\n        <Button @click=\"handleReset()\" style=\"margin-left:10px\">重置</Button>\r\n      </FormItem>\r\n    </Form>\r\n    <div style=\"margin-bottom:10px\">\r\n      <div style=\"float:left\">\r\n        <Upload ref=\"uploadFileRef\" name=\"importFile\" :action=\"importURl\" :max-size=\"10240\"\r\n                :on-success=\"handleImportSuccess\" :format=\"['xls', 'xlsx']\" :show-upload-list=\"false\"\r\n                :on-format-error=\"handleImportFormatError\"\r\n                :on-error=\"handleImportError\" :headers=\"loginInfo\" :on-exceeded-size=\"handleMaxSize\">\r\n          <Button class=\"search-btn\" type=\"primary\">导入</Button>\r\n        </Upload>\r\n      </div>\r\n      <Button class=\"search-btn\" style=\"margin-left:10px;\" @click=\"addWhAddress\">新增</Button>\r\n      <Button class=\"search-btn\" style=\"margin-left:10px;\" @click=\"whAddressExport\">导出</Button>\r\n    </div>\r\n    <Table :border=\"true\" :columns=\"column\" :data=\"data\" :loading=\"loading\">\r\n      <template v-slot:country=\"{row}\">\r\n        <span v-for=\"item in countryList\" v-if=\"item['two_code'] === row.country\">{{ item['name_cn'] }}</span>\r\n      </template>\r\n      <template v-slot:action=\"{row}\">\r\n        <Button size=\"small\" type=\"info\" @click=\"editWhAddress(row)\" style=\"margin:0 2px\">编辑</Button>\r\n        <Button size=\"small\" type=\"info\" @click=\"delWhAddress(row)\" style=\"margin:0 2px\">删除</Button>\r\n        <Button size=\"small\" type=\"info\" @click=\"lookLog(row)\" style=\"margin:0 2px\">日志</Button>\r\n      </template>\r\n    </Table>\r\n    <Page :total=\"pageInfo.total \" :current=\"pageInfo.page\" :page-size=\"pageInfo.limit\" :show-elevator=\"true\"\r\n          :show-sizer=\"true\"\r\n          :show-total=\"true\" @on-change=\"handlePage\" @on-page-size-change='handlePageSize' :transfer=\"true\"></Page>\r\n    <Modal :width=\"530\" v-model=\"modal\" :title=\"title\" @on-cancel=\"cancelForm\">\r\n      <Spin :fix=\"true\" v-if=\"spinShow\">加载中...</Spin>\r\n      <Form ref=\"form\" :model=\"form\" inline label-position=\"right\" :label-width=\"110\">\r\n        <FormItem label=\"仓库代码\" prop=\"whCode\" :rules=\"{required: true, message: '不能为空', trigger: 'blur'}\">\r\n          <Input v-model.trim=\"form.whCode\" placeholder=\"请输入仓库代码\" class=\"widthClass\"/>\r\n        </FormItem>\r\n        <FormItem label=\"国家\" prop=\"country\" :rules=\"{required: true, message: '不能为空', trigger: 'blur'}\">\r\n          <Select v-model=\"form.country\" filterable clearable placeholder=\"请选择所在国家\" class=\"widthClass\">\r\n            <Option v-for=\"(item,index) in countryList\" :value=\"item['two_code']\" :key=\"index\">{{ item['name_cn'] }}\r\n            </Option>\r\n          </Select>\r\n        </FormItem>\r\n        <FormItem label=\"州\" prop=\"province\" :rules=\"{required: true, message: '不能为空', trigger: 'blur'}\">\r\n          <Input v-model.trim=\"form.province\" placeholder=\"请输入\" class=\"widthClass\"/>\r\n        </FormItem>\r\n        <FormItem label=\"邮编\" prop=\"postCode\">\r\n          <Input v-model.trim=\"form.postCode\" placeholder=\"请输入\" class=\"widthClass\"/>\r\n        </FormItem>\r\n        <FormItem label=\"地址\" prop=\"address\">\r\n          <Input v-model.trim=\"form.address\" placeholder=\"请输入\" class=\"widthClass\"/>\r\n        </FormItem>\r\n      </Form>\r\n      <div slot=\"footer\">\r\n        <Button type=\"primary\" :disabled=\"disabled\" :loading=\"saving\" @click=\"saveWhAddress\">保存</Button>\r\n        <Button @click=\"cancelForm\">取消</Button>\r\n      </div>\r\n    </Modal>\r\n    <LogModel ref=\"logModelRef\" :logVisible=\"logVisible\" :onCancel=\"()=>logVisible=false\"/>\r\n  </Card>\r\n</template>\r\n<script>\r\nimport CommonApi from \"@/api/base/commonApi\";\r\nimport WhAddress from \"@/api/custom/whAddress\";\r\nimport LogModel from \"@/view/module/base/bussinessLog/logModel.vue\";\r\nimport {getToken, getUrl} from \"@/libs/util\";\r\n\r\nexport default {\r\n  components: {LogModel},\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      saving: false,\r\n      modal: false,\r\n      spinShow: false,\r\n      disabled: false,\r\n      logVisible: false,\r\n      refType: null,\r\n      title: '',\r\n      searchForm: {whCode: '', country: '', address: ''},\r\n      pageInfo: {total: 0, page: 1, limit: 10},\r\n      loginInfo: {\r\n        Accept: 'application/json,text/plain, */*, */*',\r\n        mode: 'cors',\r\n        Authorization: 'Bearer ' + getToken()\r\n      },\r\n      importURl: getUrl() + \"/base/whAddress/importFile\",\r\n      form: {whCode: null, country: null, province: null, postCode: null, address: null},\r\n      data: [],\r\n      column: [\r\n        {title: '仓库编码', key: 'whCode', minWidth: 100, align: 'center'},\r\n        {title: '所在国家', key: 'country', minWidth: 100, align: 'center', slot: 'country'},\r\n        {title: '州', key: 'province', minWidth: 100, align: 'center'},\r\n        {title: '邮编', key: 'postCode', minWidth: 150, align: 'center'},\r\n        {title: '地址', key: 'address', minWidth: 300, align: 'center'},\r\n        {title: '操作', key: 'action', width: 200, align: 'center', slot: 'action'}],\r\n      countryList: [],\r\n    }\r\n  },\r\n  mounted() {\r\n    this.handleSearch();\r\n    this.getCountryList();\r\n    this.getLogRefType();\r\n  },\r\n  methods: {\r\n    handleSearch() {\r\n      this.loading = true;\r\n      let params = {...this.searchForm, ...this.pageInfo}\r\n      WhAddress.listPage(params).then(res => {\r\n        if (res['code'] === 0) {\r\n          this.data = res.data.records;\r\n          this.pageInfo.total = Number(res.data.total)\r\n        }\r\n      }).finally(() => {\r\n        this.loading = false;\r\n      })\r\n    },\r\n    handleReset() {\r\n      this.$refs['searchForm'].resetFields();\r\n    },\r\n    handleImportSuccess(res) {\r\n      this.$refs['uploadFileRef'].clearFiles();\r\n      if (res['code'] === 0) {\r\n        this.$Message.success('导入成功');\r\n        this.handleSearch();\r\n      } else {\r\n        this.$Message.warning(res['message']);\r\n      }\r\n    },\r\n    handleImportFormatError(file) {\r\n      //格式验证失败的钩子\r\n      this.$Modal.error({\r\n        title: '文件格式不正确',\r\n        content: '文件 ' + file.name + '格式不正确，支持上传的文件类型：xls,xlsx',\r\n        okText: '确认'\r\n      });\r\n    },\r\n    handleImportError(err, file) {\r\n      this.$Message.warning(file.message);\r\n    },\r\n    handleMaxSize() {\r\n      this.$Message.warning('图片大小不能超过2M.');\r\n    },\r\n    whAddressExport() {\r\n      let params = {...this.searchForm};\r\n      params['fileName'] = \"仓库地址\" + new Date().getExportFormat() + \".xls\";\r\n      this.loading = true;\r\n      WhAddress.download(params, () => {\r\n        this.loading = false\r\n      })\r\n    },\r\n    delWhAddress(row) {\r\n      this.$Modal.confirm({\r\n        title: '提示',\r\n        content: '您确认要删除这条数据吗？',\r\n        onOk: () => {\r\n          WhAddress.remove({id: row.id}).then(res => {\r\n            if (res['code'] === 0) {\r\n              this.$Message.success('删除成功!');\r\n              this.handleSearch();\r\n            }\r\n          })\r\n        },\r\n      })\r\n    },\r\n    editWhAddress(row) {\r\n      this.title = \"修改\";\r\n      this.modal = true;\r\n      this.disabled = false;\r\n      this.resetForm();\r\n      this.form = Object.assign({}, row);\r\n    },\r\n    addWhAddress() {\r\n      this.title = \"添加\";\r\n      this.modal = true;\r\n      this.disabled = false;\r\n      this.resetForm();\r\n    },\r\n    saveWhAddress() {\r\n      this.$refs['form'].validate((valid) => {\r\n        if (valid) {\r\n          this.saving = true;\r\n          WhAddress.saveWhAddress(this.form).then(res => {\r\n            if (res['code'] === 0) {\r\n              this.saving = false;\r\n              this.$Message.success('保存成功!');\r\n              this.resetForm();\r\n              this.modal = false;\r\n              this.handleSearch();\r\n            }\r\n          }).finally(() => {\r\n            this.saving = false;\r\n          })\r\n        }\r\n      })\r\n    },\r\n    //获取目的国家\r\n    getCountryList() {\r\n      CommonApi.ListDictionaryValueBy(\"base_country\").then(res => {\r\n        if (res && res['code'] === 0) {\r\n          let data = res['data']\r\n          if (data) {\r\n            this.countryList = data.map(item => JSON.parse(item.value));\r\n          }\r\n        }\r\n      })\r\n    },\r\n    handlePage(page) {\r\n      this.pageInfo.page = page;\r\n      this.handleSearch();\r\n    },\r\n    handlePageSize(size) {\r\n      this.pageInfo.page = 1;\r\n      this.pageInfo.limit = size;\r\n      this.handleSearch();\r\n    },\r\n    cancelForm() {\r\n      this.modal = false;\r\n      this.resetForm();\r\n    },\r\n    resetForm() {\r\n      this.$refs['form'].resetFields();\r\n      this.form = {};\r\n    },\r\n    //日志\r\n    lookLog(row) {\r\n      const {logModelRef} = this.$refs;\r\n      if (logModelRef) {\r\n        logModelRef.setDefault(row.id, this.refType);\r\n      }\r\n      this.logVisible = true;\r\n    },\r\n    getLogRefType() {\r\n      WhAddress.getLogRefType().then(res => {\r\n        if (res['code'] === 0) {\r\n          this.refType = res.data;\r\n        }\r\n      })\r\n    },\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\">\r\n.widthClass {\r\n  width: 350px\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;AAiFA,OAAAA,SAAA;AACA,OAAAC,SAAA;AACA,OAAAC,QAAA;AACA,SAAAC,QAAA,EAAAC,MAAA;AAEA;EACAC,UAAA;IAAAH,QAAA,EAAAA;EAAA;EACAI,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,MAAA;MACAC,KAAA;MACAC,QAAA;MACAC,QAAA;MACAC,UAAA;MACAC,OAAA;MACAC,KAAA;MACAC,UAAA;QAAAC,MAAA;QAAAC,OAAA;QAAAC,OAAA;MAAA;MACAC,QAAA;QAAAC,KAAA;QAAAC,IAAA;QAAAC,KAAA;MAAA;MACAC,SAAA;QACAC,MAAA;QACAC,IAAA;QACAC,aAAA,cAAAvB,QAAA;MACA;MACAwB,SAAA,EAAAvB,MAAA;MACAwB,IAAA;QAAAZ,MAAA;QAAAC,OAAA;QAAAY,QAAA;QAAAC,QAAA;QAAAZ,OAAA;MAAA;MACAZ,IAAA;MACAyB,MAAA,GACA;QAAAjB,KAAA;QAAAkB,GAAA;QAAAC,QAAA;QAAAC,KAAA;MAAA,GACA;QAAApB,KAAA;QAAAkB,GAAA;QAAAC,QAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAArB,KAAA;QAAAkB,GAAA;QAAAC,QAAA;QAAAC,KAAA;MAAA,GACA;QAAApB,KAAA;QAAAkB,GAAA;QAAAC,QAAA;QAAAC,KAAA;MAAA,GACA;QAAApB,KAAA;QAAAkB,GAAA;QAAAC,QAAA;QAAAC,KAAA;MAAA,GACA;QAAApB,KAAA;QAAAkB,GAAA;QAAAI,KAAA;QAAAF,KAAA;QAAAC,IAAA;MAAA;MACAE,WAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,YAAA;IACA,KAAAC,cAAA;IACA,KAAAC,aAAA;EACA;EACAC,OAAA;IACAH,YAAA,WAAAA,aAAA;MAAA,IAAAI,KAAA;MACA,KAAApC,OAAA;MACA,IAAAqC,MAAA,GAAAC,aAAA,CAAAA,aAAA,UAAA9B,UAAA,QAAAI,QAAA;MACAlB,SAAA,CAAA6C,QAAA,CAAAF,MAAA,EAAAG,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA;UACAL,KAAA,CAAArC,IAAA,GAAA0C,GAAA,CAAA1C,IAAA,CAAA2C,OAAA;UACAN,KAAA,CAAAxB,QAAA,CAAAC,KAAA,GAAA8B,MAAA,CAAAF,GAAA,CAAA1C,IAAA,CAAAc,KAAA;QACA;MACA,GAAA+B,OAAA;QACAR,KAAA,CAAApC,OAAA;MACA;IACA;IACA6C,WAAA,WAAAA,YAAA;MACA,KAAAC,KAAA,eAAAC,WAAA;IACA;IACAC,mBAAA,WAAAA,oBAAAP,GAAA;MACA,KAAAK,KAAA,kBAAAG,UAAA;MACA,IAAAR,GAAA;QACA,KAAAS,QAAA,CAAAC,OAAA;QACA,KAAAnB,YAAA;MACA;QACA,KAAAkB,QAAA,CAAAE,OAAA,CAAAX,GAAA;MACA;IACA;IACAY,uBAAA,WAAAA,wBAAAC,IAAA;MACA;MACA,KAAAC,MAAA,CAAAC,KAAA;QACAjD,KAAA;QACAkD,OAAA,UAAAH,IAAA,CAAAI,IAAA;QACAC,MAAA;MACA;IACA;IACAC,iBAAA,WAAAA,kBAAAC,GAAA,EAAAP,IAAA;MACA,KAAAJ,QAAA,CAAAE,OAAA,CAAAE,IAAA,CAAAQ,OAAA;IACA;IACAC,aAAA,WAAAA,cAAA;MACA,KAAAb,QAAA,CAAAE,OAAA;IACA;IACAY,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,IAAA5B,MAAA,GAAAC,aAAA,UAAA9B,UAAA;MACA6B,MAAA,4BAAA6B,IAAA,GAAAC,eAAA;MACA,KAAAnE,OAAA;MACAN,SAAA,CAAA0E,QAAA,CAAA/B,MAAA;QACA4B,MAAA,CAAAjE,OAAA;MACA;IACA;IACAqE,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAhB,MAAA,CAAAiB,OAAA;QACAjE,KAAA;QACAkD,OAAA;QACAgB,IAAA,WAAAA,KAAA;UACA/E,SAAA,CAAAgF,MAAA;YAAAC,EAAA,EAAAL,GAAA,CAAAK;UAAA,GAAAnC,IAAA,WAAAC,GAAA;YACA,IAAAA,GAAA;cACA8B,MAAA,CAAArB,QAAA,CAAAC,OAAA;cACAoB,MAAA,CAAAvC,YAAA;YACA;UACA;QACA;MACA;IACA;IACA4C,aAAA,WAAAA,cAAAN,GAAA;MACA,KAAA/D,KAAA;MACA,KAAAL,KAAA;MACA,KAAAE,QAAA;MACA,KAAAyE,SAAA;MACA,KAAAxD,IAAA,GAAAyD,MAAA,CAAAC,MAAA,KAAAT,GAAA;IACA;IACAU,YAAA,WAAAA,aAAA;MACA,KAAAzE,KAAA;MACA,KAAAL,KAAA;MACA,KAAAE,QAAA;MACA,KAAAyE,SAAA;IACA;IACAI,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACA,KAAApC,KAAA,SAAAqC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAF,MAAA,CAAAjF,MAAA;UACAP,SAAA,CAAAuF,aAAA,CAAAC,MAAA,CAAA7D,IAAA,EAAAmB,IAAA,WAAAC,GAAA;YACA,IAAAA,GAAA;cACAyC,MAAA,CAAAjF,MAAA;cACAiF,MAAA,CAAAhC,QAAA,CAAAC,OAAA;cACA+B,MAAA,CAAAL,SAAA;cACAK,MAAA,CAAAhF,KAAA;cACAgF,MAAA,CAAAlD,YAAA;YACA;UACA,GAAAY,OAAA;YACAsC,MAAA,CAAAjF,MAAA;UACA;QACA;MACA;IACA;IACA;IACAgC,cAAA,WAAAA,eAAA;MAAA,IAAAoD,MAAA;MACA5F,SAAA,CAAA6F,qBAAA,iBAAA9C,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,IAAAA,GAAA;UACA,IAAA1C,IAAA,GAAA0C,GAAA;UACA,IAAA1C,IAAA;YACAsF,MAAA,CAAAvD,WAAA,GAAA/B,IAAA,CAAAwF,GAAA,WAAAC,IAAA;cAAA,OAAAC,IAAA,CAAAC,KAAA,CAAAF,IAAA,CAAAG,KAAA;YAAA;UACA;QACA;MACA;IACA;IACAC,UAAA,WAAAA,WAAA9E,IAAA;MACA,KAAAF,QAAA,CAAAE,IAAA,GAAAA,IAAA;MACA,KAAAkB,YAAA;IACA;IACA6D,cAAA,WAAAA,eAAAC,IAAA;MACA,KAAAlF,QAAA,CAAAE,IAAA;MACA,KAAAF,QAAA,CAAAG,KAAA,GAAA+E,IAAA;MACA,KAAA9D,YAAA;IACA;IACA+D,UAAA,WAAAA,WAAA;MACA,KAAA7F,KAAA;MACA,KAAA2E,SAAA;IACA;IACAA,SAAA,WAAAA,UAAA;MACA,KAAA/B,KAAA,SAAAC,WAAA;MACA,KAAA1B,IAAA;IACA;IACA;IACA2E,OAAA,WAAAA,QAAA1B,GAAA;MACA,IAAA2B,WAAA,QAAAnD,KAAA,CAAAmD,WAAA;MACA,IAAAA,WAAA;QACAA,WAAA,CAAAC,UAAA,CAAA5B,GAAA,CAAAK,EAAA,OAAArE,OAAA;MACA;MACA,KAAAD,UAAA;IACA;IACA6B,aAAA,WAAAA,cAAA;MAAA,IAAAiE,MAAA;MACAzG,SAAA,CAAAwC,aAAA,GAAAM,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA;UACA0D,MAAA,CAAA7F,OAAA,GAAAmC,GAAA,CAAA1C,IAAA;QACA;MACA;IACA;EACA;AACA"}]}