server:
    port: 8233
    servlet:
        session:
            cookie:
                name: OAUTH2SESSION
spring:
    application:
        name: aimo-base-server
    cloud:
        #手动配置Bus id,
        bus:
            id: ${spring.application.name}:${server.port}

        nacos:
            config:
                enabled: true
                username: nacos
                password: sy@nacos2022
                file-extension: properties
                shared-configs[0]:
                    data-id: common.properties
                    refresh: true
                    group: DEFAULT_GROUP
                shared-configs[1]:
                    data-id: db.properties
                    refresh: true
                    group: DEFAULT_GROUP
                shared-configs[2]:
                    data-id: redis.properties
                    refresh: true
                    group: DEFAULT_GROUP
                shared-configs[3]:
                    data-id: rabbitmq.properties
                    refresh: true
                    group: DEFAULT_GROUP
                shared-configs[4]:
                    data-id: enterpriseWeChat.properties
                    refresh: true
                    group: DEFAULT_GROUP
    main:
        allow-bean-definition-overriding: true
    #解决restful 404错误 spring.mvc.throw-exception-if-no-handler-found=true spring.resources.add-mappings=false
    mvc:
        throw-exception-if-no-handler-found: true
    resources:
        add-mappings: false
    profiles:
        active: local
      # 文件上传限制
    servlet:
        multipart:
            max-file-size: 10MB
            max-request-size: 10MB
    thymeleaf:
        cache: false
        encoding: UTF-8
        mode: LEGACYHTML5
        prefix: classpath:/templates/
        suffix: .html

management:
    endpoints:
        web:
            exposure:
                include: '*'

# Feign 和 Hystrix 配置
feign:
    hystrix:
        enabled: true
    compression:
        request:
            enabled: false
        response:
            enabled: false
    client:
        config:
            default:
                connectTimeout: 10000
                readTimeout: 30000
            # 为 ERP 工作流服务配置更长的超时时间
            sy-erp-server:
                connectTimeout: 15000
                readTimeout: 60000

# Hystrix 配置
hystrix:
    command:
        default:
            execution:
                isolation:
                    thread:
                        timeoutInMilliseconds: 30000
            circuitBreaker:
                enabled: true
                requestVolumeThreshold: 20
                sleepWindowInMilliseconds: 5000
                errorThresholdPercentage: 50
        # 为工作流相关操作配置更长的超时时间
        ErpWorkflowClient#completeTask(String,Map):
            execution:
                isolation:
                    thread:
                        timeoutInMilliseconds: 60000
        ErpWorkflowClient#startProcess(String,String,Map):
            execution:
                isolation:
                    thread:
                        timeoutInMilliseconds: 45000
aimo:
    swagger2:
        enabled: true
        description: 平台用户认证服务器
        title: 平台用户认证服务器
    client:
        oauth2:
            admin:
                client-id: 7gBZcbsC7kLIWCdELIl8nxcs
                client-secret: 0osTIhce7uPvDKHz6aa67bhCukaKoYl4

#mybatis plus 设置
mybatis-plus:
 #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.aimo.base.client.**.entity
  mapper-locations: classpath:mapper/*.xml,classpath:mapper/*/*.xml,classpath:mapper/*/*/*.xml

fdfs:
    so-timeout: 1500 #上传的超时时间
    connect-timeout: 600 #连接超时时间
    thumb-image:             #缩略图生成参数
        width: 100
        height: 100
    tracker-list: ************:22122           #TrackerList参数,支持多个
