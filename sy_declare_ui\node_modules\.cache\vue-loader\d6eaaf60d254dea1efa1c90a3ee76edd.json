{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\base\\workflow\\index.vue?vue&type=style&index=0&id=26384284&lang=less&scoped=true&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\base\\workflow\\index.vue", "mtime": 1754360322245}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1752744819993}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAi+BA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/view/module/base/workflow", "sourcesContent": ["<template>\r\n  <div class=\"workflow-list\">\r\n    <Card>\r\n      <!-- 搜索区域 -->\r\n      <Form ref=\"searchForm\" :model=\"searchForm\" inline class=\"search-form\">\r\n        <FormItem>\r\n          <Input\r\n            v-model=\"searchForm.workflowName\"\r\n            placeholder=\"请输入工作流名称\"\r\n            style=\"width: 200px\"\r\n            @on-enter=\"handleSearch\"\r\n          />\r\n        </FormItem>\r\n        <FormItem>\r\n          <Select\r\n            v-model=\"searchForm.execute\"\r\n            placeholder=\"请选择状态\"\r\n            style=\"width: 120px\"\r\n            clearable\r\n          >\r\n            <Option :value=\"1\">启用</Option>\r\n            <Option :value=\"0\">停用</Option>\r\n          </Select>\r\n        </FormItem>\r\n        <FormItem>\r\n          <Button type=\"primary\" @click=\"handleSearch\">查询</Button>\r\n          <Button @click=\"handleReset\" style=\"margin-left: 8px\">重置</Button>\r\n        </FormItem>\r\n      </Form>\r\n\r\n      <!-- 操作按钮 -->\r\n      <div class=\"action-buttons\">\r\n        <Button type=\"primary\" icon=\"md-add\" @click=\"handleAdd\">新增流程</Button>\r\n        <div class=\"right-buttons\">\r\n          <Button type=\"warning\" icon=\"md-rocket\" @click=\"handleElementManage\" style=\"margin-right: 8px\">上新字段管理</Button>\r\n          <Button type=\"success\" icon=\"md-settings\" @click=\"handleFunctionManage\">流程功能</Button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 数据表格 -->\r\n      <Table\r\n        :columns=\"columns\"\r\n        :data=\"tableData\"\r\n        :loading=\"loading\"\r\n        stripe\r\n        :max-height=\"600\"\r\n      >\r\n        <template v-slot:status=\"{ row }\">\r\n          <Badge v-if=\"row.execute === 1\" status=\"success\" text=\"启用\" />\r\n          <Badge v-else status=\"error\" text=\"停用\" />\r\n        </template>\r\n\r\n        <template v-slot:deployStatus=\"{ row }\">\r\n          <Badge v-if=\"row.deployStatus === 1\" status=\"success\" text=\"已部署\" />\r\n          <Badge v-else status=\"error\" text=\"未部署\" />\r\n        </template>\r\n\r\n        <template v-slot:nodeCount=\"{ row }\">\r\n          <Tag color=\"blue\">{{ row.nodeCount || 0 }}个节点</Tag>\r\n        </template>\r\n\r\n        <template v-slot:action=\"{ row }\">\r\n          <Button v-if=\"row.deployStatus === 0\" type=\"error\" size=\"small\" @click=\"handleDeploy(row)\" style=\"margin-right: 4px;\">部署</Button>\r\n          <Button type=\"primary\" size=\"small\" @click=\"handleEdit(row)\" style=\"margin-right: 4px;\">编辑</Button>\r\n          <Button\r\n            :type=\"row.execute === 1 ? 'warning' : 'success'\"\r\n            size=\"small\"\r\n            @click=\"handleToggleStatus(row)\"\r\n            style=\"margin-right: 4px;\"\r\n          >\r\n            {{ row.execute === 1 ? '停用' : '启用' }}\r\n          </Button>\r\n          <Button type=\"error\" size=\"small\" @click=\"handleDelete(row)\">删除</Button>\r\n        </template>\r\n      </Table>\r\n\r\n      <!-- 分页 -->\r\n      <div class=\"pagination-wrapper\">\r\n        <Page\r\n          :total=\"pageInfo.total\"\r\n          :current=\"pageInfo.page\"\r\n          :page-size=\"pageInfo.limit\"\r\n          show-elevator\r\n          show-sizer\r\n          show-total\r\n          @on-change=\"handlePageChange\"\r\n          @on-page-size-change=\"handlePageSizeChange\"\r\n        />\r\n      </div>\r\n    </Card>\r\n\r\n    <!-- 上新字段管理弹窗 -->\r\n    <Modal\r\n      v-model=\"elementManageModal\"\r\n      title=\"上新字段管理\"\r\n      width=\"800\"\r\n      :mask-closable=\"false\"\r\n      @on-cancel=\"handleElementModalCancel\"\r\n    >\r\n      <div class=\"element-manage-content\">\r\n        <!-- 分类标签 -->\r\n        <Tabs v-model=\"activeElementType\" @on-click=\"handleElementTypeChange\">\r\n          <TabPane label=\"基础信息字段\" name=\"1\">\r\n            <div class=\"element-list\">\r\n              <!-- 添加按钮 -->\r\n              <div class=\"add-element-btn\">\r\n                <Button type=\"dashed\" icon=\"md-add\" @click=\"handleAddElement\" long>\r\n                  添加基础信息字段\r\n                </Button>\r\n              </div>\r\n              <!-- 字段列表 -->\r\n              <Table\r\n                :columns=\"elementColumns\"\r\n                :data=\"basicElementList\"\r\n                :loading=\"false\"\r\n                size=\"small\"\r\n                stripe\r\n              ></Table>\r\n            </div>\r\n          </TabPane>\r\n          <TabPane label=\"标准信息字段\" name=\"2\">\r\n            <div class=\"element-list\">\r\n              <!-- 添加按钮 -->\r\n              <div class=\"add-element-btn\">\r\n                <Button type=\"dashed\" icon=\"md-add\" @click=\"handleAddElement\" long>\r\n                  添加标准信息字段\r\n                </Button>\r\n              </div>\r\n              <!-- 字段列表 -->\r\n              <Table\r\n                :columns=\"elementColumns\"\r\n                :data=\"standardElementList\"\r\n                :loading=\"false\"\r\n                size=\"small\"\r\n                stripe\r\n              ></Table>\r\n            </div>\r\n          </TabPane>\r\n        </Tabs>\r\n      </div>\r\n      <div slot=\"footer\">\r\n        <Button @click=\"handleElementModalCancel\">关闭</Button>\r\n      </div>\r\n    </Modal>\r\n\r\n    <!-- 添加/编辑字段弹窗 -->\r\n    <Modal\r\n      v-model=\"elementFormModal\"\r\n      :title=\"elementFormTitle\"\r\n      width=\"500\"\r\n      :mask-closable=\"false\"\r\n      @on-cancel=\"handleElementFormCancel\"\r\n    >\r\n      <Form ref=\"elementForm\" :model=\"elementForm\" :rules=\"elementFormRules\" :label-width=\"100\">\r\n        <FormItem label=\"字段名称\" prop=\"name\">\r\n          <Input v-model=\"elementForm.name\" placeholder=\"请输入字段名称\" />\r\n        </FormItem>\r\n        <FormItem label=\"字段英文\" prop=\"element\">\r\n          <Input v-model=\"elementForm.element\" placeholder=\"请输入字段英文标识\" />\r\n        </FormItem>\r\n        <FormItem label=\"字段类型\" prop=\"type\">\r\n          <RadioGroup v-model=\"elementForm.type\">\r\n            <Radio :label=\"1\">基础信息</Radio>\r\n            <Radio :label=\"2\">标准信息</Radio>\r\n          </RadioGroup>\r\n        </FormItem>\r\n      </Form>\r\n      <div slot=\"footer\">\r\n        <Button @click=\"handleElementFormCancel\">取消</Button>\r\n        <Button type=\"primary\" :loading=\"elementFormLoading\" @click=\"handleElementFormSubmit\">\r\n          {{ elementForm.id ? '更新' : '添加' }}\r\n        </Button>\r\n      </div>\r\n    </Modal>\r\n\r\n    <!-- 流程功能管理弹窗 -->\r\n    <Modal v-model=\"functionModalVisible\" title=\"流程功能管理\" width=\"80%\" :mask-closable=\"false\">\r\n      <div class=\"function-manage\">\r\n        <!-- 功能操作按钮 -->\r\n        <div class=\"function-actions\">\r\n          <Button type=\"primary\" icon=\"md-add\" @click=\"handleFunctionAdd\" style=\"margin-right: 4px;\">新增功能</Button>\r\n          <Button icon=\"md-refresh\" @click=\"loadFunctionData\">刷新</Button>\r\n        </div>\r\n\r\n        <!-- 功能列表表格 -->\r\n        <Table\r\n          :columns=\"functionColumns\"\r\n          :data=\"functionData\"\r\n          :loading=\"functionLoading\"\r\n          stripe\r\n          :max-height=\"400\"\r\n          style=\"margin-top: 16px;\"\r\n        >\r\n          <template v-slot:status=\"{ row }\">\r\n            <Badge v-if=\"row.status === 0\" status=\"success\" text=\"正常\" />\r\n            <Badge v-else status=\"error\" text=\"删除\" />\r\n          </template>\r\n\r\n          <template v-slot:action=\"{ row }\">\r\n            <Button type=\"primary\" size=\"small\" @click=\"handleFunctionEdit(row)\" style=\"margin-right: 4px;\">编辑</Button>\r\n            <Button type=\"error\" size=\"small\" @click=\"handleFunctionDelete(row)\">删除</Button>\r\n          </template>\r\n        </Table>\r\n      </div>\r\n\r\n      <div slot=\"footer\">\r\n        <Button @click=\"functionModalVisible = false\">关闭</Button>\r\n      </div>\r\n    </Modal>\r\n\r\n    <!-- 功能编辑弹窗 -->\r\n    <Modal v-model=\"functionEditModalVisible\" :title=\"functionEditMode === 'add' ? '新增功能' : '编辑功能'\" @on-ok=\"handleFunctionSave\">\r\n      <Form :model=\"functionForm\" :rules=\"functionRules\" ref=\"functionForm\" :label-width=\"80\">\r\n        <FormItem label=\"功能KEY\" prop=\"key\">\r\n          <Input v-model=\"functionForm.key\" placeholder=\"请输入功能KEY\" :disabled=\"functionEditMode === 'edit'\" />\r\n        </FormItem>\r\n        <FormItem label=\"功能名称\" prop=\"name\">\r\n          <Input v-model=\"functionForm.name\" placeholder=\"请输入功能名称\" />\r\n        </FormItem>\r\n      </Form>\r\n    </Modal>\r\n\r\n    <!-- BPMN XML查看弹窗 -->\r\n    <Modal v-model=\"bpmnViewModalVisible\" title=\"BPMN XML内容\" width=\"80%\" :mask-closable=\"false\">\r\n      <div class=\"bpmn-xml-content\">\r\n        <div class=\"xml-header\">\r\n          <span class=\"workflow-name\">{{ currentBpmnWorkflow.workflowName }}</span>\r\n          <Button type=\"primary\" size=\"small\" @click=\"copyBpmnXml\" style=\"float: right;\">\r\n            <Icon type=\"md-copy\" />\r\n            复制XML\r\n          </Button>\r\n        </div>\r\n        <div class=\"xml-viewer\">\r\n          <pre><code>{{ currentBpmnXml }}</code></pre>\r\n        </div>\r\n      </div>\r\n      <div slot=\"footer\">\r\n        <Button @click=\"bpmnViewModalVisible = false\">关闭</Button>\r\n      </div>\r\n    </Modal>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport workflowApi from '@/api/base/workflow'\r\nimport funElementApi from '@/api/base/funElement'\r\n\r\nexport default {\r\n  name: 'WorkflowList',\r\n  data() {\r\n    return {\r\n      // 搜索表单\r\n      searchForm: {\r\n        workflowName: '',\r\n        execute: null\r\n      },\r\n\r\n      // 表格列定义\r\n      columns: [\r\n        {\r\n          title: '序号',\r\n          type: 'index',\r\n          width: 50,\r\n          align: 'center'\r\n        },\r\n        {\r\n          title: '工作流Key',\r\n          key: 'workflowKey',\r\n          width: 150,\r\n          align: 'center'\r\n        },\r\n        {\r\n          title: '工作流名称',\r\n          key: 'workflowName',\r\n          minWidth: 150\r\n        },\r\n        {\r\n          title: 'BPMN文件',\r\n          key: 'bpmnXml',\r\n          width: 120,\r\n          align: 'center',\r\n          render: (h, params) => {\r\n            if (params.row.bpmnXml) {\r\n              return h('Button', {\r\n                props: {\r\n                  type: 'text',\r\n                  size: 'small'\r\n                },\r\n                on: {\r\n                  click: () => {\r\n                    this.handleViewBpmn(params.row)\r\n                  }\r\n                }\r\n              }, '查看文件')\r\n            } else {\r\n              return h('span', '无文件')\r\n            }\r\n          }\r\n        },\r\n        {\r\n          title: '状态',\r\n          key: 'execute',\r\n          width: 80,\r\n          align: 'center',\r\n          slot: 'status'\r\n        },\r\n        {\r\n          title: '部署状态',\r\n          key: 'deployStatus',\r\n          width: 150,\r\n          align: 'center',\r\n          slot: 'deployStatus'\r\n        },\r\n        // {\r\n        //   title: '创建人',\r\n        //   key: 'createUserName',\r\n        //   width: 140,\r\n        //   align: 'center'\r\n        // },\r\n        // {\r\n        //   title: '更新人',\r\n        //   key: 'updateUserName',\r\n        //   width: 140,\r\n        //   align: 'center'\r\n        // },\r\n        {\r\n          title: '创建时间',\r\n          key: 'createTime',\r\n          width: 160,\r\n          align: 'center'\r\n        },\r\n        {\r\n          title: '更新时间',\r\n          key: 'updateTime',\r\n          width: 160,\r\n          align: 'center'\r\n        },\r\n        {\r\n          title: '操作',\r\n          key: 'action',\r\n          width: 280,\r\n          align: 'center',\r\n          slot: 'action'\r\n        }\r\n      ],\r\n\r\n      // 表格数据\r\n      tableData: [],\r\n\r\n      // 加载状态\r\n      loading: false,\r\n\r\n      // 分页信息\r\n      pageInfo: {\r\n        page: 1,\r\n        limit: 10,\r\n        total: 0\r\n      },\r\n      currentWorkflow: {},\r\n\r\n      // BPMN查看相关\r\n      bpmnViewModalVisible: false,\r\n      currentBpmnWorkflow: {},\r\n      currentBpmnXml: '',\r\n\r\n      // 上新字段管理相关数据\r\n      elementManageModal: false,\r\n      activeElementType: '1', // 1: 基础信息, 2: 标准信息\r\n      basicElementList: [],\r\n      standardElementList: [],\r\n      elementFormModal: false,\r\n      elementFormLoading: false,\r\n      elementForm: {\r\n        id: null,\r\n        name: '',\r\n        element: '',\r\n        type: 1,\r\n        funType: 10001 // 上新功能类型枚举\r\n      },\r\n      // 表格列定义\r\n      elementColumns: [\r\n        {\r\n          title: '序号',\r\n          type: 'index',\r\n          width: 60,\r\n          align: 'center'\r\n        },\r\n        {\r\n          title: '字段名称',\r\n          key: 'name',\r\n          minWidth: 120\r\n        },\r\n        {\r\n          title: '字段英文',\r\n          key: 'element',\r\n          minWidth: 150,\r\n          render: (h, params) => {\r\n            return h('code', {\r\n              style: {\r\n                background: '#f5f5f5',\r\n                padding: '2px 6px',\r\n                borderRadius: '3px',\r\n                fontSize: '12px',\r\n                fontFamily: 'Courier New, monospace'\r\n              }\r\n            }, params.row.element)\r\n          }\r\n        },\r\n        {\r\n          title: '创建时间',\r\n          key: 'createTime',\r\n          width: 150,\r\n          render: (h, params) => {\r\n            return h('span', params.row.createTime ? params.row.createTime : '-')\r\n          }\r\n        },\r\n        {\r\n          title: '操作',\r\n          key: 'action',\r\n          width: 200,\r\n          align: 'center',\r\n          render: (h, params) => {\r\n            return h('div', [\r\n              h('Button', {\r\n                props: {\r\n                  type: 'primary',\r\n                  size: 'small'\r\n                },\r\n                style: {\r\n                  marginRight: '5px'\r\n                },\r\n                on: {\r\n                  click: () => {\r\n                    this.handleEditElement(params.row)\r\n                  }\r\n                }\r\n              }, [\r\n                '编辑'\r\n              ]),\r\n              h('Button', {\r\n                props: {\r\n                  type: 'error',\r\n                  size: 'small'\r\n                },\r\n                on: {\r\n                  click: () => {\r\n                    this.handleDeleteElement(params.row)\r\n                  }\r\n                }\r\n              }, [\r\n                '删除'\r\n              ])\r\n            ])\r\n          }\r\n        }\r\n      ],\r\n      elementFormRules: {\r\n        name: [\r\n          { required: true, message: '请输入字段名称', trigger: 'blur' }\r\n        ],\r\n        element: [\r\n          { required: true, message: '请输入字段英文标识', trigger: 'blur' },\r\n          { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '字段英文必须以字母开头，只能包含字母、数字和下划线', trigger: 'blur' }\r\n        ],\r\n        type: [\r\n          { required: true, type: 'number', message: '请选择字段类型', trigger: 'change' }\r\n        ]\r\n      },\r\n\r\n      // 流程功能管理相关\r\n      functionModalVisible: false,\r\n      functionEditModalVisible: false,\r\n      functionEditMode: 'add', // add | edit\r\n      functionLoading: false,\r\n      functionData: [],\r\n      functionForm: {\r\n        id: null,\r\n        key: '',\r\n        name: ''\r\n      },\r\n      functionRules: {\r\n        key: [\r\n          { required: true, message: '请输入功能KEY', trigger: 'blur' },\r\n          { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '功能KEY必须以字母开头，只能包含字母、数字和下划线', trigger: 'blur' }\r\n        ],\r\n        name: [\r\n          { required: true, message: '请输入功能名称', trigger: 'blur' }\r\n        ]\r\n      },\r\n      functionColumns: [\r\n        {\r\n          title: '序号',\r\n          type: 'index',\r\n          width: 60,\r\n          align: 'center'\r\n        },\r\n        {\r\n          title: '功能KEY',\r\n          key: 'key',\r\n          minWidth: 150\r\n        },\r\n        {\r\n          title: '功能名称',\r\n          key: 'name',\r\n          minWidth: 150\r\n        },\r\n        // {\r\n        //   title: '创建人',\r\n        //   key: 'createUserName',\r\n        //   width: 140,\r\n        //   align: 'center'\r\n        // },\r\n        // {\r\n        //   title: '更新人',\r\n        //   key: 'updateUserName',\r\n        //   width: 140,\r\n        //   align: 'center'\r\n        // },\r\n        {\r\n          title: '创建时间',\r\n          key: 'createTime',\r\n          width: 160,\r\n          align: 'center'\r\n        },\r\n        {\r\n          title: '更新时间',\r\n          key: 'updateTime',\r\n          width: 160,\r\n          align: 'center'\r\n        },\r\n        {\r\n          title: '操作',\r\n          key: 'action',\r\n          width: 150,\r\n          align: 'center',\r\n          slot: 'action'\r\n        }\r\n      ]\r\n    }\r\n  },\r\n\r\n  computed: {\r\n    // 表单标题\r\n    elementFormTitle() {\r\n      return this.elementForm.id ? '编辑字段' : '添加字段'\r\n    }\r\n  },\r\n\r\n  mounted() {\r\n    this.loadTableData()\r\n  },\r\n\r\n  methods: {\r\n    // 加载表格数据\r\n    async loadTableData() {\r\n      this.loading = true\r\n      try {\r\n        const params = {\r\n          page: this.pageInfo.page,\r\n          size: this.pageInfo.limit,\r\n          workflowName: this.searchForm.workflowName,\r\n          execute: this.searchForm.execute\r\n        }\r\n\r\n        const response = await workflowApi.getWorkflowPage(params)\r\n        if (response.code === 0) {\r\n          this.tableData = response.data.records || []\r\n          this.pageInfo.total = Number(response.data.total) || 0\r\n        } else {\r\n          this.$Message.error(response.message || '获取工作流列表失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取工作流列表失败:', error)\r\n        this.$Message.error('获取工作流列表失败')\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    // 搜索\r\n    handleSearch() {\r\n      console.log('搜索条件:', this.searchForm)\r\n      this.pageInfo.page = 1\r\n      this.loadTableData()\r\n    },\r\n\r\n    // 重置搜索\r\n    handleReset() {\r\n      this.searchForm = {\r\n        workflowName: '',\r\n        execute: null\r\n      }\r\n      this.handleSearch()\r\n    },\r\n\r\n    // 刷新\r\n    handleRefresh() {\r\n      this.loadTableData()\r\n    },\r\n\r\n    // 新增流程\r\n    handleAdd() {\r\n      this.$router.push('/base/workflow/add')\r\n    },\r\n\r\n    // 查看BPMN文件\r\n    handleViewBpmn(row) {\r\n      if (row.bpmnXml) {\r\n        this.currentBpmnWorkflow = row\r\n        this.currentBpmnXml = row.bpmnXml\r\n        this.bpmnViewModalVisible = true\r\n      } else {\r\n        this.$Message.warning('该工作流暂无BPMN文件')\r\n      }\r\n    },\r\n\r\n    // 复制BPMN XML内容\r\n    copyBpmnXml() {\r\n      if (navigator.clipboard && navigator.clipboard.writeText) {\r\n        navigator.clipboard.writeText(this.currentBpmnXml).then(() => {\r\n          this.$Message.success('BPMN XML已复制到剪贴板')\r\n        }).catch(() => {\r\n          this.$Message.error('复制失败，请手动复制')\r\n        })\r\n      } else {\r\n        // 兼容旧浏览器\r\n        const textArea = document.createElement('textarea')\r\n        textArea.value = this.currentBpmnXml\r\n        document.body.appendChild(textArea)\r\n        textArea.select()\r\n        try {\r\n          document.execCommand('copy')\r\n          this.$Message.success('BPMN XML已复制到剪贴板')\r\n        } catch (err) {\r\n          this.$Message.error('复制失败，请手动复制')\r\n        }\r\n        document.body.removeChild(textArea)\r\n      }\r\n    },\r\n\r\n    // 编辑工作流定义\r\n    handleEdit(row) {\r\n      this.$router.push(`/base/workflow/add/${row.id}`)\r\n    },\r\n\r\n    // 删除工作流定义\r\n    handleDelete(row) {\r\n      this.$Modal.confirm({\r\n        title: '确认删除',\r\n        content: `确定要删除\"${row.workflowName}\"工作流吗？删除后不可恢复。`,\r\n        onOk: async () => {\r\n          try {\r\n            const response = await workflowApi.deleteWorkflow(row)\r\n            if (response.code === 0) {\r\n              this.$Message.success(response.message || '工作流删除成功')\r\n              // 重新加载数据\r\n              this.loadTableData()\r\n            } else {\r\n              this.$Message.error(response.message || '删除失败')\r\n            }\r\n          } catch (error) {\r\n            console.error('删除工作流失败:', error)\r\n            this.$Message.error('删除失败，请重试')\r\n          }\r\n        }\r\n      })\r\n    },\r\n\r\n    // 切换启用/停用状态\r\n    async handleToggleStatus(row) {\r\n      try {\r\n        const newStatus = row.execute === 1 ? 0 : 1\r\n        const statusText = newStatus === 1 ? '启用' : '停用'\r\n\r\n        const updateData = {\r\n          id: row.id,\r\n          execute: newStatus\r\n        }\r\n\r\n        const response = await workflowApi.updateWorkflow(updateData)\r\n        if (response.code === 0) {\r\n          this.$Message.success(`${statusText}成功`)\r\n          // 更新本地数据\r\n          row.execute = newStatus\r\n        } else {\r\n          this.$Message.error(response.message || `${statusText}失败`)\r\n        }\r\n      } catch (error) {\r\n        console.error('切换状态失败:', error)\r\n        this.$Message.error('操作失败，请重试')\r\n      }\r\n    },\r\n\r\n    // ========== 上新字段管理相关方法 ==========\r\n\r\n    // 上新字段管理\r\n    handleElementManage() {\r\n      this.elementManageModal = true\r\n      this.activeElementType = '1'\r\n      this.loadElementData()\r\n    },\r\n\r\n    // 加载字段数据\r\n    async loadElementData() {\r\n      try {\r\n        const response = await funElementApi.getList({\r\n          funType: 10001 // 上新功能类型\r\n        })\r\n\r\n        if (response.code === 0) {\r\n          const allElements = response.data || []\r\n          // 按类型分组\r\n          this.basicElementList = allElements.filter(item => item.type === 1)\r\n          this.standardElementList = allElements.filter(item => item.type === 2)\r\n        } else {\r\n          this.$Message.error(response.message || '加载字段数据失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('加载字段数据失败:', error)\r\n        this.$Message.error('加载字段数据失败')\r\n      }\r\n    },\r\n\r\n    // 切换字段类型\r\n    handleElementTypeChange(name) {\r\n      this.activeElementType = name\r\n    },\r\n\r\n    // 添加字段\r\n    handleAddElement() {\r\n      this.elementForm = {\r\n        id: null,\r\n        name: '',\r\n        element: '',\r\n        type: parseInt(this.activeElementType),\r\n        funType: 10001\r\n      }\r\n      this.elementFormModal = true\r\n\r\n      // 清除表单验证状态\r\n      this.$nextTick(() => {\r\n        if (this.$refs.elementForm) {\r\n          this.$refs.elementForm.clearValidate()\r\n        }\r\n      })\r\n    },\r\n\r\n    // 编辑字段\r\n    handleEditElement(item) {\r\n      this.elementForm = {\r\n        id: item.id,\r\n        name: item.name,\r\n        element: item.element,\r\n        type: item.type,\r\n        funType: item.funType\r\n      }\r\n      this.elementFormModal = true\r\n\r\n      // 清除表单验证状态\r\n      this.$nextTick(() => {\r\n        if (this.$refs.elementForm) {\r\n          this.$refs.elementForm.clearValidate()\r\n        }\r\n      })\r\n    },\r\n\r\n    // 删除字段\r\n    handleDeleteElement(item) {\r\n      this.$Modal.confirm({\r\n        title: '确认删除',\r\n        content: `确定要删除字段\"${item.name}\"吗？`,\r\n        onOk: async () => {\r\n          try {\r\n            const response = await funElementApi.delete(item.id)\r\n            if (response.code === 0) {\r\n              this.$Message.success('删除成功')\r\n              this.loadElementData()\r\n            } else {\r\n              this.$Message.error(response.message || '删除失败')\r\n            }\r\n          } catch (error) {\r\n            console.error('删除字段失败:', error)\r\n            this.$Message.error('删除失败')\r\n          }\r\n        }\r\n      })\r\n    },\r\n\r\n    // 关闭字段管理弹窗\r\n    handleElementModalCancel() {\r\n      this.elementManageModal = false\r\n    },\r\n\r\n    // 提交字段表单\r\n    async handleElementFormSubmit() {\r\n      console.log('提交表单，当前表单数据:', this.elementForm)\r\n      this.$refs.elementForm.validate(async (valid) => {\r\n        console.log('表单验证结果:', valid)\r\n        if (valid) {\r\n          this.elementFormLoading = true\r\n          try {\r\n            const isEdit = !!this.elementForm.id\r\n            let response\r\n\r\n            if (isEdit) {\r\n              response = await funElementApi.update(this.elementForm.id, this.elementForm)\r\n            } else {\r\n              response = await funElementApi.create(this.elementForm)\r\n            }\r\n\r\n            if (response.code === 0) {\r\n              this.$Message.success(isEdit ? '更新成功' : '添加成功')\r\n              this.elementFormModal = false\r\n              this.loadElementData()\r\n            } else {\r\n              this.$Message.error(response.message || '操作失败')\r\n            }\r\n          } catch (error) {\r\n            console.error('保存字段失败:', error)\r\n            this.$Message.error('操作失败')\r\n          } finally {\r\n            this.elementFormLoading = false\r\n          }\r\n        }\r\n      })\r\n    },\r\n\r\n    // 取消字段表单\r\n    handleElementFormCancel() {\r\n      this.elementFormModal = false\r\n      // 重置表单数据和验证状态\r\n      this.$nextTick(() => {\r\n        if (this.$refs.elementForm) {\r\n          this.$refs.elementForm.resetFields()\r\n        }\r\n      })\r\n    },\r\n\r\n    // 工作流部署按钮\r\n    handleDeploy(row) {\r\n      if (!row.bpmnXml) {\r\n        this.$Message.warning('该工作流没有BPMN文件，无法部署')\r\n        return\r\n      }\r\n      this.$Modal.confirm({\r\n        title: '部署确认',\r\n        content: `确定要部署 \"${row.workflowName}\" 到工作流引擎吗？`,\r\n        onOk: async () => {\r\n          try {\r\n            this.$Message.loading('正在部署工作流...')\r\n            const response = await workflowApi.erpDeployWorkflow(row)\r\n\r\n            if (response.code === 0) {\r\n              this.$Message.success('工作流部署成功！')\r\n              console.log('部署结果:', response.data)\r\n\r\n              // 可以在这里更新表格数据，标记为已部署\r\n              this.loadTableData()\r\n            } else {\r\n              this.$Message.error(response.message || '工作流部署失败')\r\n            }\r\n          } catch (error) {\r\n            console.error('工作流部署失败:', error)\r\n            this.$Message.error('工作流部署失败: ' + (error.message || '未知错误'))\r\n          }\r\n        }\r\n      })\r\n    },\r\n\r\n    // 分页变更\r\n    handlePageChange(page) {\r\n      this.pageInfo.page = Number(page)\r\n      this.loadTableData()\r\n    },\r\n\r\n    // 页面大小变更\r\n    handlePageSizeChange(pageSize) {\r\n      this.pageInfo.limit = Number(pageSize)\r\n      this.pageInfo.page = 1\r\n      this.loadTableData()\r\n    },\r\n\r\n    // ========== 流程功能管理相关方法 ==========\r\n\r\n    // 打开流程功能管理弹窗\r\n    handleFunctionManage() {\r\n      this.functionModalVisible = true\r\n      this.loadFunctionData()\r\n    },\r\n\r\n    // 加载功能数据\r\n    async loadFunctionData() {\r\n      this.functionLoading = true\r\n\r\n      try {\r\n        const response = await workflowApi.getFunctionTypeList()\r\n        if (response.code === 0) {\r\n          this.functionData = response.data.records || []\r\n        } else {\r\n          this.$Message.error(response.message || '获取功能类型列表失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取功能类型列表失败:', error)\r\n        this.$Message.error('获取功能类型列表失败')\r\n      } finally {\r\n        this.functionLoading = false\r\n      }\r\n    },\r\n\r\n    // 新增功能\r\n    handleFunctionAdd() {\r\n      this.functionEditMode = 'add'\r\n      this.functionForm = {\r\n        id: null,\r\n        key: '',\r\n        name: ''\r\n      }\r\n      this.functionEditModalVisible = true\r\n      this.$nextTick(() => {\r\n        this.$refs.functionForm.resetFields()\r\n      })\r\n    },\r\n\r\n    // 编辑功能\r\n    handleFunctionEdit(row) {\r\n      this.functionEditMode = 'edit'\r\n      this.functionForm = {\r\n        id: row.id,\r\n        key: row.key,\r\n        name: row.name\r\n      }\r\n      this.functionEditModalVisible = true\r\n    },\r\n\r\n    // 保存功能\r\n    handleFunctionSave() {\r\n      this.$refs.functionForm.validate(async (valid) => {\r\n        if (valid) {\r\n          try {\r\n            let response\r\n            if (this.functionEditMode === 'add') {\r\n              // 新增功能\r\n              response = await workflowApi.addFunctionType(this.functionForm)\r\n            } else {\r\n              // 编辑功能\r\n              response = await workflowApi.updateFunctionType(this.functionForm)\r\n            }\r\n\r\n            if (response.code === 0) {\r\n              this.$Message.success(response.message || (this.functionEditMode === 'add' ? '功能新增成功' : '功能编辑成功'))\r\n              this.functionEditModalVisible = false\r\n              // 重新加载数据\r\n              this.loadFunctionData()\r\n            } else {\r\n              this.$Message.error(response.message || '操作失败')\r\n            }\r\n          } catch (error) {\r\n            console.error('保存功能失败:', error)\r\n            this.$Message.error('操作失败，请重试')\r\n          }\r\n        }\r\n      })\r\n    },\r\n\r\n    // 删除功能\r\n    handleFunctionDelete(row) {\r\n      this.$Modal.confirm({\r\n        title: '确认删除',\r\n        content: `确定要删除功能\"${row.name}\"吗？此操作不可恢复。`,\r\n        onOk: async () => {\r\n          try {\r\n            const response = await workflowApi.deleteFunctionType(row.id)\r\n            if (response.code === 0) {\r\n              this.$Message.success(response.message || '功能删除成功')\r\n              // 重新加载数据\r\n              this.loadFunctionData()\r\n            } else {\r\n              this.$Message.error(response.message || '删除失败')\r\n            }\r\n          } catch (error) {\r\n            console.error('删除功能失败:', error)\r\n            this.$Message.error('删除失败，请重试')\r\n          }\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n\r\n\r\n<style lang=\"less\" scoped>\r\n.workflow-list {\r\n  padding: 16px;\r\n\r\n  .search-form {\r\n    margin-bottom: 16px;\r\n\r\n    .ivu-form-item {\r\n      margin-bottom: 0;\r\n      margin-right: 16px;\r\n    }\r\n  }\r\n\r\n  .action-buttons {\r\n    margin-bottom: 16px;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    gap: 8px;\r\n\r\n    .right-buttons {\r\n      display: flex;\r\n      gap: 8px;\r\n    }\r\n  }\r\n\r\n  .pagination-wrapper {\r\n    margin-top: 16px;\r\n    text-align: right;\r\n  }\r\n\r\n  .workflow-view {\r\n    .workflow-info {\r\n      margin-bottom: 16px;\r\n\r\n      .info-item {\r\n        margin-bottom: 12px;\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        .info-label {\r\n          font-weight: 500;\r\n          color: #515a6e;\r\n          min-width: 80px;\r\n        }\r\n\r\n        .info-value {\r\n          color: #17233d;\r\n        }\r\n      }\r\n    }\r\n\r\n    .workflow-preview {\r\n      .preview-canvas {\r\n        position: relative;\r\n        width: 100%;\r\n        height: 400px;\r\n        background: #f8f9fa;\r\n        border: 1px solid #e8eaec;\r\n        border-radius: 4px;\r\n        overflow: hidden;\r\n\r\n        .preview-node {\r\n          position: absolute;\r\n          width: 50px;\r\n          height: 25px;\r\n          background: white;\r\n          border: 1px solid #dcdee2;\r\n          border-radius: 3px;\r\n          display: flex;\r\n          flex-direction: column;\r\n          align-items: center;\r\n          justify-content: center;\r\n          font-size: 10px;\r\n          user-select: none;\r\n\r\n          &.node-start {\r\n            background: #52c41a;\r\n            color: white;\r\n            border-color: #52c41a;\r\n          }\r\n\r\n          &.node-end {\r\n            background: #f5222d;\r\n            color: white;\r\n            border-color: #f5222d;\r\n          }\r\n\r\n          &.node-approval {\r\n            background: #1890ff;\r\n            color: white;\r\n            border-color: #1890ff;\r\n          }\r\n\r\n          &.node-condition {\r\n            background: #fa8c16;\r\n            color: white;\r\n            border-color: #fa8c16;\r\n            transform: rotate(45deg);\r\n\r\n            .node-title {\r\n              transform: rotate(-45deg);\r\n            }\r\n          }\r\n\r\n          &.node-task {\r\n            background: #f0f0f0;\r\n            color: #333;\r\n          }\r\n\r\n          .node-title {\r\n            font-size: 8px;\r\n            font-weight: 500;\r\n            text-align: center;\r\n            line-height: 1;\r\n          }\r\n\r\n          i {\r\n            font-size: 10px;\r\n            margin-bottom: 2px;\r\n          }\r\n        }\r\n\r\n        .preview-connections {\r\n          position: absolute;\r\n          top: 0;\r\n          left: 0;\r\n          width: 100%;\r\n          height: 100%;\r\n          pointer-events: none;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // 流程功能管理样式\r\n  .function-manage {\r\n    .function-actions {\r\n      display: flex;\r\n      gap: 8px;\r\n      margin-bottom: 16px;\r\n    }\r\n  }\r\n}\r\n\r\n// 全局样式覆盖\r\n:deep(.ivu-card-body) {\r\n  padding: 16px;\r\n}\r\n\r\n:deep(.ivu-table-wrapper) {\r\n  border: 1px solid #e8eaec;\r\n}\r\n\r\n// BPMN XML查看弹窗样式\r\n.bpmn-xml-content {\r\n  .xml-header {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    margin-bottom: 16px;\r\n    padding-bottom: 12px;\r\n    border-bottom: 1px solid #e8eaec;\r\n\r\n    .workflow-name {\r\n      font-size: 16px;\r\n      font-weight: 600;\r\n      color: #2d8cf0;\r\n    }\r\n  }\r\n\r\n  .xml-viewer {\r\n    max-height: 500px;\r\n    overflow: auto;\r\n    background: #f8f9fa;\r\n    border: 1px solid #e8eaec;\r\n    border-radius: 4px;\r\n    padding: 16px;\r\n\r\n    pre {\r\n      margin: 0;\r\n      white-space: pre-wrap;\r\n      word-wrap: break-word;\r\n      font-family: 'Courier New', 'Monaco', 'Menlo', monospace;\r\n      font-size: 12px;\r\n      line-height: 1.5;\r\n      color: #333;\r\n\r\n      code {\r\n        background: none;\r\n        padding: 0;\r\n        border: none;\r\n        font-size: inherit;\r\n        color: inherit;\r\n      }\r\n    }\r\n\r\n    // 滚动条样式\r\n    &::-webkit-scrollbar {\r\n      width: 8px;\r\n      height: 8px;\r\n    }\r\n\r\n    &::-webkit-scrollbar-track {\r\n      background: #f1f1f1;\r\n      border-radius: 4px;\r\n    }\r\n\r\n    &::-webkit-scrollbar-thumb {\r\n      background: #c1c1c1;\r\n      border-radius: 4px;\r\n\r\n      &:hover {\r\n        background: #a8a8a8;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 上新字段管理样式\r\n  .element-manage-content {\r\n    min-height: 400px;\r\n  }\r\n\r\n  .element-list {\r\n    padding: 16px 0;\r\n  }\r\n\r\n  .add-element-btn {\r\n    margin-bottom: 16px;\r\n  }\r\n}\r\n\r\n\r\n</style>\r\n"]}]}