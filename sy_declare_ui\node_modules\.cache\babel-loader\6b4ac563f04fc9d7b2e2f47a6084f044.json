{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\base\\menus\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\base\\menus\\index.vue", "mtime": 1752737748512}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\.babelrc", "mtime": 1752737748316}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["listConvertTree", "<PERSON><PERSON>", "MenuAction", "icons", "Common", "name", "components", "data", "validateEn", "rule", "value", "callback", "reg", "Error", "test", "statusOps", "confirmModal", "saving", "visible", "selectIcons", "selectTreeData", "id", "menuName", "formItemRules", "parentId", "required", "message", "trigger", "menuCode", "validator", "formItem", "icon", "path", "scheme", "target", "status", "priority", "menuDesc", "columns", "title", "key", "min<PERSON><PERSON><PERSON>", "type", "slot", "methods", "treeSelectNormalizer", "node", "label", "children", "setSelectTree", "rowClick", "handleReset", "Object", "assign", "row", "$refs", "resetFields", "handleSubmit", "_this", "validate", "valid", "edit", "then", "res", "$Message", "success", "handleSearch", "finally", "add", "handleRemove", "_this2", "remove", "onIconClick", "item", "_this3", "getAll", "opt", "<PERSON><PERSON><PERSON>", "parent<PERSON><PERSON>", "startPid", "mounted"], "sources": ["src/view/module/base/menus/index.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <Row :gutter=\"8\">\r\n      <Col :lg=\"11\" :xl=\"11\" :xxl=\"6\">\r\n      <Card :shadow=\"true\">\r\n        <tree-table style=\"overflow: auto\" expand-key=\"menuName\" @radio-click=\"rowClick\" :expand-type=\"false\" :is-fold=\"false\" :tree-type=\"true\" :selectable=\"false\"\r\n                    :columns=\"columns\" :data=\"data\" class=\"card-tree-table\" >\r\n          <template v-slot:status=\"scope\">\r\n            <Badge v-for=\"v in statusOps\" v-if=\"v.key === scope.row.status\"\r\n                   :status=\"v.key === 0?'success':'error'\" v-bind:key=\"v.key\"></Badge>\r\n            <Icon :type=\"scope.row.icon\" size=\"16\"/>\r\n          </template>\r\n        </tree-table>\r\n      </Card>\r\n      </Col>\r\n      <Col :lg=\"13\" :xl=\"13\" :xxl=\"10\">\r\n      <Card :shadow=\"true\">\r\n        <div class=\"search-con search-con-top\">\r\n          <ButtonGroup>\r\n            <Button v-if=\"hasAuthority('menuAdd')\" type=\"primary\" @click=\"handleReset\">添加</Button>\r\n            <Button v-if=\"hasAuthority('menuDel')\" :disabled=\"!(formItem.id )\" type=\"primary\"\r\n                    @click=\"confirmModal = true\">删除\r\n            </Button>\r\n          </ButtonGroup>\r\n          <Modal v-model=\"confirmModal\" title=\"提示\" @on-ok=\"handleRemove\">\r\n            确定删除,菜单资源【{{formItem.menuName}}】吗?{{formItem.children && formItem.children.length > 0 ? '存在子菜单,将一起删除.是否继续?' : ''}}\r\n          </Modal>\r\n        </div>\r\n        <Form ref=\"menuForm\" :model=\"formItem\" :rules=\"formItemRules\" :label-width=\"80\">\r\n          <FormItem label=\"上级菜单\" prop=\"parentId\">\r\n            <treeselect v-model=\"formItem.parentId\" :options=\"selectTreeData\" :default-expand-level=\"1\" :normalizer=\"treeSelectNormalizer\"/>\r\n          </FormItem>\r\n          <FormItem label=\"菜单标识\" prop=\"menuCode\">\r\n            <Input v-model=\"formItem.menuCode\" placeholder=\"请输入内容\"></Input>\r\n          </FormItem>\r\n          <FormItem label=\"菜单名称\" prop=\"menuName\">\r\n            <Input v-model=\"formItem.menuName\" placeholder=\"请输入内容\"></Input>\r\n          </FormItem>\r\n          <FormItem label=\"页面地址\" prop=\"path\">\r\n            <Input v-model=\"formItem.path\" placeholder=\"请输入内容\">\r\n            <Select v-model=\"formItem.scheme\" style=\"width: 80px\">\r\n              <Option value=\"/\">/</Option>\r\n              <Option value=\"http://\">http://</Option>\r\n              <Option value=\"https://\">https://</Option>\r\n            </Select>\r\n            <Select v-model=\"formItem.target\" style=\"width: 100px\">\r\n              <Option value=\"_self\">窗口内打开</Option>\r\n              <Option :disabled=\"formItem.scheme==='/'\" value=\"_blank\">新窗口打开</Option>\r\n            </Select>\r\n            </Input>\r\n            <span v-if=\"formItem.scheme === '/'\">前端组件：/view/module/{{formItem.path}}.vue</span>\r\n            <span v-else>跳转地址：{{formItem.scheme}}{{formItem.path}}</span>\r\n          </FormItem>\r\n          <FormItem label=\"图标\">\r\n            <Input v-model=\"formItem.icon\" placeholder=\"请输入内容\">\r\n            <Icon size=\"22\" :type=\"formItem.icon\" />\r\n            <Poptip width=\"600\" placement=\"bottom\">\r\n              <Button icon=\"ios-search\"></Button>\r\n              <div slot=\"content\">\r\n                <ul class=\"icons\">\r\n                  <li class=\"icons-item\" :title=\"item\" @click=\"onIconClick(item)\" v-for=\"item in selectIcons\">\r\n                    <Icon :type=\"item\" size=\"28\"/>\r\n                    <p>{{item}}</p>\r\n                  </li>\r\n                </ul>\r\n              </div>\r\n            </Poptip>\r\n            </Input>\r\n          </FormItem>\r\n          <FormItem label=\"优先级\">\r\n            <InputNumber v-model=\"formItem.priority\"></InputNumber>\r\n          </FormItem>\r\n          <FormItem label=\"状态\">\r\n            <RadioGroup v-model=\"formItem.status\" type=\"button\">\r\n              <Radio v-for=\"v in statusOps\" :label=\"v.key\" v-if=\"v.key !== -1\" v-bind:key=\"v.key\">{{ v.name }}</Radio>\r\n            </RadioGroup>\r\n          </FormItem>\r\n          <FormItem label=\"描述\">\r\n            <Input v-model=\"formItem.menuDesc\" type=\"textarea\" placeholder=\"请输入内容\"></Input>\r\n          </FormItem>\r\n          <FormItem>\r\n            <Button @click=\"handleSubmit\" :loading=\"saving\" type=\"primary\" v-if=\"hasAuthority('menuEdit') || hasAuthority('menuAdd')\">保存</Button>\r\n            <Button @click=\"handleReset\" style=\"margin-left: 8px\">重置</Button>\r\n          </FormItem>\r\n        </Form>\r\n      </Card>\r\n      </Col>\r\n      <Col :lg=\"11\" :xl=\"11\" :xxl=\"8\" class=\"lastCol\">\r\n      <Card :shadow=\"true\">\r\n        <menu-action :value=\"formItem\"></menu-action>\r\n      </Card>\r\n      </Col>\r\n    </Row>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {listConvertTree} from '@/libs/util'\r\nimport Menu from '@/api/base/menu'\r\nimport MenuAction from './menu-action.vue'\r\nimport icons from './icons'\r\nimport Common from \"@/api/basic/common\";\r\nexport default {\r\n    name: 'systemMenu',\r\n    components: {\r\n      MenuAction\r\n    },\r\n    data () {\r\n      const validateEn = (rule, value, callback) => {\r\n        let reg = /^[_a-zA-Z0-9]+$/\r\n        if (value === '') {\r\n          callback(new Error('菜单标识不能为空'))\r\n        } else if (value !== '' && !reg.test(value)) {\r\n          callback(new Error('只允许字母、数字、下划线'))\r\n        } else {\r\n          callback()\r\n        }\r\n      }\r\n      return {\r\n        statusOps: Common.statusOps,\r\n        confirmModal: false,\r\n        saving: false,\r\n        visible: false,\r\n        selectIcons: icons,\r\n        selectTreeData: [{\r\n          id: 0,\r\n          menuName: '无'\r\n        }],\r\n        formItemRules: {\r\n          parentId: [\r\n            {required: true, message: '上级菜单', trigger: 'blur'}\r\n          ],\r\n          menuCode: [\r\n            {required: true, validator: validateEn, trigger: 'blur'}\r\n          ],\r\n          menuName: [\r\n            {required: true, message: '菜单名称不能为空', trigger: 'blur'}\r\n          ]\r\n        },\r\n        formItem: {\r\n          id: '',\r\n          menuCode: '',\r\n          menuName: '',\r\n          icon: 'md-document',\r\n          path: '',\r\n          scheme: '/',\r\n          target: '_self',\r\n          status: 1,\r\n          parentId: '0',\r\n          priority: 0,\r\n          menuDesc: ''\r\n        },\r\n        columns: [\r\n          {\r\n            title: '菜单名称',\r\n            key: 'menuName',\r\n            minWidth: '200px'\r\n          },\r\n          {\r\n            title: '状态',\r\n            key: 'status',\r\n            type: 'template',\r\n            minWidth: '100px',\r\n            slot: 'status'\r\n          },\r\n        ],\r\n        data: []\r\n      }\r\n    },\r\n    methods: {\r\n      treeSelectNormalizer (node) {\r\n        return {\r\n          id: node.id,\r\n          label: node.menuName,\r\n          children: node.children\r\n        }\r\n      },\r\n      setSelectTree (data) {\r\n        this.selectTreeData = data\r\n      },\r\n      rowClick (data) {\r\n        this.handleReset()\r\n        if (data) {\r\n          this.formItem = Object.assign({}, data.row)\r\n        }\r\n      },\r\n      handleReset () {\r\n        this.formItem = {\r\n          id: '',\r\n          menuCode: '',\r\n          menuName: '',\r\n          icon: 'md-document',\r\n          path: '',\r\n          scheme: '/',\r\n          target: '_self',\r\n          status: 0,\r\n          parentId: '0',\r\n          priority: 0,\r\n          menuDesc: ''\r\n        }\r\n        this.$refs['menuForm'].resetFields()\r\n        this.saving = false\r\n      },\r\n      handleSubmit () {\r\n        this.$refs['menuForm'].validate((valid) => {\r\n          if (valid) {\r\n            this.saving = true\r\n            if (this.formItem.id) {\r\n              Menu.edit(this.formItem).then(res => {\r\n                if (res['code'] === 0) {\r\n                  this.$Message.success('保存成功')\r\n                }\r\n                this.handleSearch()\r\n              }).finally(() => {\r\n                this.saving = false\r\n              })\r\n            } else {\r\n              Menu.add(this.formItem).then(res => {\r\n                if (res['code'] === 0) {\r\n                  this.$Message.success('保存成功')\r\n                }\r\n                this.handleSearch()\r\n              }).finally(() => {\r\n                this.saving = false\r\n              })\r\n            }\r\n          }\r\n        })\r\n      },\r\n      handleRemove () {\r\n        Menu.remove(this.formItem.id).then(res => {\r\n          this.handleReset()\r\n          this.handleSearch()\r\n          if (res['code'] === 0) {\r\n            this.$Message.success('删除成功')\r\n          }\r\n        })\r\n      },\r\n      onIconClick (item) {\r\n        this.formItem.icon = item\r\n      },\r\n      handleSearch () {\r\n        Menu.getAll().then(res => {\r\n          if(res['code'] ===0){\r\n            let opt = {\r\n              primaryKey: 'id',\r\n              parentKey: 'parentId',\r\n              startPid: '0'\r\n            }\r\n            this.data = listConvertTree(res.data, opt)\r\n            this.setSelectTree(this.data)\r\n          }\r\n        })\r\n      }\r\n    },\r\n    mounted: function () {\r\n      this.handleSearch()\r\n    }\r\n  }\r\n</script>\r\n<style lang=\"less\">\r\n  .icons {\r\n    overflow: auto;\r\n    zoom: 1;\r\n    height: 300px;\r\n  }\r\n\r\n  .icons-item {\r\n    float: left;\r\n    margin: 6px;\r\n    width: 60px;\r\n    text-align: center;\r\n    list-style: none;\r\n    cursor: pointer;\r\n    color: #5c6b77;\r\n    transition: all .2s ease;\r\n    position: relative;\r\n  }\r\n\r\n  .icons-item p {\r\n    word-break: break-all;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .card-tree-table{\r\n    max-height: 700px;\r\n  }\r\n  @media only screen and (max-width: 1366px) {//1366px宽度以下单独样式\r\n    .card-tree-table{\r\n      max-height: 605px;\r\n    }\r\n    .lastCol{\r\n      margin-top: 10px;\r\n    }\r\n  }\r\n</style>\r\n"], "mappings": ";;;;AAiGA,SAAAA,eAAA;AACA,OAAAC,IAAA;AACA,OAAAC,UAAA;AACA,OAAAC,KAAA;AACA,OAAAC,MAAA;AACA;EACAC,IAAA;EACAC,UAAA;IACAJ,UAAA,EAAAA;EACA;EACAK,IAAA,WAAAA,KAAA;IACA,IAAAC,UAAA,YAAAA,WAAAC,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,IAAAC,GAAA;MACA,IAAAF,KAAA;QACAC,QAAA,KAAAE,KAAA;MACA,WAAAH,KAAA,YAAAE,GAAA,CAAAE,IAAA,CAAAJ,KAAA;QACAC,QAAA,KAAAE,KAAA;MACA;QACAF,QAAA;MACA;IACA;IACA;MACAI,SAAA,EAAAX,MAAA,CAAAW,SAAA;MACAC,YAAA;MACAC,MAAA;MACAC,OAAA;MACAC,WAAA,EAAAhB,KAAA;MACAiB,cAAA;QACAC,EAAA;QACAC,QAAA;MACA;MACAC,aAAA;QACAC,QAAA,GACA;UAAAC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,QAAA,GACA;UAAAH,QAAA;UAAAI,SAAA,EAAArB,UAAA;UAAAmB,OAAA;QAAA,EACA;QACAL,QAAA,GACA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAG,QAAA;QACAT,EAAA;QACAO,QAAA;QACAN,QAAA;QACAS,IAAA;QACAC,IAAA;QACAC,MAAA;QACAC,MAAA;QACAC,MAAA;QACAX,QAAA;QACAY,QAAA;QACAC,QAAA;MACA;MACAC,OAAA,GACA;QACAC,KAAA;QACAC,GAAA;QACAC,QAAA;MACA,GACA;QACAF,KAAA;QACAC,GAAA;QACAE,IAAA;QACAD,QAAA;QACAE,IAAA;MACA,EACA;MACApC,IAAA;IACA;EACA;EACAqC,OAAA;IACAC,oBAAA,WAAAA,qBAAAC,IAAA;MACA;QACAzB,EAAA,EAAAyB,IAAA,CAAAzB,EAAA;QACA0B,KAAA,EAAAD,IAAA,CAAAxB,QAAA;QACA0B,QAAA,EAAAF,IAAA,CAAAE;MACA;IACA;IACAC,aAAA,WAAAA,cAAA1C,IAAA;MACA,KAAAa,cAAA,GAAAb,IAAA;IACA;IACA2C,QAAA,WAAAA,SAAA3C,IAAA;MACA,KAAA4C,WAAA;MACA,IAAA5C,IAAA;QACA,KAAAuB,QAAA,GAAAsB,MAAA,CAAAC,MAAA,KAAA9C,IAAA,CAAA+C,GAAA;MACA;IACA;IACAH,WAAA,WAAAA,YAAA;MACA,KAAArB,QAAA;QACAT,EAAA;QACAO,QAAA;QACAN,QAAA;QACAS,IAAA;QACAC,IAAA;QACAC,MAAA;QACAC,MAAA;QACAC,MAAA;QACAX,QAAA;QACAY,QAAA;QACAC,QAAA;MACA;MACA,KAAAkB,KAAA,aAAAC,WAAA;MACA,KAAAvC,MAAA;IACA;IACAwC,YAAA,WAAAA,aAAA;MAAA,IAAAC,KAAA;MACA,KAAAH,KAAA,aAAAI,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAF,KAAA,CAAAzC,MAAA;UACA,IAAAyC,KAAA,CAAA5B,QAAA,CAAAT,EAAA;YACApB,IAAA,CAAA4D,IAAA,CAAAH,KAAA,CAAA5B,QAAA,EAAAgC,IAAA,WAAAC,GAAA;cACA,IAAAA,GAAA;gBACAL,KAAA,CAAAM,QAAA,CAAAC,OAAA;cACA;cACAP,KAAA,CAAAQ,YAAA;YACA,GAAAC,OAAA;cACAT,KAAA,CAAAzC,MAAA;YACA;UACA;YACAhB,IAAA,CAAAmE,GAAA,CAAAV,KAAA,CAAA5B,QAAA,EAAAgC,IAAA,WAAAC,GAAA;cACA,IAAAA,GAAA;gBACAL,KAAA,CAAAM,QAAA,CAAAC,OAAA;cACA;cACAP,KAAA,CAAAQ,YAAA;YACA,GAAAC,OAAA;cACAT,KAAA,CAAAzC,MAAA;YACA;UACA;QACA;MACA;IACA;IACAoD,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACArE,IAAA,CAAAsE,MAAA,MAAAzC,QAAA,CAAAT,EAAA,EAAAyC,IAAA,WAAAC,GAAA;QACAO,MAAA,CAAAnB,WAAA;QACAmB,MAAA,CAAAJ,YAAA;QACA,IAAAH,GAAA;UACAO,MAAA,CAAAN,QAAA,CAAAC,OAAA;QACA;MACA;IACA;IACAO,WAAA,WAAAA,YAAAC,IAAA;MACA,KAAA3C,QAAA,CAAAC,IAAA,GAAA0C,IAAA;IACA;IACAP,YAAA,WAAAA,aAAA;MAAA,IAAAQ,MAAA;MACAzE,IAAA,CAAA0E,MAAA,GAAAb,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA;UACA,IAAAa,GAAA;YACAC,UAAA;YACAC,SAAA;YACAC,QAAA;UACA;UACAL,MAAA,CAAAnE,IAAA,GAAAP,eAAA,CAAA+D,GAAA,CAAAxD,IAAA,EAAAqE,GAAA;UACAF,MAAA,CAAAzB,aAAA,CAAAyB,MAAA,CAAAnE,IAAA;QACA;MACA;IACA;EACA;EACAyE,OAAA,WAAAA,QAAA;IACA,KAAAd,YAAA;EACA;AACA"}]}