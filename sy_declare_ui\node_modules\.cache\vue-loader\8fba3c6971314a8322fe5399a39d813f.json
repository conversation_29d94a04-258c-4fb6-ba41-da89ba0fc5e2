{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\base\\product\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\base\\product\\index.vue", "mtime": 1754360258638}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAiHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/view/module/base/product", "sourcesContent": ["<!--\r\n@create date 2025-02-07\r\n@desc 产品管理\r\n-->\r\n\r\n<template>\r\n  <div class=\"search-con-top\">\r\n    <Card :shadow=\"true\">\r\n      <div>\r\n        <Form ref=\"searchFormRef\" class=\"searchForm\" :model=\"searchForm\" :inline=\"true\" @submit.native.prevent>\r\n          <FormItem prop=\"code\" class=\"multiClass\">\r\n            <div class=\"flex-h\">\r\n              <Multiple placeholder=\"请输入产品编码(回车分隔)\" @changeValue=\"(values)=>{ multiValuesCode = values || []; }\" ref=\"multipleRefCodeRef\" style=\"height:32px;\"></Multiple>\r\n              <Button :visible=\"false\" @click=\"()=>{popVisibleCode=true;}\">输入</Button>\r\n              <Dropdown trigger=\"custom\" :visible=\"popVisibleCode\" style=\"margin-left: 3px\" :transfer=\"true\" transfer-class-name=\"orderBillDrop\">\r\n                <template #list>\r\n                  <DropdownMenu class=\"popContentClass\">\r\n                    <Input v-model=\"popContentCode\" type=\"textarea\" :autosize=\"{minRows: 4,maxRows: 8}\" placeholder=\"请输入内容，回车或逗号分隔\" style=\"width: 260px\"/>\r\n                    <div style=\"text-align: right; padding-top: 3px\">\r\n                      <Button type=\"info\" size=\"small\" @click=\"closeDropdownCode\">确定</Button>\r\n                    </div>\r\n                  </DropdownMenu>\r\n                </template>\r\n              </Dropdown>\r\n            </div>\r\n          </FormItem>\r\n          <FormItem prop=\"name\" class=\"multiClass\">\r\n            <div class=\"flex-h\">\r\n              <Multiple placeholder=\"请输入产品名称(回车分隔)\" @changeValue=\"(values)=>{ multiValuesName = values || []; }\" ref=\"multipleRefNameRef\" style=\"height:32px;\"></Multiple>\r\n              <Button :visible=\"false\" @click=\"()=>{popVisibleName=true;}\">输入</Button>\r\n              <Dropdown trigger=\"custom\" :visible=\"popVisibleName\" style=\"margin-left: 3px\" :transfer=\"true\" transfer-class-name=\"orderBillDrop\">\r\n                <template #list>\r\n                  <DropdownMenu class=\"popContentClass\">\r\n                    <Input v-model=\"popContentName\" type=\"textarea\" :autosize=\"{minRows: 4,maxRows: 8}\" placeholder=\"请输入内容，回车或逗号分隔\" style=\"width: 260px\"/>\r\n                    <div style=\"text-align: right; padding-top: 3px\">\r\n                      <Button type=\"info\" size=\"small\" @click=\"closeDropdownName\">确定</Button>\r\n                    </div>\r\n                  </DropdownMenu>\r\n                </template>\r\n              </Dropdown>\r\n            </div>\r\n          </FormItem>\r\n          <FormItem prop=\"spec\" class=\"multiClass\">\r\n            <div class=\"flex-h\">\r\n              <Multiple placeholder=\"请输入产品规格(回车分隔)\" @changeValue=\"(values)=>{ multiValuesSpec = values || []; }\" ref=\"multipleRefSpecRef\" style=\"height:32px;\"></Multiple>\r\n              <Button :visible=\"false\" @click=\"()=>{popVisibleSpec=true;}\">输入</Button>\r\n              <Dropdown trigger=\"custom\" :visible=\"popVisibleSpec\" style=\"margin-left: 3px\" :transfer=\"true\" transfer-class-name=\"orderBillDrop\">\r\n                <template #list>\r\n                  <DropdownMenu class=\"popContentClass\">\r\n                    <Input v-model=\"popContentSpec\" type=\"textarea\" :autosize=\"{minRows: 4,maxRows: 8}\" placeholder=\"请输入内容，回车或逗号分隔\" style=\"width: 260px\"/>\r\n                    <div style=\"text-align: right; padding-top: 3px\">\r\n                      <Button type=\"info\" size=\"small\" @click=\"closeDropdownSpec\">确定</Button>\r\n                    </div>\r\n                  </DropdownMenu>\r\n                </template>\r\n              </Dropdown>\r\n            </div>\r\n          </FormItem>\r\n          <FormItem prop=\"isCombo\" :clear=\"true\">\r\n            <Select type=\"text\" v-model=\"searchForm.isCombo\" placeholder=\"是否组合品\" style=\"width:160px\">\r\n              <Option v-for=\"(item,index) in statusList\" :value=\"item.key\" :key=\"index\">{{ item['value'] }}</Option>\r\n            </Select>\r\n          </FormItem>\r\n          <FormItem prop=\"isThd\" :clear=\"true\">\r\n            <Select type=\"text\" v-model=\"searchForm.isThd\" placeholder=\"是否同步\" style=\"width:160px\">\r\n              <Option v-for=\"(item,index) in statusList\" :value=\"item.key\" :key=\"index\">{{ item['value'] }}</Option>\r\n            </Select>\r\n          </FormItem>\r\n          <FormItem>\r\n            <Button type=\"primary\" @click=\"handleSearch()\">查询</Button>&nbsp;\r\n            <Button @click=\"handleReset()\">重置</Button>\r\n          </FormItem>\r\n        </Form>\r\n      </div>\r\n      <div style=\"margin-bottom: 10px;\">\r\n        <div style=\"float:left\">\r\n        <Upload ref=\"uploadFileRef\" name=\"importFile\" :action=\"importURl\" :max-size=\"10240\"\r\n                :on-success=\"handleImportSuccess\"\r\n                :format=\"['xls', 'xlsx']\"\r\n                :show-upload-list=\"false\"\r\n                :on-format-error=\"handleImportFormatError\"\r\n                :on-error=\"handleImportError\" :headers=\"loginInfo\" :on-exceeded-size=\"handleMaxSize\">\r\n          <Button type=\"primary\" >上传文件</Button>\r\n        </Upload></div>\r\n        <Button @click=\"downTemplate\" class=\"buttonMargin\" :loading=\"loading\">下载模板</Button>\r\n        <Button @click=\"handleExport\" class=\"buttonMargin\" :loading=\"loading\">导出</Button>\r\n        <Button @click=\"()=>{syncVisible=true;}\" class=\"buttonMargin\" :loading=\"loading\">同步数据</Button>\r\n        <Table :border=\"true\" ref=\"autoTableRef\" :max-height=\"autoTableHeight($refs.autoTableRef)\" :columns=\"columns\"\r\n               :data=\"tableData\" :loading=\"loading\" :span-method=\"handleSpan\">\r\n          <template v-slot:isCombo=\"{row}\">\r\n            <Badge v-for=\"v in statusList\" :status=\"v.key === 0?'success':'warning'\" :text=\"v.value\" v-if=\"v.key === row.isCombo\" v-bind:key=\"v.key\"></Badge>\r\n          </template>\r\n        </Table>\r\n        <Page :total=\"searchForm.total\" size=\"small\" :current=\"searchForm.page\" :page-size=\"searchForm.limit\" :show-elevator=\"true\"\r\n              :show-sizer=\"true\" :show-total=\"true\" @on-change=\"handlePage\" @on-page-size-change='handlePageSize'></Page>\r\n      </div>\r\n      <Modal :title=\"'同步产品信息'\" width=\"500px\" @on-cancel=\"()=>{syncVisible=false; $refs['syncFormRef'].resetFields();}\" @on-ok=\"onSync\" :value=\"syncVisible\" >\r\n        <Form ref=\"syncFormRef\" :model=\"syncForm\" :label-width=\"100\">\r\n          <FormItem label=\"销售Sku\" prop=\"codes\">\r\n            <Input v-model=\"syncForm.codes\" type=\"textarea\" placeholder=\"请输入内容,多个以逗号隔开,最多支持10个\"></Input>\r\n          </FormItem>\r\n        </Form>\r\n        <div slot=\"footer\"></div>\r\n        <template v-slot:footer=\"{}\">\r\n          <Button @click=\"()=>{syncVisible=false;$refs['syncFormRef'].resetFields();}\" :disabled=\"syncLoading\">取消</Button>\r\n          <Button type=\"primary\" @click=\"onSync\" :loading=\"syncLoading\" style=\"margin-left: 15px\">确认</Button>\r\n        </template>\r\n      </Modal>\r\n    </Card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { autoTableHeight} from \"@/libs/tools.js\";\r\nimport { getToken, getUrl } from '@/libs/util';\r\nimport Multiple from \"@/view/module/common/multipleInput.vue\";\r\nimport Product from \"@/api/base/product\";\r\nimport ShopSelect from \"_c/shopSelect/index.vue\";\r\nimport UpcCode from \"@/api/newApply/upcCode\";\r\nexport default {\r\n  name: \"productManage\",\r\n  components: {ShopSelect, Multiple},\r\n  data() {\r\n    return {\r\n      autoTableHeight,\r\n      syncVisible:false,\r\n      syncLoading:false,\r\n      syncForm:{\"codes\":null},\r\n      loading: false,\r\n      multiValuesCode:[],\r\n      popVisibleCode:false,\r\n      popContentCode: undefined,\r\n      multiValuesName:[],\r\n      popVisibleName:false,\r\n      popContentName: undefined,\r\n      multiValuesSpec:[],\r\n      popVisibleSpec:false,\r\n      popContentSpec: undefined,\r\n      mergeColumns:['index','code','name','spec','isCombo','syncFlag','updateTime'],\r\n      statusList:[{\"key\":-1,\"value\":\"全部\"},{\"key\":0,\"value\":\"否\"},{\"key\":1,\"value\":\"是\"}],\r\n      importURl: getUrl() + Product.path+ '/importFile',\r\n      loginInfo: {\r\n        Accept: 'application/json,text/plain, */*, */*',\r\n        mode: 'cors',\r\n        Authorization: 'Bearer ' + getToken()\r\n      },\r\n      searchForm: {\r\n        isCombo:null,\r\n        isThd:null,\r\n        page:1,\r\n        limit:10\r\n      },\r\n      tableData: [], //表格数据\r\n      columns: [{title: '#', key: 'index',width: 60,align: 'center'},\r\n        {title: '编码',key: 'code',width: 150,resizable:true,render: (_, { row }) => (<span v-copytext={row['code']}>{row['code']}</span>)},\r\n        {title: '类型',key: 'name',minWidth: 120,resizable:true,render: (_, { row }) => (<span v-copytext={row['name']}>{row['name']}</span>)},\r\n        {title: '规格',key: 'spec',minWidth: 250,resizable:true,render: (_, { row }) => (<span v-copytext={row['spec']}>{row['spec']}</span>)},\r\n        {title: '是否组合品',key: 'isCombo',width: 120,resizable:true,slot:'isCombo'},\r\n        {title: '是否同步',key: 'syncFlag',width: 120,resizable:true,render: (_, { row }) => (<span v-copytext={row['syncFlag']}>{row['syncFlag']}</span>)},\r\n        {title: '同步时间',key: 'updateTime',width: 180,resizable:true,render:(_, { row }) => (<span v-copytext={row['updateTime']}>{row['updateTime']}</span>)},\r\n        {title: '子编码', align: \"center\",key: 'childSku', width: 120, render: (h, {row}) => (<span v-copytext={row.childSku}>{row.childSku}</span>)},\r\n        {title: '子规格', align: \"center\",key: 'childSpec', minWidth: 200, render: (h, {row}) => (<span v-copytext={row.childSpec}>{row.childSpec}</span>)},\r\n        {title: '子数量', align: \"center\",key: 'itemQuantity', width: 80, render: (h, {row}) => (<span v-copytext={row.itemQuantity}>{row.itemQuantity}</span>)},\r\n      ],\r\n    };\r\n  },\r\n  //组件初始化进行的操作\r\n  mounted() {\r\n    this.handleSearch();\r\n  },\r\n  methods: {\r\n    closeDropdownCode() { //关闭输入文本框\r\n      const { popContentCode } = this;\r\n      const { multipleRefCodeRef } = this.$refs;\r\n      this.popVisibleCode = false;\r\n      if(!popContentCode) return;\r\n      const content = popContentCode ? popContentCode.trim().replace(/，/g, \",\") : '';\r\n      this.multiValuesCode = content.split('\\n').filter(v=>!!v);\r\n      this.multiValuesCode = [...new Set(this.multiValuesCode)];\r\n      if(multipleRefCodeRef && multipleRefCodeRef.setValueArray){\r\n        multipleRefCodeRef.setValueArray(this.multiValuesCode);\r\n      }\r\n    },\r\n    closeDropdownName() { //关闭输入文本框\r\n      const { popContentName } = this;\r\n      const { multipleRefNameRef } = this.$refs;\r\n      this.popVisibleName = false;\r\n      if(!popContentName) return;\r\n      const content = popContentName ? popContentName.trim().replace(/，/g, \",\") : '';\r\n      this.multiValuesName = content.split('\\n').filter(v=>!!v);\r\n      this.multiValuesName = [...new Set(this.multiValuesName)];\r\n      if(multipleRefNameRef && multipleRefNameRef.setValueArray){\r\n        multipleRefNameRef.setValueArray(this.multiValuesCode);\r\n      }\r\n    },\r\n    closeDropdownSpec() { //关闭输入文本框\r\n      const { popContentSpec } = this;\r\n      const { multipleRefSpecRef } = this.$refs;\r\n      this.popVisibleSpec = false;\r\n      if(!popContentSpec) return;\r\n      const content = popContentSpec ? popContentSpec.trim().replace(/，/g, \",\") : '';\r\n      this.multiValuesSpec = content.split('\\n').filter(v=>!!v);\r\n      this.multiValuesSpec = [...new Set(this.multiValuesSpec)];\r\n      if(multipleRefSpecRef && multipleRefSpecRef.setValueArray){\r\n        multipleRefSpecRef.setValueArray(this.multiValuesSpec);\r\n      }\r\n    },\r\n    getParam(){\r\n      const params = {\r\n        ...this.searchForm\r\n      };\r\n      const getStr = value => value && Array.isArray(value) ? value.join(\"&#&\") : undefined;\r\n      if (this.multiValuesCode.length > 0){\r\n        params[\"codes\"] = getStr(this.multiValuesCode);\r\n      }\r\n      if (this.multiValuesName.length > 0){\r\n        params[\"names\"] = getStr(this.multiValuesName);\r\n      }\r\n      if (this.multiValuesSpec.length > 0){\r\n        params[\"specs\"] = getStr(this.multiValuesSpec);\r\n      }\r\n      return params;\r\n    },\r\n    getDataSource(data = []) {\r\n      const result = [];\r\n      let index = 1;\r\n      for (const item of data) {\r\n        item['index'] = index++;\r\n        if (item && item.detailList) {\r\n          if (item.detailList.length === 0){\r\n            item.detailList = [{}];\r\n            result.push(item);\r\n          }else{\r\n            let rowSpan = item.detailList.length;\r\n            item.detailList.forEach((child, index) => {\r\n              const obj = {\r\n                ...item,\r\n                rowSpan: index ===0?rowSpan:0,\r\n                childSku: child && child.childSku,\r\n                childSpec: child && child.childSpec,\r\n                itemQuantity:child && child.quantity\r\n              };\r\n              result.push(obj);\r\n            });\r\n          }\r\n        }\r\n      }\r\n      return result;\r\n    },\r\n    handleSpan({ row, column}) {\r\n      if (this.mergeColumns.includes(column.key)) {\r\n        return {\r\n          rowspan: row.rowSpan,\r\n          colspan: 1\r\n        };\r\n      }\r\n    },\r\n    //查询\r\n    handleSearch() {\r\n      const params = this.getParam();\r\n      this.loading = true\r\n      Product.listPage(params).then(res => {\r\n        if (res && res['code'] === 0) {\r\n          this.tableData = this.getDataSource(res.data.records || []);\r\n          this.searchForm.total = parseInt(res.data.total);\r\n        }\r\n      }).finally(() => {\r\n        this.loading = false\r\n      })\r\n    },\r\n    handleReset() {\r\n      //重置验证\r\n      this.$refs['searchFormRef'].resetFields();\r\n      this.searchForm.shops=[];\r\n      this.resetMultiple(true);\r\n    },\r\n    resetMultiple(clearTxt = false) {\r\n      if (clearTxt === true) {\r\n        this.multiValuesCode = [];\r\n        const { multipleRefCodeRef } = this.$refs;\r\n        if (multipleRefCodeRef && multipleRefCodeRef.setValueArray) {\r\n          multipleRefCodeRef.setValueArray([]);\r\n        }\r\n        this.multiValuesName = [];\r\n        const { multipleRefNameRef } = this.$refs;\r\n        if (multipleRefNameRef && multipleRefNameRef.setValueArray) {\r\n          multipleRefNameRef.setValueArray([]);\r\n        }\r\n        this.multiValuesSpec = [];\r\n        const { multipleRefSpecRef } = this.$refs;\r\n        if (multipleRefSpecRef && multipleRefSpecRef.setValueArray) {\r\n          multipleRefSpecRef.setValueArray([]);\r\n        }\r\n      }\r\n      this.popContentCode = undefined;\r\n      this.popVisibleCode = false;\r\n      this.popContentName = undefined;\r\n      this.popVisibleName = false;\r\n      this.popContentSpec = undefined;\r\n      this.popVisibleSpec = false;\r\n    },\r\n    handlePage(current) {\r\n      this.searchForm.page = current\r\n      this.handleSearch()\r\n    },\r\n    handlePageSize(size) {\r\n      this.searchForm.limit = size\r\n      this.handleSearch()\r\n    },\r\n    handleExport(){\r\n      this.loading = true;\r\n      let params = this.getParam();\r\n      params['fileName'] = \"产品信息表_\" + new Date().getExportFormat() + \".xls\";\r\n      Product.exportFile(params, () => {\r\n        this.loading = false\r\n      });\r\n    },\r\n    onSync(){\r\n      //点击确定\r\n      this.syncLoading = true;\r\n      const content = this.syncForm.codes ? this.syncForm.codes.trim().replace(/，/g, \",\") : '';\r\n      let codes = content.split('\\n').filter(v=>!!v).join(\",\");\r\n      Product.syncProduct({\"codes\":codes}).then((res)=>{\r\n        if(res && res['code'] ===0){\r\n          this.$Message.success(\"提交同步任务成功,任务正在执行\");\r\n          this.onCancel();\r\n        }else{\r\n          this.$Message.error(res['message'])\r\n        }\r\n      }).catch(()=>{}).finally(()=>{this.syncLoading = false;});\r\n    },\r\n    downTemplate() {\r\n      this.loading = true;\r\n      let params = {};\r\n      params['fileName'] = \"组合品导入模板.xls\";\r\n      Product.downTemplate(params, () => {\r\n        this.loading = false\r\n      });\r\n    },\r\n    handleImportError (err, file) {\r\n      this.$Message.error(file.message);\r\n    },\r\n    handleImportSuccess (res) {\r\n      this.$refs['uploadFileRef'].clearFiles();\r\n      if (res.code === 0) {\r\n        this.handleSearch();\r\n      } else {\r\n        this.$Message.error(res.message);\r\n      }\r\n    },\r\n    handleMaxSize () {\r\n      this.$Message.warning('大小不能超过10M.');\r\n    },\r\n    handleImportFormatError (file) {\r\n      //格式验证失败的钩子\r\n      this.$Modal.error({\r\n        title: '文件格式不正确',\r\n        content: '文件 ' + file.name + '格式不正确，支持上传的文件类型：xls,xlsx',\r\n        okText: '确认'\r\n      });\r\n    },\r\n  }\r\n};\r\n</script>\r\n<style lang=\"less\">\r\n.search-con-top{\r\n  position: relative;\r\n  padding: 0;\r\n  //标签、sku、asin搜索项\r\n  .multiClass{\r\n    .flex-h{\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      margin-top:2px;\r\n    }\r\n  }\r\n}\r\n.widthClass {\r\n  width: 350px\r\n}\r\n.buttonMargin{\r\n  margin-left:15px;\r\n}\r\n</style>\r\n"]}]}