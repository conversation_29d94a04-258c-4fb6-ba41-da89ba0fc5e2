{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\api\\base\\gateway\\route.js", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\api\\base\\gateway\\route.js", "mtime": 1752737748399}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\.babelrc", "mtime": 1752737748316}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC9saWJzL3JlcXVlc3QnOwp2YXIgcm91dGVQYXRoID0gIi9iYXNlL3JvdXRlIjsKCi8qKg0KICog6I635Y+W5YiG6aG15pWw5o2uDQogKi8KdmFyIGxpc3RQYWdlID0gZnVuY3Rpb24gbGlzdFBhZ2UocGFyYW1zKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiByb3V0ZVBhdGggKyAnL2xpc3RQYWdlJywKICAgIHBhcmFtczogcGFyYW1zLAogICAgbWV0aG9kOiAnZ2V0JwogIH0pOwp9OwovKioNCiAqIOa3u+WKoOaVsOaNrg0KICovCnZhciBhZGRSb3V0ZSA9IGZ1bmN0aW9uIGFkZFJvdXRlKGRhdGEpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6IHJvdXRlUGF0aCArICcvYWRkUm91dGUnLAogICAgZGF0YTogZGF0YSwKICAgIG1ldGhvZDogJ3Bvc3QnCiAgfSk7Cn07Ci8qKg0KICog5re75Yqg5pWw5o2uDQogKi8KdmFyIHVwZGF0ZVJvdXRlID0gZnVuY3Rpb24gdXBkYXRlUm91dGUoZGF0YSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogcm91dGVQYXRoICsgJy91cGRhdGVSb3V0ZScsCiAgICBkYXRhOiBkYXRhLAogICAgbWV0aG9kOiAncG9zdCcKICB9KTsKfTsKCi8qKg0KICog5Yig6ZmkDQogKi8KdmFyIHJlbW92ZVJvdXRlID0gZnVuY3Rpb24gcmVtb3ZlUm91dGUoaWQpIHsKICB2YXIgZGF0YSA9IHsKICAgICJpZCI6IGlkCiAgfTsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6IHJvdXRlUGF0aCArICcvcmVtb3ZlUm91dGUnLAogICAgZGF0YTogZGF0YSwKICAgIG1ldGhvZDogJ3Bvc3QnCiAgfSk7Cn07CmV4cG9ydCBkZWZhdWx0IHsKICBsaXN0UGFnZTogbGlzdFBhZ2UsCiAgYWRkUm91dGU6IGFkZFJvdXRlLAogIHVwZGF0ZVJvdXRlOiB1cGRhdGVSb3V0ZSwKICByZW1vdmVSb3V0ZTogcmVtb3ZlUm91dGUKfTs="}, {"version": 3, "names": ["request", "routePath", "listPage", "params", "url", "method", "addRoute", "data", "updateRoute", "removeRoute", "id"], "sources": ["D:/Users/<USER>/Desktop/dev/sy_declare_ui/src/api/base/gateway/route.js"], "sourcesContent": ["import request from '@/libs/request'\r\n\r\nconst routePath = \"/base/route\";\r\n\r\n/**\r\n * 获取分页数据\r\n */\r\nconst listPage = (params) => {\r\n  return request({\r\n    url: routePath + '/listPage',\r\n    params,\r\n    method: 'get'\r\n  })\r\n}\r\n/**\r\n * 添加数据\r\n */\r\nconst addRoute = (data) => {\r\n  return request({\r\n    url: routePath + '/addRoute',\r\n    data,\r\n    method: 'post'\r\n  })\r\n}\r\n/**\r\n * 添加数据\r\n */\r\nconst updateRoute = (data) => {\r\n  return request({\r\n    url: routePath + '/updateRoute',\r\n    data,\r\n    method: 'post'\r\n  })\r\n}\r\n\r\n/**\r\n * 删除\r\n */\r\nconst removeRoute = (id) => {\r\n  const data = {\"id\": id}\r\n  return request({\r\n    url: routePath + '/removeRoute',\r\n    data,\r\n    method: 'post'\r\n  })\r\n}\r\nexport default {\r\n  listPage,\r\n  addRoute,\r\n  updateRoute,\r\n  removeRoute\r\n}\r\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,gBAAgB;AAEpC,IAAMC,SAAS,GAAG,aAAa;;AAE/B;AACA;AACA;AACA,IAAMC,QAAQ,GAAG,SAAXA,QAAQA,CAAIC,MAAM,EAAK;EAC3B,OAAOH,OAAO,CAAC;IACbI,GAAG,EAAEH,SAAS,GAAG,WAAW;IAC5BE,MAAM,EAANA,MAAM;IACNE,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;AACD;AACA;AACA;AACA,IAAMC,QAAQ,GAAG,SAAXA,QAAQA,CAAIC,IAAI,EAAK;EACzB,OAAOP,OAAO,CAAC;IACbI,GAAG,EAAEH,SAAS,GAAG,WAAW;IAC5BM,IAAI,EAAJA,IAAI;IACJF,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;AACD;AACA;AACA;AACA,IAAMG,WAAW,GAAG,SAAdA,WAAWA,CAAID,IAAI,EAAK;EAC5B,OAAOP,OAAO,CAAC;IACbI,GAAG,EAAEH,SAAS,GAAG,cAAc;IAC/BM,IAAI,EAAJA,IAAI;IACJF,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA,IAAMI,WAAW,GAAG,SAAdA,WAAWA,CAAIC,EAAE,EAAK;EAC1B,IAAMH,IAAI,GAAG;IAAC,IAAI,EAAEG;EAAE,CAAC;EACvB,OAAOV,OAAO,CAAC;IACbI,GAAG,EAAEH,SAAS,GAAG,cAAc;IAC/BM,IAAI,EAAJA,IAAI;IACJF,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;AACD,eAAe;EACbH,QAAQ,EAARA,QAAQ;EACRI,QAAQ,EAARA,QAAQ;EACRE,WAAW,EAAXA,WAAW;EACXC,WAAW,EAAXA;AACF,CAAC"}]}