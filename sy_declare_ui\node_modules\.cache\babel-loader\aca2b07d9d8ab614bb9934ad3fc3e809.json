{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\base\\gateway\\route\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\base\\gateway\\route\\index.vue", "mtime": 1752737748511}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\.babelrc", "mtime": 1752737748316}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["route", "gatewayLog", "autoTableHeight", "name", "data", "loading", "saving", "modalVisible", "modalTitle", "pageInfo", "total", "page", "limit", "selectType", "selectServiceList", "formItemRules", "routeDesc", "required", "message", "trigger", "routeName", "path", "formItem", "id", "serviceId", "url", "stripPrefix", "retryable", "status", "columns", "title", "key", "width", "slot", "fixed", "methods", "handleModal", "Object", "assign", "service", "handleReset", "$refs", "resetFields", "handleSubmit", "_this", "validate", "valid", "updateRoute", "then", "res", "$Message", "success", "handleSearch", "finally", "addRoute", "_this2", "listPage", "records", "parseInt", "handlePage", "current", "handlePageSize", "size", "handleRemove", "_this3", "$Modal", "confirm", "onOk", "removeRoute", "handleRefreshGateway", "_this4", "content", "refreshGateway", "mounted"], "sources": ["src/view/module/base/gateway/route/index.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <Card :shadow=\"true\">\r\n      <div class=\"search-con search-con-top\">\r\n        <ButtonGroup>\r\n          <Button :disabled=\"!hasAuthority('RouteAdd')\" class=\"search-btn\" type=\"primary\"\r\n                  @click=\"handleModal()\">\r\n            <span>添加</span>\r\n          </Button>\r\n        </ButtonGroup>\r\n      </div>\r\n      <Alert :show-icon=\"true\">谨慎添加或修改路由,如果修改不当,将影响正常访问！&nbsp;<a @click=\"handleRefreshGateway\">手动刷新网关</a></Alert>\r\n      <Table :border=\"true\" :columns=\"columns\" :max-height=\"autoTableHeight($refs.autoTableRef)\" ref=\"autoTableRef\" :data=\"data\" :loading=\"loading\">\r\n        <template v-slot:status=\"{ row }\">\r\n          <Badge v-if=\"row.status===0\" status=\"success\" text=\"启用\"/>\r\n          <Badge v-else status=\"error\" text=\"禁用\"/>\r\n        </template>\r\n        <template v-slot:routeType=\"{ row }\">\r\n          <span v-if=\"!!row.serviceId\"><Tag color=\"green\">负载均衡</Tag>{{row.serviceId}}</span>\r\n          <span v-else-if=\"!!row.url\"><Tag color=\"blue\">反向代理</Tag>{{row.url}}</span>\r\n        </template>\r\n\r\n        <template v-slot:action=\"{ row }\">\r\n          <a v-if=\"hasAuthority('RouteEdit')\" @click=\"handleModal(row)\">\r\n            编辑</a>&nbsp;\r\n          <a v-if=\"hasAuthority('RouteDel')\" @click=\"handleRemove(row)\">\r\n            删除</a>&nbsp;\r\n        </template>\r\n      </Table>\r\n      <Page :total=\"pageInfo.total\" size=\"small\" :current=\"pageInfo.page\" :page-size=\"pageInfo.limit\"\r\n            :show-elevator=\"true\" :show-sizer=\"true\" :show-total=\"true\"\r\n            @on-change=\"handlePage\" @on-page-size-change='handlePageSize'></Page>\r\n    </Card>\r\n    <Modal v-model=\"modalVisible\"\r\n           :title=\"modalTitle\"\r\n           width=\"40\"\r\n           @on-cancel=\"handleReset\">\r\n      <div>\r\n        <Form ref=\"routeForm\" :model=\"formItem\" :rules=\"formItemRules\" :label-width=\"100\">\r\n          <FormItem label=\"路由名称\" prop=\"routeDesc\">\r\n            <Input v-model=\"formItem.routeDesc\" placeholder=\"请输入内容\"></Input>\r\n          </FormItem>\r\n          <FormItem label=\"路由标识\" prop=\"routeName\">\r\n            <Input v-model=\"formItem.routeName\" placeholder=\"默认使用服务名称{application.name}\"></Input>\r\n          </FormItem>\r\n          <FormItem label=\"路由前缀\" prop=\"path\">\r\n            <Input v-model=\"formItem.path\" placeholder=\"/{path}/**\"></Input>\r\n          </FormItem>\r\n          <FormItem label=\"路由方式\">\r\n            <Select v-model=\"selectType\">\r\n              <Option value=\"service\" label=\"负载均衡(serviceId)\"></Option>\r\n              <Option value=\"url\" label=\"反向代理(url)\"></Option>\r\n            </Select>\r\n          </FormItem>\r\n          <FormItem v-if=\"selectType==='service'\" label=\"负载均衡\" prop=\"serviceId\"\r\n                    :rules=\"{required: true, message: '服务名称不能为空', trigger: 'blur'}\">\r\n            <Input v-model=\"formItem.serviceId\" placeholder=\"服务名称application.name\"></Input>\r\n          </FormItem>\r\n          <FormItem v-if=\"selectType==='url'\" label=\"反向代理\" prop=\"url\"\r\n                    :rules=\"[{required: true, message: '服务地址不能为空', trigger: 'blur'},{type: 'url', message: '请输入有效网址', trigger: 'blur'}]\">\r\n            <Input v-model=\"formItem.url\" placeholder=\"http://localhost:8080\"></Input>\r\n          </FormItem>\r\n          <FormItem label=\"忽略前缀\">\r\n            <RadioGroup v-model=\"formItem.stripPrefix\" type=\"button\">\r\n              <Radio label=\"0\">否</Radio>\r\n              <Radio label=\"1\">是</Radio>\r\n            </RadioGroup>\r\n          </FormItem>\r\n          <FormItem label=\"失败重试\">\r\n            <RadioGroup v-model=\"formItem.retryable\" type=\"button\">\r\n              <Radio label=\"0\">否</Radio>\r\n              <Radio label=\"1\">是</Radio>\r\n            </RadioGroup>\r\n          </FormItem>\r\n          <FormItem label=\"状态\">\r\n            <RadioGroup v-model=\"formItem.status\" type=\"button\">\r\n              <Radio label=\"0\">启用</Radio>\r\n              <Radio label=\"1\">禁用</Radio>\r\n            </RadioGroup>\r\n          </FormItem>\r\n        </Form>\r\n        <div class=\"drawer-footer\">\r\n          <Button type=\"default\" @click=\"handleReset\">取消</Button>&nbsp;\r\n          <Button type=\"primary\" @click=\"handleSubmit\" :loading=\"saving\"  v-if=\"hasAuthority('RouteEdit') ||hasAuthority('RouteAdd')\">保存</Button>\r\n        </div>\r\n      </div>\r\n    </Modal>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport route from '@/api/base/gateway/route'\r\nimport gatewayLog from '@/api/base/gateway/gatewayLog'\r\nimport {autoTableHeight} from '@/libs/tools.js'\r\n\r\nexport default {\r\n    name: 'gatewayRoute',\r\n    data () {\r\n      return {\r\n        autoTableHeight,\r\n        loading: false,\r\n        saving: false,\r\n        modalVisible: false,\r\n        modalTitle: '',\r\n        pageInfo: {\r\n          total: 0,\r\n          page: 1,\r\n          limit: 10\r\n        },\r\n        selectType: 'service',\r\n        selectServiceList: [],\r\n        formItemRules: {\r\n          routeDesc: [\r\n            {required: true, message: '路由名称不能为空', trigger: 'blur'}\r\n          ],\r\n          routeName: [\r\n            {required: true, message: '路由标识不能为空', trigger: 'blur'}\r\n          ],\r\n          path: [\r\n            {required: true, message: '路由前缀不能为空', trigger: 'blur'}\r\n          ]\r\n        },\r\n        formItem: {\r\n          id: '',\r\n          path: '',\r\n          serviceId: '',\r\n          url: '',\r\n          stripPrefix: 0,\r\n          retryable: 0,\r\n          status: 0,\r\n          routeName: '',\r\n          routeDesc: ''\r\n        },\r\n        columns: [\r\n          {\r\n            title: '路由名称',\r\n            key: 'routeDesc',\r\n            width: 300\r\n          },\r\n          {\r\n            title: '路由标识',\r\n            key: 'routeName',\r\n            width: 300\r\n\r\n          },\r\n          {\r\n            title: '路由前缀',\r\n            key: 'path',\r\n            width: 200\r\n          },\r\n          {\r\n            title: '路由方式',\r\n            slot: 'routeType',\r\n            width: 300\r\n          },\r\n          {\r\n            title: '忽略前缀',\r\n            key: 'stripPrefix'\r\n          },\r\n          {\r\n            title: '失败重试',\r\n            key: 'retryable'\r\n          },\r\n          {\r\n            title: '状态',\r\n            key: 'status',\r\n            slot: 'status'\r\n          },\r\n          {\r\n            title: '操作',\r\n            slot: 'action',\r\n            fixed: 'right',\r\n            width: 120\r\n          }\r\n        ],\r\n        data: []\r\n      }\r\n    },\r\n    methods: {\r\n      handleModal (data) {\r\n        if (data) {\r\n          this.modalTitle = '编辑路由'\r\n          this.formItem = Object.assign({}, this.formItem, data)\r\n        } else {\r\n          this.modalTitle = '添加路由'\r\n        }\r\n        this.formItem.status = this.formItem.status + ''\r\n        this.formItem.stripPrefix = this.formItem.stripPrefix + ''\r\n        this.formItem.retryable = this.formItem.retryable + ''\r\n        this.formItem.url = this.formItem.service ? '' : this.formItem.url\r\n        this.formItem.service = this.formItem.url ? '' : this.formItem.service\r\n        this.selectType = this.formItem.url ? 'url' : 'service'\r\n        this.modalVisible = true\r\n      },\r\n      handleReset () {\r\n        this.formItem = {\r\n          id: '',\r\n          path: '',\r\n          serviceId: '',\r\n          url: '',\r\n          stripPrefix: 0,\r\n          retryable: 0,\r\n          status: 0,\r\n          routeName: '',\r\n          routeDesc: ''\r\n        }\r\n        //重置验证\r\n        this.$refs['routeForm'].resetFields()\r\n        this.modalVisible = false\r\n        this.saving = false\r\n      },\r\n      handleSubmit () {\r\n        this.$refs['routeForm'].validate((valid) => {\r\n          if (valid) {\r\n            this.saving = true\r\n            if (this.formItem.id) {\r\n              route.updateRoute(this.formItem).then(res => {\r\n                if (res['code'] === 0) {\r\n                  this.$Message.success('保存成功')\r\n                  this.handleReset()\r\n                }\r\n                this.handleSearch()\r\n              }).finally(() => {\r\n                this.saving = false\r\n              })\r\n            } else {\r\n              route.addRoute(this.formItem).then(res => {\r\n                this.handleReset()\r\n                this.handleSearch()\r\n                if (res['code'] === 0) {\r\n                  this.$Message.success('保存成功')\r\n                }\r\n              }).finally(() => {\r\n                this.saving = false\r\n              })\r\n            }\r\n          }\r\n        })\r\n      },\r\n      handleSearch (page) {\r\n        if (page) {\r\n          this.pageInfo.page = page\r\n        }\r\n        this.loading = true\r\n        route.listPage({page: this.pageInfo.page, limit: this.pageInfo.limit}).then(res => {\r\n          this.data = res.data.records\r\n          this.pageInfo.total = parseInt(res.data.total)\r\n        }).finally(() => {\r\n          this.loading = false\r\n        })\r\n      },\r\n      handlePage (current) {\r\n        this.pageInfo.page = current\r\n        this.handleSearch()\r\n      },\r\n      handlePageSize (size) {\r\n        this.pageInfo.limit = size\r\n        this.handleSearch()\r\n      },\r\n      handleRemove (data) {\r\n        this.$Modal.confirm({\r\n          title: '确定删除吗？',\r\n          onOk: () => {\r\n            route.removeRoute(data.id).then(res => {\r\n              if (res['code'] === 0) {\r\n                this.pageInfo.page = 1\r\n                this.$Message.success('删除成功')\r\n              }\r\n              this.handleSearch()\r\n            })\r\n          }\r\n        })\r\n      },\r\n      handleRefreshGateway () {\r\n        this.$Modal.confirm({\r\n          title: '提示',\r\n          content: '将重新加载所有网关实例包括（访问权限、流量限制、IP访问限制、路由缓存），是否继续？',\r\n          onOk: () => {\r\n            gatewayLog.refreshGateway().then(res => {\r\n              if (res['code'] === 0) {\r\n                this.$Message.success('刷新成功')\r\n              }\r\n            })\r\n          }\r\n        })\r\n      }\r\n    },\r\n    mounted: function () {\r\n      this.handleSearch()\r\n    }\r\n  }\r\n</script>\r\n"], "mappings": ";AA2FA,OAAAA,KAAA;AACA,OAAAC,UAAA;AACA,SAAAC,eAAA;AAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAF,eAAA,EAAAA,eAAA;MACAG,OAAA;MACAC,MAAA;MACAC,YAAA;MACAC,UAAA;MACAC,QAAA;QACAC,KAAA;QACAC,IAAA;QACAC,KAAA;MACA;MACAC,UAAA;MACAC,iBAAA;MACAC,aAAA;QACAC,SAAA,GACA;UAAAC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,SAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAE,IAAA,GACA;UAAAJ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAG,QAAA;QACAC,EAAA;QACAF,IAAA;QACAG,SAAA;QACAC,GAAA;QACAC,WAAA;QACAC,SAAA;QACAC,MAAA;QACAR,SAAA;QACAJ,SAAA;MACA;MACAa,OAAA,GACA;QACAC,KAAA;QACAC,GAAA;QACAC,KAAA;MACA,GACA;QACAF,KAAA;QACAC,GAAA;QACAC,KAAA;MAEA,GACA;QACAF,KAAA;QACAC,GAAA;QACAC,KAAA;MACA,GACA;QACAF,KAAA;QACAG,IAAA;QACAD,KAAA;MACA,GACA;QACAF,KAAA;QACAC,GAAA;MACA,GACA;QACAD,KAAA;QACAC,GAAA;MACA,GACA;QACAD,KAAA;QACAC,GAAA;QACAE,IAAA;MACA,GACA;QACAH,KAAA;QACAG,IAAA;QACAC,KAAA;QACAF,KAAA;MACA,EACA;MACA5B,IAAA;IACA;EACA;EACA+B,OAAA;IACAC,WAAA,WAAAA,YAAAhC,IAAA;MACA,IAAAA,IAAA;QACA,KAAAI,UAAA;QACA,KAAAc,QAAA,GAAAe,MAAA,CAAAC,MAAA,UAAAhB,QAAA,EAAAlB,IAAA;MACA;QACA,KAAAI,UAAA;MACA;MACA,KAAAc,QAAA,CAAAM,MAAA,QAAAN,QAAA,CAAAM,MAAA;MACA,KAAAN,QAAA,CAAAI,WAAA,QAAAJ,QAAA,CAAAI,WAAA;MACA,KAAAJ,QAAA,CAAAK,SAAA,QAAAL,QAAA,CAAAK,SAAA;MACA,KAAAL,QAAA,CAAAG,GAAA,QAAAH,QAAA,CAAAiB,OAAA,aAAAjB,QAAA,CAAAG,GAAA;MACA,KAAAH,QAAA,CAAAiB,OAAA,QAAAjB,QAAA,CAAAG,GAAA,aAAAH,QAAA,CAAAiB,OAAA;MACA,KAAA1B,UAAA,QAAAS,QAAA,CAAAG,GAAA;MACA,KAAAlB,YAAA;IACA;IACAiC,WAAA,WAAAA,YAAA;MACA,KAAAlB,QAAA;QACAC,EAAA;QACAF,IAAA;QACAG,SAAA;QACAC,GAAA;QACAC,WAAA;QACAC,SAAA;QACAC,MAAA;QACAR,SAAA;QACAJ,SAAA;MACA;MACA;MACA,KAAAyB,KAAA,cAAAC,WAAA;MACA,KAAAnC,YAAA;MACA,KAAAD,MAAA;IACA;IACAqC,YAAA,WAAAA,aAAA;MAAA,IAAAC,KAAA;MACA,KAAAH,KAAA,cAAAI,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAF,KAAA,CAAAtC,MAAA;UACA,IAAAsC,KAAA,CAAAtB,QAAA,CAAAC,EAAA;YACAvB,KAAA,CAAA+C,WAAA,CAAAH,KAAA,CAAAtB,QAAA,EAAA0B,IAAA,WAAAC,GAAA;cACA,IAAAA,GAAA;gBACAL,KAAA,CAAAM,QAAA,CAAAC,OAAA;gBACAP,KAAA,CAAAJ,WAAA;cACA;cACAI,KAAA,CAAAQ,YAAA;YACA,GAAAC,OAAA;cACAT,KAAA,CAAAtC,MAAA;YACA;UACA;YACAN,KAAA,CAAAsD,QAAA,CAAAV,KAAA,CAAAtB,QAAA,EAAA0B,IAAA,WAAAC,GAAA;cACAL,KAAA,CAAAJ,WAAA;cACAI,KAAA,CAAAQ,YAAA;cACA,IAAAH,GAAA;gBACAL,KAAA,CAAAM,QAAA,CAAAC,OAAA;cACA;YACA,GAAAE,OAAA;cACAT,KAAA,CAAAtC,MAAA;YACA;UACA;QACA;MACA;IACA;IACA8C,YAAA,WAAAA,aAAAzC,IAAA;MAAA,IAAA4C,MAAA;MACA,IAAA5C,IAAA;QACA,KAAAF,QAAA,CAAAE,IAAA,GAAAA,IAAA;MACA;MACA,KAAAN,OAAA;MACAL,KAAA,CAAAwD,QAAA;QAAA7C,IAAA,OAAAF,QAAA,CAAAE,IAAA;QAAAC,KAAA,OAAAH,QAAA,CAAAG;MAAA,GAAAoC,IAAA,WAAAC,GAAA;QACAM,MAAA,CAAAnD,IAAA,GAAA6C,GAAA,CAAA7C,IAAA,CAAAqD,OAAA;QACAF,MAAA,CAAA9C,QAAA,CAAAC,KAAA,GAAAgD,QAAA,CAAAT,GAAA,CAAA7C,IAAA,CAAAM,KAAA;MACA,GAAA2C,OAAA;QACAE,MAAA,CAAAlD,OAAA;MACA;IACA;IACAsD,UAAA,WAAAA,WAAAC,OAAA;MACA,KAAAnD,QAAA,CAAAE,IAAA,GAAAiD,OAAA;MACA,KAAAR,YAAA;IACA;IACAS,cAAA,WAAAA,eAAAC,IAAA;MACA,KAAArD,QAAA,CAAAG,KAAA,GAAAkD,IAAA;MACA,KAAAV,YAAA;IACA;IACAW,YAAA,WAAAA,aAAA3D,IAAA;MAAA,IAAA4D,MAAA;MACA,KAAAC,MAAA,CAAAC,OAAA;QACApC,KAAA;QACAqC,IAAA,WAAAA,KAAA;UACAnE,KAAA,CAAAoE,WAAA,CAAAhE,IAAA,CAAAmB,EAAA,EAAAyB,IAAA,WAAAC,GAAA;YACA,IAAAA,GAAA;cACAe,MAAA,CAAAvD,QAAA,CAAAE,IAAA;cACAqD,MAAA,CAAAd,QAAA,CAAAC,OAAA;YACA;YACAa,MAAA,CAAAZ,YAAA;UACA;QACA;MACA;IACA;IACAiB,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,MAAA;MACA,KAAAL,MAAA,CAAAC,OAAA;QACApC,KAAA;QACAyC,OAAA;QACAJ,IAAA,WAAAA,KAAA;UACAlE,UAAA,CAAAuE,cAAA,GAAAxB,IAAA,WAAAC,GAAA;YACA,IAAAA,GAAA;cACAqB,MAAA,CAAApB,QAAA,CAAAC,OAAA;YACA;UACA;QACA;MACA;IACA;EACA;EACAsB,OAAA,WAAAA,QAAA;IACA,KAAArB,YAAA;EACA;AACA"}]}