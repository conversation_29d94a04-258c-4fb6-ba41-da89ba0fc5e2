{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\base\\user\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\base\\user\\index.vue", "mtime": 1753847200919}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\.babelrc", "mtime": 1752737748316}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9jcmVhdGVGb3JPZkl0ZXJhdG9ySGVscGVyIGZyb20gIkQ6L1VzZXJzL2FkbWluaS9EZXNrdG9wL2Rldi9zeV9kZWNsYXJlX3VpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9jcmVhdGVGb3JPZkl0ZXJhdG9ySGVscGVyLmpzIjsKaW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSAiRDovVXNlcnMvYWRtaW5pL0Rlc2t0b3AvZGV2L3N5X2RlY2xhcmVfdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL29iamVjdFNwcmVhZDIuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5lcnJvci5jYXVzZS5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLnJlZ2V4cC5leGVjLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMucmVnZXhwLnRlc3QuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5maWx0ZXIuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuaW5jbHVkZXMuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcuaW5jbHVkZXMuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3Qua2V5cy5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmpzb24uc3RyaW5naWZ5LmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvd2ViLmRvbS1jb2xsZWN0aW9ucy5mb3ItZWFjaC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LnB1c2guanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5qb2luLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkubWFwLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMucmVnZXhwLnRvLXN0cmluZy5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmNvbmNhdC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy5pdGVyYXRvci5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL3dlYi5kb20tY29sbGVjdGlvbnMuaXRlcmF0b3IuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5zb3J0LmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuc2V0LmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuZnJvbS5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LnNsaWNlLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuc3ltYm9sLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuc3ltYm9sLmRlc2NyaXB0aW9uLmpzIjsKaW1wb3J0IFJvbGUgZnJvbSAiQC9hcGkvYmFzZS9yb2xlIjsKaW1wb3J0IHsgbGlzdENvbnZlcnRUcmVlIH0gZnJvbSAiQC9saWJzL3V0aWwiOwppbXBvcnQgQXV0aG9yaXR5IGZyb20gIkAvYXBpL3N5c3RlbS9hdXRob3JpdHlfMSI7CmltcG9ydCBEZXBhcnRtZW50U2VsZWN0IGZyb20gIl9jL2RlcGFydG1lbnQtc2VsZWN0L2luZGV4LnZ1ZSI7IC8vIOW8leWFpemDqOmXqOmAieaLqee7hOS7tgppbXBvcnQgeyBhdXRvVGFibGVIZWlnaHQsIGlzRW1wdHkgfSBmcm9tICJAL2xpYnMvdG9vbHMuanMiOwppbXBvcnQgQ29tbW9uIGZyb20gJ0AvYXBpL2Jhc2ljL2NvbW1vbic7CmltcG9ydCBDb21tb25BcGkgZnJvbSAnQC9hcGkvYmFzZS9jb21tb25BcGknOwppbXBvcnQgU2hvcCBmcm9tICdAL2FwaS9iYXNmL3Nob3AnOwppbXBvcnQgVXNlciBmcm9tICdAL2FwaS9iYXNlL3VzZXInOwppbXBvcnQgeyBnZXRCeUNvbXBhbnlJZCB9IGZyb20gJ0AvYXBpL2Jhc2UvZGVwYXJ0bWVudCc7CmltcG9ydCBDaGVja2JveEdyb3VwcyBmcm9tICJAL3ZpZXcvbW9kdWxlL2Jhc2ljL1Nob3BBdXRob3JpdHkvY29tcG9uZW50cy9jaGVja2JveEdyb3Vwcy52dWUiOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogInN5c3RlbVVzZXIiLAogIGNvbXBvbmVudHM6IHsKICAgIENoZWNrYm94R3JvdXBzOiBDaGVja2JveEdyb3VwcywKICAgIERlcGFydG1lbnRTZWxlY3Q6IERlcGFydG1lbnRTZWxlY3QKICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICB2YXIgX3RoaXMgPSB0aGlzOwogICAgdmFyIGggPSB0aGlzLiRjcmVhdGVFbGVtZW50OwogICAgdmFyIHZhbGlkYXRlRW4gPSBmdW5jdGlvbiB2YWxpZGF0ZUVuKHJ1bGUsIHZhbHVlLCBjYWxsYmFjaykgewogICAgICB2YXIgcmVnID0gL15bQS1aYS16MC05X1wtXSskL2dpOwogICAgICBpZiAodmFsdWUgPT09ICIiKSB7CiAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCLnmbvlvZXlkI3kuI3og73kuLrnqboiKSk7CiAgICAgIH0gZWxzZSBpZiAodmFsdWUgIT09ICIiICYmICFyZWcudGVzdCh2YWx1ZSkpIHsKICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIuWPquWFgeiuuOWtl+avjeOAgeaVsOWtl+OAgeS4i+WIkue6vyzoi7HmlofkuK3liJLnur8iKSk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgY2FsbGJhY2soKTsKICAgICAgfQogICAgfTsKICAgIHZhciB2YWxpZGF0ZVBhc3MgPSBmdW5jdGlvbiB2YWxpZGF0ZVBhc3MocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSB7CiAgICAgIHZhciByZWcyID0gL14uezYsMTh9JC87CiAgICAgIGlmICh2YWx1ZSA9PT0gIiIpIHsKICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIuivt+i+k+WFpeWvhueggSIpKTsKICAgICAgfSBlbHNlIGlmICh2YWx1ZSAhPT0gX3RoaXMuZm9ybUl0ZW0ucGFzc3dvcmQpIHsKICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIuS4pOasoei+k+WFpeWvhueggeS4jeS4gOiHtCIpKTsKICAgICAgfSBlbHNlIGlmICh2YWx1ZSAhPT0gIiIgJiYgIXJlZzIudGVzdCh2YWx1ZSkpIHsKICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIumVv+W6pjbliLAxOOS4quWtl+espiIpKTsKICAgICAgfSBlbHNlIHsKICAgICAgICBjYWxsYmFjaygpOwogICAgICB9CiAgICB9OwogICAgdmFyIHZhbGlkYXRlUGFzc0NvbmZpcm0gPSBmdW5jdGlvbiB2YWxpZGF0ZVBhc3NDb25maXJtKHJ1bGUsIHZhbHVlLCBjYWxsYmFjaykgewogICAgICBpZiAodmFsdWUgPT09ICIiKSB7CiAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCLor7flho3mrKHovpPlhaXlr4bnoIEiKSk7CiAgICAgIH0gZWxzZSBpZiAodmFsdWUgIT09IF90aGlzLmZvcm1JdGVtLnBhc3N3b3JkKSB7CiAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCLkuKTmrKHovpPlhaXlr4bnoIHkuI3kuIDoh7QiKSk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgY2FsbGJhY2soKTsKICAgICAgfQogICAgfTsKICAgIHZhciB2YWxpZGF0ZU1vYmlsZSA9IGZ1bmN0aW9uIHZhbGlkYXRlTW9iaWxlKHJ1bGUsIHZhbHVlLCBjYWxsYmFjaykgewogICAgICB2YXIgcmVnID0gL14xXGR7MTB9JC87CiAgICAgIGlmICh2YWx1ZSAhPT0gIiIgJiYgIXJlZy50ZXN0KHZhbHVlKSkgewogICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcigi5omL5py65Y+356CB5qC85byP5LiN5q2j56GuIikpOwogICAgICB9IGVsc2UgewogICAgICAgIGNhbGxiYWNrKCk7CiAgICAgIH0KICAgIH07CiAgICByZXR1cm4gewogICAgICBBY3Rpb25UeXBlOiAnbm9uZScsCiAgICAgIGF1dG9UYWJsZUhlaWdodDogYXV0b1RhYmxlSGVpZ2h0LAogICAgICB1c2VyU3RhdHVzT3BzOiBDb21tb24udXNlclN0YXR1c09wcywKICAgICAgeWVzTm9PcHM6IENvbW1vbi55ZXNOb09wcywKICAgICAgY2hlY2tCb3hMb2FkaW5nOiBmYWxzZSwKICAgICAgbG9hZGluZzogZmFsc2UsCiAgICAgIHNhdmluZzogZmFsc2UsCiAgICAgIG1vZGFsVmlzaWJsZTogZmFsc2UsCiAgICAgIGNoYW5nZVNob3c6IHRydWUsCiAgICAgIGNoYW5nZUxvYWRpbmc6IGZhbHNlLAogICAgICBtb2RhbFRpdGxlOiAiIiwKICAgICAgY3VycmVudDogImZvcm0xIiwKICAgICAgZm9ybXM6IFsiZm9ybTEiLCAiZm9ybTIiLCAiZm9ybTMiLCAiZm9ybTQiLCAiZm9ybTUiXSwKICAgICAgc2VsZWN0TWVudXM6IFtdLAogICAgICBzZWxlY3RSb2xlczogW10sCiAgICAgIHNlbGVjdFNob3BzOiBbXSwKICAgICAgY29tcGFueUxpc3Q6IFtdLAogICAgICBkZXB0U2VhcmNoTGlzdDogW10sCiAgICAgIHNlbGVjdFBsYXRmb3JtczogW10sCiAgICAgIGNoZWNrQWxsR3JvdXA6IFtdLAogICAgICBzZWxlY3RUcmVlRGF0YTogW10sCiAgICAgIGZvcm1TZWxlY3RSb2xlczogW10sCiAgICAgIHBhZ2VJbmZvOiB7CiAgICAgICAgcGFnZTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgc29ydDogImNyZWF0ZVRpbWUiLAogICAgICAgIG9yZGVyOiAiZGVzYyIsCiAgICAgICAgdXNlck5hbWU6ICcnLAogICAgICAgIG5pY2tOYW1lOiAnJywKICAgICAgICByb2xlSWRzOiAnJywKICAgICAgICBkZXBhcnRtZW50SWQ6ICcnLAogICAgICAgIHN0YXR1czogLTEKICAgICAgfSwKICAgICAgcm9sZUFsbDogW10sCiAgICAgIGZvcm1JdGVtUnVsZXM6IHsKICAgICAgICBkZXBhcnRtZW50SWQ6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLpg6jpl6jkuI3og73kuLrnqboiCiAgICAgICAgfV0sCiAgICAgICAgY29tcGFueUlkOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi5YWs5Y+45LiN6IO95Li656m6IiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dLAogICAgICAgIHBhc3N3b3JkOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICB2YWxpZGF0b3I6IHZhbGlkYXRlUGFzcywKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dLAogICAgICAgIHBhc3N3b3JkQ29uZmlybTogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgdmFsaWRhdG9yOiB2YWxpZGF0ZVBhc3NDb25maXJtLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0sCiAgICAgICAgc2VhcmNoVmFsOiBbewogICAgICAgICAgcmVxdWlyZWQ6IGZhbHNlLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0sCiAgICAgICAgbmlja05hbWU6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLlp5PlkI3kuI3og73kuLrnqboiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0sCiAgICAgICAgZW1haWw6IFt7CiAgICAgICAgICByZXF1aXJlZDogZmFsc2UsCiAgICAgICAgICB0eXBlOiAiZW1haWwiLAogICAgICAgICAgbWVzc2FnZTogIumCrueuseagvOW8j+S4jeato+ehriIsCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIKICAgICAgICB9XSwKICAgICAgICBtb2JpbGU6IFt7CiAgICAgICAgICB2YWxpZGF0b3I6IHZhbGlkYXRlTW9iaWxlLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0KICAgICAgfSwKICAgICAgZm9ybUl0ZW06IHsKICAgICAgICB1c2VySWQ6ICIiLAogICAgICAgIHVzZXJOYW1lOiAiIiwKICAgICAgICBuaWNrTmFtZTogIiIsCiAgICAgICAgZW5OYW1lOiAiIiwKICAgICAgICBkaW5nSWQ6ICIiLAogICAgICAgIHBhc3N3b3JkOiAiIiwKICAgICAgICBwYXNzd29yZENvbmZpcm06ICIiLAogICAgICAgIHN0YXR1czogMCwKICAgICAgICBpbnRlbnRBY2Nlc3M6IDEsCiAgICAgICAgY29tcGFueUlkOiAiIiwKICAgICAgICBlbWFpbDogIiIsCiAgICAgICAgbW9iaWxlOiAiIiwKICAgICAgICB1c2VyRGVzYzogIiIsCiAgICAgICAgc2VhcmNoVmFsOiAiIiwKICAgICAgICBhdmF0YXI6ICIiLAogICAgICAgIGdyYW50Um9sZXM6IFtdLAogICAgICAgIGdyYW50U2hvcHM6IFtdLAogICAgICAgIGdyYW50QWN0aW9uczogW10sCiAgICAgICAgZ3JhbnRNZW51czogW10sCiAgICAgICAgZGVwYXJ0bWVudElkOiBudWxsCiAgICAgIH0sCiAgICAgIGNvbHVtbnM6IFt7CiAgICAgICAgdGl0bGU6ICLnmbvlvZXlkI0iLAogICAgICAgIGtleTogInVzZXJOYW1lIiwKICAgICAgICBhbGlnbjogImNlbnRlciIsCiAgICAgICAgbWluV2lkdGg6IDEyMCwKICAgICAgICByZW5kZXI6IGZ1bmN0aW9uIHJlbmRlcihoLCBfcmVmKSB7CiAgICAgICAgICB2YXIgcm93ID0gX3JlZi5yb3c7CiAgICAgICAgICByZXR1cm4gaCgic3BhbiIsIHsKICAgICAgICAgICAgImRpcmVjdGl2ZXMiOiBbewogICAgICAgICAgICAgIG5hbWU6ICJjb3B5dGV4dCIsCiAgICAgICAgICAgICAgdmFsdWU6IHJvdy51c2VyTmFtZQogICAgICAgICAgICB9XQogICAgICAgICAgfSwgW3Jvdy51c2VyTmFtZV0pOwogICAgICAgIH0KICAgICAgfSwgewogICAgICAgIHRpdGxlOiAi5aeT5ZCNIiwKICAgICAgICBrZXk6ICJuaWNrTmFtZSIsCiAgICAgICAgYWxpZ246ICJjZW50ZXIiLAogICAgICAgIG1pbldpZHRoOiAxMjAsCiAgICAgICAgcmVuZGVyOiBmdW5jdGlvbiByZW5kZXIoaCwgX3JlZjIpIHsKICAgICAgICAgIHZhciByb3cgPSBfcmVmMi5yb3c7CiAgICAgICAgICByZXR1cm4gaCgic3BhbiIsIHsKICAgICAgICAgICAgImRpcmVjdGl2ZXMiOiBbewogICAgICAgICAgICAgIG5hbWU6ICJjb3B5dGV4dCIsCiAgICAgICAgICAgICAgdmFsdWU6IHJvdy5uaWNrTmFtZQogICAgICAgICAgICB9XQogICAgICAgICAgfSwgW3Jvdy5uaWNrTmFtZV0pOwogICAgICAgIH0KICAgICAgfSwgewogICAgICAgIHRpdGxlOiAn5omA5bGe5YWs5Y+4JywKICAgICAgICBrZXk6ICdjb21wYW55TmFtZScsCiAgICAgICAgd2lkdGg6IDIwMAogICAgICB9LCB7CiAgICAgICAgdGl0bGU6ICLmiYDlsZ7pg6jpl6giLAogICAgICAgIGtleTogImRlcGFydG1lbnROYW1lIiwKICAgICAgICBtaW5XaWR0aDogMjAwCiAgICAgIH0sIHsKICAgICAgICB0aXRsZTogIuinkuiJsiIsCiAgICAgICAga2V5OiAicm9sZU5hbWVzIiwKICAgICAgICBhbGlnbjogImNlbnRlciIsCiAgICAgICAgbWluV2lkdGg6IDE1MCwKICAgICAgICBzb3J0YWJsZTogdHJ1ZQogICAgICB9LCB7CiAgICAgICAgdGl0bGU6ICLnirbmgIEiLAogICAgICAgIHNsb3Q6ICJzdGF0dXMiLAogICAgICAgIGtleTogInN0YXR1cyIsCiAgICAgICAgYWxpZ246ICJjZW50ZXIiLAogICAgICAgIHdpZHRoOiAxMDAKICAgICAgfSwgewogICAgICAgIHRpdGxlOiAi5aSW572R6K6/6ZeuIiwKICAgICAgICBrZXk6ICJpbnRlbnRBY2Nlc3MiLAogICAgICAgIGFsaWduOiAiY2VudGVyIiwKICAgICAgICBzbG90OiAiaW50ZW50QWNjZXNzIiwKICAgICAgICB3aWR0aDogMTAwCiAgICAgIH0sIHsKICAgICAgICB0aXRsZTogIuazqOWGjOaXtumXtCIsCiAgICAgICAga2V5OiAiY3JlYXRlVGltZSIsCiAgICAgICAgYWxpZ246ICJjZW50ZXIiLAogICAgICAgIHdpZHRoOiAxNDUKICAgICAgfSwgewogICAgICAgIHRpdGxlOiAn5o+P6L+wJywKICAgICAgICBrZXk6ICd1c2VyRGVzYycsCiAgICAgICAgd2lkdGg6IDE4MAogICAgICB9LCB7CiAgICAgICAgdGl0bGU6ICLlhajpg6jlupfpk7oiLAogICAgICAgIGtleTogImlzQWxsU2hvcCIsCiAgICAgICAgd2lkdGg6IDgwLAogICAgICAgIHNsb3Q6ICJpc0FsbFNob3AiLAogICAgICAgIGFsaWduOiAiY2VudGVyIgogICAgICB9LCB7CiAgICAgICAgdGl0bGU6ICLmk43kvZwiLAogICAgICAgIHNsb3Q6ICJhY3Rpb24iLAogICAgICAgIGZpeGVkOiAicmlnaHQiLAogICAgICAgIGFsaWduOiAiY2VudGVyIiwKICAgICAgICB3aWR0aDogMTIwCiAgICAgIH1dLAogICAgICB0cmVlQ29sdW1uczogW3sKICAgICAgICB0aXRsZTogIuiPnOWNlSIsCiAgICAgICAga2V5OiAibWVudU5hbWUiLAogICAgICAgIG1pbldpZHRoOiAiMjUwcHgiCiAgICAgIH0sIHsKICAgICAgICB0aXRsZTogIuaTjeS9nCIsCiAgICAgICAgdHlwZTogInRlbXBsYXRlIiwKICAgICAgICB0ZW1wbGF0ZTogIm9wZXJhdGlvbiIsCiAgICAgICAgbWluV2lkdGg6ICIyMDBweCIKICAgICAgfV0sCiAgICAgIGRhdGE6IFtdLAogICAgICBmbGFnOiBmYWxzZSwKICAgICAgYm9vbDE6IGZhbHNlLAogICAgICBib29sMjogZmFsc2UsCiAgICAgIHByb2R1Y3RDYXRlZ29yeU9wczogW10sCiAgICAgIGNoZWNrZWRJZHM6IFtdIC8v5Yu+6YCJ55qE57G755uu44CB57O75YiXaWQKICAgIH07CiAgfSwKCiAgbW91bnRlZDogZnVuY3Rpb24gbW91bnRlZCgpIHsKICAgIHRoaXMuaGFuZGxlU2VhcmNoKCk7CiAgICB0aGlzLmhhbmRsZUNvbXBhbnkoKTsKICAgIHRoaXMuZ2V0Um9sZXNBbGwoKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIGNoYW5nZVJvbGVDaGVjazogZnVuY3Rpb24gY2hhbmdlUm9sZUNoZWNrKHYpIHsKICAgICAgdmFyIF90aGlzMiA9IHRoaXM7CiAgICAgIGlmICh2KSB7CiAgICAgICAgdGhpcy5zZWxlY3RSb2xlcyA9IHRoaXMuQ29tbXNlbGVjdFJvbGVzLmZpbHRlcihmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgcmV0dXJuIF90aGlzMi5mb3JtSXRlbS5ncmFudFJvbGVzLmluY2x1ZGVzKGl0ZW0ucm9sZUlkKTsKICAgICAgICB9KTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLnNlbGVjdFJvbGVzID0gdGhpcy5Db21tc2VsZWN0Um9sZXM7CiAgICAgIH0KICAgIH0sCiAgICBxdWVyeU1lbnVOYW1lOiBmdW5jdGlvbiBxdWVyeU1lbnVOYW1lKCkgewogICAgICB2YXIgY29tbURhdGEgPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHRoaXMuQ29tbU1lbnVzKSk7CiAgICAgIHZhciBtYXBUcmVlID0gZnVuY3Rpb24gbWFwVHJlZSh2YWx1ZSwgYXJyKSB7CiAgICAgICAgdmFyIG5ld0FyciA9IFtdOwogICAgICAgIGFyci5mb3JFYWNoKGZ1bmN0aW9uIChlbGVtZW50KSB7CiAgICAgICAgICBpZiAoZWxlbWVudC5tZW51TmFtZS5pbmRleE9mKHZhbHVlKSA+IC0xKSB7CiAgICAgICAgICAgIC8vIOWIpOaWreadoeS7tgogICAgICAgICAgICBuZXdBcnIucHVzaChlbGVtZW50KTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIGlmIChlbGVtZW50LmNoaWxkcmVuICYmIGVsZW1lbnQuY2hpbGRyZW4ubGVuZ3RoID4gMCkgewogICAgICAgICAgICAgIHZhciByZURhdGEgPSBtYXBUcmVlKHZhbHVlLCBlbGVtZW50LmNoaWxkcmVuKTsKICAgICAgICAgICAgICBpZiAocmVEYXRhICYmIHJlRGF0YS5sZW5ndGggPiAwKSB7CiAgICAgICAgICAgICAgICB2YXIgb2JqID0gX29iamVjdFNwcmVhZChfb2JqZWN0U3ByZWFkKHt9LCBlbGVtZW50KSwge30sIHsKICAgICAgICAgICAgICAgICAgY2hpbGRyZW46IHJlRGF0YQogICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICBuZXdBcnIucHVzaChvYmopOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0pOwogICAgICAgIHJldHVybiBuZXdBcnI7CiAgICAgIH07CiAgICAgIGlmIChpc0VtcHR5KHRoaXMuZm9ybUl0ZW0ubWVudU5hbWUpKSB7CiAgICAgICAgdGhpcy5zZWxlY3RNZW51cyA9IHRoaXMuQ29tbU1lbnVzOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuc2VsZWN0TWVudXMgPSBtYXBUcmVlKHRoaXMuZm9ybUl0ZW0ubWVudU5hbWUsIGNvbW1EYXRhKTsKICAgICAgfQogICAgfSwKICAgIHF1ZXJ5U2hvcE5hbWU6IGZ1bmN0aW9uIHF1ZXJ5U2hvcE5hbWUoKSB7CiAgICAgIHRoaXMuaGFuZGxlTG9hZFNob3BHcmFudGVkKHRoaXMuZm9ybUl0ZW0uaWQsIHRoaXMuZm9ybUl0ZW0uc2VhcmNoVmFsKTsKICAgIH0sCiAgICBjaGVja0FsbFNob3A6IGZ1bmN0aW9uIGNoZWNrQWxsU2hvcCh2YWx1ZSkgewogICAgICB2YXIgX3RoaXMzID0gdGhpczsKICAgICAgdmFyIHJvdyA9IGFyZ3VtZW50cy5sZW5ndGggPiAxICYmIGFyZ3VtZW50c1sxXSAhPT0gdW5kZWZpbmVkID8gYXJndW1lbnRzWzFdIDoge307CiAgICAgIGlmICghcm93LmlkKSByZXR1cm47CiAgICAgIHRoaXMuJE1vZGFsLmNvbmZpcm0oewogICAgICAgIHRpdGxlOiByb3cuaXNBbGxTaG9wID09PSAxID8gIlx1Nzg2RVx1NUI5QVx1ODk4MVx1NTNENlx1NkQ4OFx1OEQ0Qlx1NEU4OCAiLmNvbmNhdChyb3cubmlja05hbWUsICIgXHU1MTY4XHU5MEU4XHU1RTk3XHU5NEZBXHU2NzQzXHU5NjUwXHU1NDE3XHVGRjFGIikgOiAiXHU3ODZFXHU1QjlBXHU4OTgxXHU3RUQ5ICIuY29uY2F0KHJvdy5uaWNrTmFtZSwgIiBcdThENEJcdTRFODhcdTUxNjhcdTkwRThcdTVFOTdcdTk0RkFcdTc2ODRcdTY3NDNcdTk2NTBcdTU0MTdcdUZGMUYiKSwKICAgICAgICBjb250ZW50OiByb3cuaXNBbGxTaG9wID09PSAxID8gIuazqO+8muatpOaTjeS9nOS8muS/neeVmeivpeeUqOaIt+eOsOacieeahOW6l+mTuuadg+mZkO+8jOS9huaYr+WQjue7reaWsOWinuW6l+mTuuaXtuS4jeS8muiHquWKqOaOiOS6iOivpeeUqOaIt+aJgOacieW6l+mTuuadg+mZkO+8gSIgOiAiIiwKICAgICAgICBva1RleHQ6ICLnoa7lrpoiLAogICAgICAgIGNhbmNlbFRleHQ6ICLlj5bmtogiLAogICAgICAgIG9uT2s6IGZ1bmN0aW9uIG9uT2soKSB7CiAgICAgICAgICBfdGhpczMuY2hhbmdlTG9hZGluZyA9IHRydWU7CiAgICAgICAgICBVc2VyLmFkZEFsbFNob3BzKHsKICAgICAgICAgICAgImlkIjogcm93LmlkLAogICAgICAgICAgICAiaXNDaGVjayI6IHZhbHVlID8gMSA6IDAKICAgICAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgICAgICBpZiAocmVzICYmIHJlc1snY29kZSddID09PSAwICYmIHJlc1snbWVzc2FnZSddID09PSAic3VjY2VzcyIpIHsKICAgICAgICAgICAgICBfdGhpczMuJE1lc3NhZ2Uuc3VjY2Vzcygi5o6I5p2D5oiQ5Yqf77yBIik7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgX3RoaXMzLmhhbmRsZVNlYXJjaCgpOwogICAgICAgICAgfSkuZmluYWxseShmdW5jdGlvbiAoKSB7CiAgICAgICAgICAgIF90aGlzMy5jaGFuZ2VMb2FkaW5nID0gZmFsc2U7CiAgICAgICAgICB9KTsKICAgICAgICB9LAogICAgICAgIG9uQ2FuY2VsOiBmdW5jdGlvbiBvbkNhbmNlbCgpIHsKICAgICAgICAgIF90aGlzMy5jaGFuZ2VTaG93ID0gZmFsc2U7CiAgICAgICAgICBzZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHsKICAgICAgICAgICAgX3RoaXMzLmNoYW5nZVNob3cgPSB0cnVlOwogICAgICAgICAgfSwgMTAwKTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIHBlcnNvblN5bjogZnVuY3Rpb24gcGVyc29uU3luKCkgewogICAgICB2YXIgX3RoaXM0ID0gdGhpczsKICAgICAgdGhpcy5mbGFnID0gdHJ1ZTsKICAgICAgVXNlci5zeW5jV2VjaGF0VXNlcigpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIGlmIChyZXNbJ2NvZGUnXSA9PT0gMCkgewogICAgICAgICAgX3RoaXM0LiRNZXNzYWdlLnN1Y2Nlc3MoIuWQjOatpeaIkOWKnyIpOwogICAgICAgICAgX3RoaXM0LmhhbmRsZVNlYXJjaCgpOwogICAgICAgIH0KICAgICAgfSkuZmluYWxseShmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXM0LmZsYWcgPSBmYWxzZTsKICAgICAgfSk7CiAgICB9LAogICAgaGFuZGxlTW9kYWw6IGZ1bmN0aW9uIGhhbmRsZU1vZGFsKGFjdGlvblR5cGUsIGRhdGEpIHsKICAgICAgdGhpcy5BY3Rpb25UeXBlID0gYWN0aW9uVHlwZTsKICAgICAgaWYgKHRoaXMuQWN0aW9uVHlwZSA9PT0gJ2FkZCcpIHsKICAgICAgICB0aGlzLmZvcm1JdGVtLnVzZXJOYW1lID0gJyc7CiAgICAgICAgdGhpcy5mb3JtSXRlbS5wYXNzd29yZCA9ICcnOwogICAgICB9CiAgICAgIHRoaXMucm93ID0gZGF0YTsKICAgICAgdGhpcy5ib29sMSA9IGZhbHNlOwogICAgICB0aGlzLmJvb2wyID0gZmFsc2U7CiAgICAgIGlmIChkYXRhKSB7CiAgICAgICAgdGhpcy5nZXREZXB0TGlzdChkYXRhLmNvbXBhbnlJZCk7CiAgICAgICAgdGhpcy5mb3JtSXRlbSA9IE9iamVjdC5hc3NpZ24oe30sIHRoaXMuZm9ybUl0ZW0sIGRhdGEpOwogICAgICB9CiAgICAgIGlmICh0aGlzLmN1cnJlbnQgPT09IHRoaXMuZm9ybXNbMF0pIHsKICAgICAgICB0aGlzLm1vZGFsVGl0bGUgPSBkYXRhID8gKHRoaXMuQWN0aW9uVHlwZSA9PT0gJ2VkaXQnID8gIue8lui+keeUqOaItyAtICIgOiAn5p+l55yL55So5oi3IC0gJykgKyBkYXRhLnVzZXJOYW1lIDogIua3u+WKoOeUqOaItyI7CiAgICAgICAgdGhpcy5tb2RhbFZpc2libGUgPSB0cnVlOwogICAgICB9CiAgICAgIGlmICh0aGlzLmN1cnJlbnQgPT09IHRoaXMuZm9ybXNbMV0pIHsKICAgICAgICB0aGlzLm1vZGFsVGl0bGUgPSBkYXRhID8gKHRoaXMuQWN0aW9uVHlwZSA9PT0gJ2VkaXQnID8gIue8lui+keWIhumFjeinkuiJsiAtICIgOiAi5p+l55yL5YiG6YWN6KeS6ImyIC0iKSArIGRhdGEudXNlck5hbWUgOiAi5YiG6YWN6KeS6ImyIjsKICAgICAgICB0aGlzLmhhbmRsZUxvYWRSb2xlcyh0aGlzLmZvcm1JdGVtLmlkKTsKICAgICAgfQogICAgICBpZiAodGhpcy5jdXJyZW50ID09PSB0aGlzLmZvcm1zWzJdKSB7CiAgICAgICAgdGhpcy5tb2RhbFRpdGxlID0gZGF0YSA/ICh0aGlzLkFjdGlvblR5cGUgPT09ICdlZGl0JyA/ICLnvJbovpHliIbphY3mnYPpmZAgLSAiIDogIuafpeeci+WIhumFjeadg+mZkCAtIikgKyBkYXRhLnVzZXJOYW1lIDogIuWIhumFjeadg+mZkCI7CiAgICAgICAgdGhpcy5oYW5kbGVMb2FkVXNlckdyYW50ZWQodGhpcy5mb3JtSXRlbS5pZCk7CiAgICAgIH0KICAgICAgaWYgKHRoaXMuY3VycmVudCA9PT0gdGhpcy5mb3Jtc1szXSkgewogICAgICAgIHRoaXMubW9kYWxUaXRsZSA9IGRhdGEgPyAodGhpcy5BY3Rpb25UeXBlID09PSAnZWRpdCcgPyAi57yW6L6R572R5bqX5p2D6ZmQIC0gIiA6ICLmn6XnnIvnvZHlupfmnYPpmZAgLSIpICsgZGF0YS51c2VyTmFtZSA6ICLnvZHlupfmnYPpmZAiOwogICAgICAgIHRoaXMuaGFuZGxlTG9hZFNob3BHcmFudGVkKHRoaXMuZm9ybUl0ZW0uaWQpOwogICAgICB9CiAgICAgIGlmICh0aGlzLmN1cnJlbnQgPT09IHRoaXMuZm9ybXNbNF0pIHsKICAgICAgICB0aGlzLm1vZGFsVGl0bGUgPSBkYXRhID8gKHRoaXMuQWN0aW9uVHlwZSA9PT0gJ2VkaXQnID8gIuS/ruaUueWvhueggSAtICIgOiAi5p+l55yL5a+G56CBIC0iKSArIGRhdGEudXNlck5hbWUgOiAi5L+u5pS55a+G56CBIjsKICAgICAgICB0aGlzLm1vZGFsVmlzaWJsZSA9IHRydWU7CiAgICAgIH0KICAgIH0sCiAgICAvL+inkuiJsuaOkuW6jwogICAgcm9sZVNvcnQ6IGZ1bmN0aW9uIHJvbGVTb3J0KG9iaikgewogICAgICB0aGlzLnBhZ2VJbmZvLm9yZGVyID0gb2JqLm9yZGVyOwogICAgICB0aGlzLnBhZ2VJbmZvLnBhZ2UgPSAxOwogICAgICB0aGlzLnBhZ2VJbmZvLnBhZ2VTaXplID0gMTA7CiAgICAgIHRoaXMuaGFuZGxlU2VhcmNoKCk7CiAgICB9LAogICAgZ2V0UnVsZUlkczogZnVuY3Rpb24gZ2V0UnVsZUlkcygpIHsKICAgICAgdGhpcy5wYWdlSW5mby5yb2xlSWRzID0gdGhpcy5mb3JtU2VsZWN0Um9sZXMuam9pbigiLCIpOwogICAgfSwKICAgIGhhbmRsZVJlc2V0Rm9ybTogZnVuY3Rpb24gaGFuZGxlUmVzZXRGb3JtKGZvcm0pIHsKICAgICAgaWYgKHRoaXMuJHJlZnNbZm9ybV0gIT09IHVuZGVmaW5lZCkgewogICAgICAgIHRoaXMuJHJlZnNbZm9ybV0ucmVzZXRGaWVsZHMoKTsKICAgICAgICBpZiAoZm9ybSA9PT0gInNlYXJjaEZvcm0iKSB7CiAgICAgICAgICB0aGlzLmZvcm1TZWxlY3RSb2xlcyA9IFtdOwogICAgICAgICAgdGhpcy5wYWdlSW5mby5kZXBhcnRtZW50SWQgPSB1bmRlZmluZWQ7CiAgICAgICAgfQogICAgICB9CiAgICB9LAogICAgaGFuZGxlUmVzZXQ6IGZ1bmN0aW9uIGhhbmRsZVJlc2V0KCkgewogICAgICB2YXIgX3RoaXM1ID0gdGhpczsKICAgICAgdGhpcy5mb3JtSXRlbSA9IHsKICAgICAgICB1c2VySWQ6ICIiLAogICAgICAgIHVzZXJOYW1lOiAiIiwKICAgICAgICBuaWNrTmFtZTogIiIsCiAgICAgICAgZW5OYW1lOiAiIiwKICAgICAgICBwYXNzd29yZDogIiIsCiAgICAgICAgcGFzc3dvcmRDb25maXJtOiAiIiwKICAgICAgICBzdGF0dXM6IDAsCiAgICAgICAgaW50ZW50QWNjZXNzOiAxLAogICAgICAgIGNvbXBhbnlJZDogIiIsCiAgICAgICAgZGVwYXJ0bWVudElkOiBudWxsLAogICAgICAgIGVtYWlsOiAiIiwKICAgICAgICBtb2JpbGU6ICIiLAogICAgICAgIHVzZXJEZXNjOiAiIiwKICAgICAgICBhdmF0YXI6ICIiLAogICAgICAgIGdyYW50Um9sZXM6IFtdLAogICAgICAgIGdyYW50U2hvcHM6IFtdLAogICAgICAgIGdyYW50TWVudXM6IFtdLAogICAgICAgIGdyYW50QWN0aW9uczogW10KICAgICAgfTsKICAgICAgLy/ph43nva7pqozor4EKICAgICAgdGhpcy5mb3Jtcy5tYXAoZnVuY3Rpb24gKGZvcm0pIHsKICAgICAgICBfdGhpczUuaGFuZGxlUmVzZXRGb3JtKGZvcm0pOwogICAgICB9KTsKICAgICAgdGhpcy5jdXJyZW50ID0gdGhpcy5mb3Jtc1swXTsKICAgICAgdGhpcy5mb3JtSXRlbS5ncmFudE1lbnVzID0gW107CiAgICAgIHRoaXMuZm9ybUl0ZW0uZ3JhbnRBY3Rpb25zID0gW107CiAgICAgIHRoaXMuc2VsZWN0VHJlZURhdGEgPSBbXTsKICAgICAgdGhpcy5tb2RhbFZpc2libGUgPSBmYWxzZTsKICAgICAgdGhpcy5zYXZpbmcgPSBmYWxzZTsKICAgICAgdGhpcy5jaGVja2VkSWRzID0gW107CiAgICB9LAogICAgaGFuZGxlU3VibWl0OiBmdW5jdGlvbiBoYW5kbGVTdWJtaXQoKSB7CiAgICAgIHZhciBfdGhpczYgPSB0aGlzOwogICAgICBpZiAodGhpcy5jdXJyZW50ID09PSB0aGlzLmZvcm1zWzBdKSB7CiAgICAgICAgdGhpcy4kcmVmc1t0aGlzLmN1cnJlbnRdLnZhbGlkYXRlKGZ1bmN0aW9uICh2YWxpZCkgewogICAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICAgIF90aGlzNi5zYXZpbmcgPSB0cnVlOwogICAgICAgICAgICBpZiAoX3RoaXM2LmZvcm1JdGVtLmlkKSB7CiAgICAgICAgICAgICAgVXNlci5lZGl0KF90aGlzNi5mb3JtSXRlbSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgICAgICAgICBpZiAocmVzWydjb2RlJ10gPT09IDApIHsKICAgICAgICAgICAgICAgICAgX3RoaXM2LiRNZXNzYWdlLnN1Y2Nlc3MoIuS/neWtmOaIkOWKnyIpOwogICAgICAgICAgICAgICAgICBfdGhpczYubW9kYWxWaXNpYmxlID0gZmFsc2U7CiAgICAgICAgICAgICAgICAgIF90aGlzNi5oYW5kbGVSZXNldCgpOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgX3RoaXM2LmhhbmRsZVNlYXJjaCgpOwogICAgICAgICAgICAgIH0pLmZpbmFsbHkoZnVuY3Rpb24gKCkgewogICAgICAgICAgICAgICAgX3RoaXM2LnNhdmluZyA9IGZhbHNlOwogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgIFVzZXIuYWRkKF90aGlzNi5mb3JtSXRlbSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgICAgICAgICBpZiAocmVzWydjb2RlJ10gPT09IDApIHsKICAgICAgICAgICAgICAgICAgX3RoaXM2LiRNZXNzYWdlLnN1Y2Nlc3MoIuS/neWtmOaIkOWKnyIpOwogICAgICAgICAgICAgICAgICBfdGhpczYuaGFuZGxlUmVzZXQoKTsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIF90aGlzNi5oYW5kbGVTZWFyY2goKTsKICAgICAgICAgICAgICB9KS5maW5hbGx5KGZ1bmN0aW9uICgpIHsKICAgICAgICAgICAgICAgIF90aGlzNi5zYXZpbmcgPSBmYWxzZTsKICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0pOwogICAgICB9CiAgICAgIGlmICh0aGlzLmN1cnJlbnQgPT09IHRoaXMuZm9ybXNbMV0gJiYgdGhpcy5mb3JtSXRlbS5pZCkgewogICAgICAgIHRoaXMuJHJlZnNbdGhpcy5jdXJyZW50XS52YWxpZGF0ZShmdW5jdGlvbiAodmFsaWQpIHsKICAgICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgICBfdGhpczYuc2F2aW5nID0gdHJ1ZTsKICAgICAgICAgICAgVXNlci5zYXZlVXNlclJvbGVzKF90aGlzNi5mb3JtSXRlbSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgICAgICAgaWYgKHJlc1snY29kZSddID09PSAwKSB7CiAgICAgICAgICAgICAgICBfdGhpczYuJE1lc3NhZ2Uuc3VjY2Vzcygi5YiG6YWN6KeS6Imy5oiQ5YqfIik7CiAgICAgICAgICAgICAgICBfdGhpczYubW9kYWxWaXNpYmxlID0gZmFsc2U7CiAgICAgICAgICAgICAgICBfdGhpczYuaGFuZGxlUmVzZXQoKTsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgX3RoaXM2LmhhbmRsZVNlYXJjaCgpOwogICAgICAgICAgICB9KS5maW5hbGx5KGZ1bmN0aW9uICgpIHsKICAgICAgICAgICAgICBfdGhpczYuc2F2aW5nID0gZmFsc2U7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfQogICAgICAgIH0pOwogICAgICB9CiAgICAgIGlmICh0aGlzLmN1cnJlbnQgPT09IHRoaXMuZm9ybXNbMl0gJiYgdGhpcy5mb3JtSXRlbS5pZCkgewogICAgICAgIHRoaXMuJHJlZnNbdGhpcy5jdXJyZW50XS52YWxpZGF0ZShmdW5jdGlvbiAodmFsaWQpIHsKICAgICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgICB2YXIgYXV0aG9yaXR5SWRzID0gX3RoaXM2LmdldENoZWNrZWRBdXRob3JpdGllcygpOwogICAgICAgICAgICAvL+ino+WGs+mDqOWIhuWLvumAiemXrumimAogICAgICAgICAgICB2YXIgZ2V0UGFyZW50SWRzID0gZnVuY3Rpb24gZ2V0UGFyZW50SWRzKCkgewogICAgICAgICAgICAgIHZhciBhcnIgPSBhcmd1bWVudHMubGVuZ3RoID4gMCAmJiBhcmd1bWVudHNbMF0gIT09IHVuZGVmaW5lZCA/IGFyZ3VtZW50c1swXSA6IFtdOwogICAgICAgICAgICAgIHZhciBfaXRlcmF0b3IgPSBfY3JlYXRlRm9yT2ZJdGVyYXRvckhlbHBlcihhcnIpLAogICAgICAgICAgICAgICAgX3N0ZXA7CiAgICAgICAgICAgICAgdHJ5IHsKICAgICAgICAgICAgICAgIGZvciAoX2l0ZXJhdG9yLnMoKTsgIShfc3RlcCA9IF9pdGVyYXRvci5uKCkpLmRvbmU7KSB7CiAgICAgICAgICAgICAgICAgIHZhciB4SXRlbSA9IF9zdGVwLnZhbHVlOwogICAgICAgICAgICAgICAgICB2YXIgY2hpbGRyZW5JZHMgPSB4SXRlbS5jaGlsZHJlbiA/IHhJdGVtLmNoaWxkcmVuLm1hcChmdW5jdGlvbiAodikgewogICAgICAgICAgICAgICAgICAgIHJldHVybiB2LmF1dGhvcml0eUlkOwogICAgICAgICAgICAgICAgICB9KSA6IFtdOwogICAgICAgICAgICAgICAgICB2YXIgZmxhZyA9IGZhbHNlOwogICAgICAgICAgICAgICAgICB2YXIgX2l0ZXJhdG9yMiA9IF9jcmVhdGVGb3JPZkl0ZXJhdG9ySGVscGVyKGNoaWxkcmVuSWRzKSwKICAgICAgICAgICAgICAgICAgICBfc3RlcDI7CiAgICAgICAgICAgICAgICAgIHRyeSB7CiAgICAgICAgICAgICAgICAgICAgZm9yIChfaXRlcmF0b3IyLnMoKTsgIShfc3RlcDIgPSBfaXRlcmF0b3IyLm4oKSkuZG9uZTspIHsKICAgICAgICAgICAgICAgICAgICAgIHZhciB5SXRlbSA9IF9zdGVwMi52YWx1ZTsKICAgICAgICAgICAgICAgICAgICAgIGlmIChhdXRob3JpdHlJZHMuaW5jbHVkZXMoeUl0ZW0pKSB7CiAgICAgICAgICAgICAgICAgICAgICAgIGZsYWcgPSB0cnVlOwogICAgICAgICAgICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgIH0gY2F0Y2ggKGVycikgewogICAgICAgICAgICAgICAgICAgIF9pdGVyYXRvcjIuZShlcnIpOwogICAgICAgICAgICAgICAgICB9IGZpbmFsbHkgewogICAgICAgICAgICAgICAgICAgIF9pdGVyYXRvcjIuZigpOwogICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgIGlmIChmbGFnID09PSB0cnVlICYmICFhdXRob3JpdHlJZHMuaW5jbHVkZXMoeEl0ZW0uYXV0aG9yaXR5SWQpKSB7CiAgICAgICAgICAgICAgICAgICAgYXV0aG9yaXR5SWRzLnB1c2goeEl0ZW0uYXV0aG9yaXR5SWQpOwogICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgIGlmICh4SXRlbS5jaGlsZHJlbikgZ2V0UGFyZW50SWRzKHhJdGVtLmNoaWxkcmVuKTsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICB9IGNhdGNoIChlcnIpIHsKICAgICAgICAgICAgICAgIF9pdGVyYXRvci5lKGVycik7CiAgICAgICAgICAgICAgfSBmaW5hbGx5IHsKICAgICAgICAgICAgICAgIF9pdGVyYXRvci5mKCk7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9OwogICAgICAgICAgICBnZXRQYXJlbnRJZHMoX3RoaXM2LnNlbGVjdE1lbnVzKTsKICAgICAgICAgICAgdmFyIF9pdGVyYXRvcjMgPSBfY3JlYXRlRm9yT2ZJdGVyYXRvckhlbHBlcihfdGhpczYuc2VsZWN0TWVudXMpLAogICAgICAgICAgICAgIF9zdGVwMzsKICAgICAgICAgICAgdHJ5IHsKICAgICAgICAgICAgICBmb3IgKF9pdGVyYXRvcjMucygpOyAhKF9zdGVwMyA9IF9pdGVyYXRvcjMubigpKS5kb25lOykgewogICAgICAgICAgICAgICAgdmFyIHhJdGVtID0gX3N0ZXAzLnZhbHVlOwogICAgICAgICAgICAgICAgdmFyIGNoaWxkcmVuSWRzID0geEl0ZW0uY2hpbGRyZW4gPyB4SXRlbS5jaGlsZHJlbi5tYXAoZnVuY3Rpb24gKHYpIHsKICAgICAgICAgICAgICAgICAgcmV0dXJuIHYuYXV0aG9yaXR5SWQ7CiAgICAgICAgICAgICAgICB9KSA6IFtdOwogICAgICAgICAgICAgICAgdmFyIGZsYWcgPSBmYWxzZTsKICAgICAgICAgICAgICAgIHZhciBfaXRlcmF0b3I0ID0gX2NyZWF0ZUZvck9mSXRlcmF0b3JIZWxwZXIoY2hpbGRyZW5JZHMpLAogICAgICAgICAgICAgICAgICBfc3RlcDQ7CiAgICAgICAgICAgICAgICB0cnkgewogICAgICAgICAgICAgICAgICBmb3IgKF9pdGVyYXRvcjQucygpOyAhKF9zdGVwNCA9IF9pdGVyYXRvcjQubigpKS5kb25lOykgewogICAgICAgICAgICAgICAgICAgIHZhciB5SXRlbSA9IF9zdGVwNC52YWx1ZTsKICAgICAgICAgICAgICAgICAgICBpZiAoYXV0aG9yaXR5SWRzLmluY2x1ZGVzKHlJdGVtKSkgewogICAgICAgICAgICAgICAgICAgICAgZmxhZyA9IHRydWU7CiAgICAgICAgICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIH0gY2F0Y2ggKGVycikgewogICAgICAgICAgICAgICAgICBfaXRlcmF0b3I0LmUoZXJyKTsKICAgICAgICAgICAgICAgIH0gZmluYWxseSB7CiAgICAgICAgICAgICAgICAgIF9pdGVyYXRvcjQuZigpOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgaWYgKGZsYWcgPT09IHRydWUgJiYgIWF1dGhvcml0eUlkcy5pbmNsdWRlcyh4SXRlbS5hdXRob3JpdHlJZCkpIHsKICAgICAgICAgICAgICAgICAgYXV0aG9yaXR5SWRzLnB1c2goeEl0ZW0uYXV0aG9yaXR5SWQpOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSBjYXRjaCAoZXJyKSB7CiAgICAgICAgICAgICAgX2l0ZXJhdG9yMy5lKGVycik7CiAgICAgICAgICAgIH0gZmluYWxseSB7CiAgICAgICAgICAgICAgX2l0ZXJhdG9yMy5mKCk7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgX3RoaXM2LnNhdmluZyA9IHRydWU7CiAgICAgICAgICAgIEF1dGhvcml0eS5ncmFudEF1dGhvcml0eUZvclVzZXIoewogICAgICAgICAgICAgIHVzZXJJZDogX3RoaXM2LmZvcm1JdGVtLmlkLAogICAgICAgICAgICAgIGF1dGhvcml0eUlkczogYXV0aG9yaXR5SWRzCiAgICAgICAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgICAgICAgIGlmIChyZXNbJ2NvZGUnXSA9PT0gMCkgewogICAgICAgICAgICAgICAgX3RoaXM2LiRNZXNzYWdlLnN1Y2Nlc3MoIuaOiOadg+aIkOWKnyIpOwogICAgICAgICAgICAgICAgX3RoaXM2Lm1vZGFsVmlzaWJsZSA9IGZhbHNlOwogICAgICAgICAgICAgICAgX3RoaXM2LmhhbmRsZVJlc2V0KCk7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIF90aGlzNi5oYW5kbGVTZWFyY2goKTsKICAgICAgICAgICAgfSkuZmluYWxseShmdW5jdGlvbiAoKSB7CiAgICAgICAgICAgICAgX3RoaXM2LnNhdmluZyA9IGZhbHNlOwogICAgICAgICAgICB9KTsKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgfQogICAgICBpZiAodGhpcy5jdXJyZW50ID09PSB0aGlzLmZvcm1zWzNdICYmIHRoaXMuZm9ybUl0ZW0uaWQpIHsKICAgICAgICB0aGlzLiRyZWZzW3RoaXMuY3VycmVudF0udmFsaWRhdGUoZnVuY3Rpb24gKHZhbGlkKSB7CiAgICAgICAgICBpZiAoIXZhbGlkKSB7CiAgICAgICAgICAgIHJldHVybjsKICAgICAgICAgIH0KICAgICAgICAgIF90aGlzNi5zYXZpbmcgPSB0cnVlOwogICAgICAgICAgdmFyIHNob3BJZHMgPSAiIjsKICAgICAgICAgIF90aGlzNi5jaGVja0FsbEdyb3VwLm1hcChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgICBpZiAoaXRlbSkgc2hvcElkcyArPSBpdGVtICsgIiwiOwogICAgICAgICAgfSk7CiAgICAgICAgICB2YXIgYWxsU2hvcElkcyA9ICIiOwogICAgICAgICAgX3RoaXM2LnNlbGVjdFNob3BzLm1hcChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgICByZXR1cm4gaXRlbVsnc2hvcExpc3QnXS5tYXAoZnVuY3Rpb24gKGVhY2gpIHsKICAgICAgICAgICAgICByZXR1cm4gYWxsU2hvcElkcyArPSBlYWNoLmlkICsgIiwiOwogICAgICAgICAgICB9KTsKICAgICAgICAgIH0pOwogICAgICAgICAgVXNlci5zYXZlVXNlclNob3BzKHsKICAgICAgICAgICAgaWQ6IF90aGlzNi5mb3JtSXRlbS5pZCwKICAgICAgICAgICAgInNob3BJZHMiOiBzaG9wSWRzLAogICAgICAgICAgICAiYWxsU2hvcElkcyI6IGFsbFNob3BJZHMKICAgICAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgICAgICBpZiAocmVzWydjb2RlJ10gPT09IDApIHsKICAgICAgICAgICAgICBfdGhpczYuJE1lc3NhZ2Uuc3VjY2Vzcygi5YiG6YWN5bqX6ZO65p2D6ZmQ5oiQ5YqfIik7CiAgICAgICAgICAgICAgX3RoaXM2LmZvcm1JdGVtLnNlYXJjaFZhbCA9ICIiOwogICAgICAgICAgICB9CiAgICAgICAgICAgIF90aGlzNi5oYW5kbGVTZWFyY2goKTsKICAgICAgICAgIH0pLmZpbmFsbHkoZnVuY3Rpb24gKCkgewogICAgICAgICAgICBfdGhpczYuc2F2aW5nID0gZmFsc2U7CiAgICAgICAgICAgIF90aGlzNi5tb2RhbFZpc2libGUgPSBmYWxzZTsKICAgICAgICAgIH0pOwogICAgICAgIH0pOwogICAgICB9CiAgICAgIGlmICh0aGlzLmN1cnJlbnQgPT09IHRoaXMuZm9ybXNbNF0gJiYgdGhpcy5mb3JtSXRlbS5pZCkgewogICAgICAgIHRoaXMuJHJlZnNbdGhpcy5jdXJyZW50XS52YWxpZGF0ZShmdW5jdGlvbiAodmFsaWQpIHsKICAgICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgICBfdGhpczYuc2F2aW5nID0gdHJ1ZTsKICAgICAgICAgICAgVXNlci51cGRhdGVQYXNzd29yZCh7CiAgICAgICAgICAgICAgaWQ6IF90aGlzNi5mb3JtSXRlbS5pZCwKICAgICAgICAgICAgICBwYXNzd29yZDogX3RoaXM2LmZvcm1JdGVtLnBhc3N3b3JkCiAgICAgICAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgICAgICAgIGlmIChyZXNbJ2NvZGUnXSA9PT0gMCkgewogICAgICAgICAgICAgICAgX3RoaXM2LiRNZXNzYWdlLnN1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpOwogICAgICAgICAgICAgICAgX3RoaXM2Lm1vZGFsVmlzaWJsZSA9IGZhbHNlOwogICAgICAgICAgICAgICAgX3RoaXM2LmhhbmRsZVJlc2V0KCk7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIF90aGlzNi5oYW5kbGVTZWFyY2goKTsKICAgICAgICAgICAgfSkuZmluYWxseShmdW5jdGlvbiAoKSB7CiAgICAgICAgICAgICAgX3RoaXM2LnNhdmluZyA9IGZhbHNlOwogICAgICAgICAgICAgIF90aGlzNi5tb2RhbFZpc2libGUgPSBmYWxzZTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9CiAgICAgICAgfSk7CiAgICAgIH0KICAgIH0sCiAgICBoYW5kbGVTZWFyY2g6IGZ1bmN0aW9uIGhhbmRsZVNlYXJjaChwYWdlKSB7CiAgICAgIHZhciBfdGhpczcgPSB0aGlzOwogICAgICBpZiAocGFnZSkgewogICAgICAgIHRoaXMucGFnZUluZm8ucGFnZSA9IHBhZ2U7CiAgICAgIH0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsKICAgICAgdmFyIHBhcmFtcyA9IF9vYmplY3RTcHJlYWQoX29iamVjdFNwcmVhZCh7fSwgdGhpcy5wYWdlSW5mbyksIHt9LCB7CiAgICAgICAgZGVwYXJ0bWVudElkczogdGhpcy5wYWdlSW5mby5kZXBhcnRtZW50SWQgPyB0aGlzLnBhZ2VJbmZvLmRlcGFydG1lbnRJZC50b1N0cmluZygpIDogdW5kZWZpbmVkCiAgICAgIH0pOwogICAgICBkZWxldGUgcGFyYW1zLmRlcGFydG1lbnRJZDsKICAgICAgVXNlci5saXN0UGFnZShwYXJhbXMpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIF90aGlzNy5kYXRhID0gcmVzLmRhdGEucmVjb3JkczsKICAgICAgICBfdGhpczcucGFnZUluZm8udG90YWwgPSBwYXJzZUludChyZXMuZGF0YS50b3RhbCk7CiAgICAgIH0pLmZpbmFsbHkoZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzNy5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIGdldENoZWNrZWRBdXRob3JpdGllczogZnVuY3Rpb24gZ2V0Q2hlY2tlZEF1dGhvcml0aWVzKCkgewogICAgICB2YXIgbWVudXMgPSB0aGlzLiRyZWZzWyJ0cmVlIl0uZ2V0Q2hlY2tlZFByb3AoImF1dGhvcml0eUlkIik7CiAgICAgIHJldHVybiBtZW51cy5jb25jYXQodGhpcy5mb3JtSXRlbS5ncmFudEFjdGlvbnMpOwogICAgfSwKICAgIGhhbmRsZUxvYWRTaG9wR3JhbnRlZDogZnVuY3Rpb24gaGFuZGxlTG9hZFNob3BHcmFudGVkKGlkLCBzaG9wTmFtZSkgewogICAgICB2YXIgdGhhdCA9IHRoaXM7CiAgICAgIHRoYXQuY2hlY2tCb3hMb2FkaW5nID0gdHJ1ZTsKICAgICAgdmFyIHJlczEgPSBVc2VyLmdldFVzZXJTaG9wcyh7CiAgICAgICAgaWQ6IGlkCiAgICAgIH0pOwogICAgICB2YXIgcmVzMiA9IFVzZXIuZ2V0VXNlclNob3BzKHsKICAgICAgICBuYW1lOiBzaG9wTmFtZQogICAgICB9KTsKICAgICAgUHJvbWlzZS5hbGwoW3JlczEsIHJlczJdKS50aGVuKGZ1bmN0aW9uIChyZXN1bHQpIHsKICAgICAgICB2YXIgcmVzMSA9IHJlc3VsdFswXSB8fCB7fTsKICAgICAgICB2YXIgcmVzMiA9IHJlc3VsdFsxXSB8fCB7fTsKICAgICAgICB2YXIgYWxsU2hvcCA9IHJlczIuZGF0YSB8fCBbXTsKICAgICAgICB2YXIgc2hvcE9iaiA9IHt9OwogICAgICAgIGFsbFNob3AubWFwKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgICB2YXIgbmFtZUtleSA9IGl0ZW1bJ25hbWVLZXknXTsKICAgICAgICAgIHZhciBsaXN0ID0gc2hvcE9ialtuYW1lS2V5XSB8fCBbXTsKICAgICAgICAgIGxpc3QucHVzaChpdGVtKTsKICAgICAgICAgIHNob3BPYmpbbmFtZUtleV0gPSBsaXN0OwogICAgICAgIH0pOwogICAgICAgIHZhciBzZWxlY3RTaG9wcyA9IFtdOwogICAgICAgIGZvciAodmFyIG5hbWVLZXkgaW4gc2hvcE9iaikgewogICAgICAgICAgc2VsZWN0U2hvcHMucHVzaCh7CiAgICAgICAgICAgICJuYW1lS2V5IjogbmFtZUtleSwKICAgICAgICAgICAgInNob3BMaXN0Ijogc2hvcE9ialtuYW1lS2V5XQogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICAgIHNlbGVjdFNob3BzLnNvcnQoZnVuY3Rpb24gKG8xLCBvMikgewogICAgICAgICAgcmV0dXJuIG8xWyduYW1lS2V5J10ubG9jYWxlQ29tcGFyZShvMlsnbmFtZUtleSddKTsKICAgICAgICB9KTsKICAgICAgICB0aGF0LnNlbGVjdFNob3BzID0gc2VsZWN0U2hvcHM7CiAgICAgICAgdmFyIGNoZWNrQXJyYXkgPSBbXTsKICAgICAgICB2YXIgdXNlclNob3AgPSAocmVzMS5kYXRhIHx8IFtdKS5tYXAoZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICAgIHJldHVybiBpdGVtWyJpZCJdOwogICAgICAgIH0pOwogICAgICAgIGZvciAodmFyIGkgPSAwOyBpIDwgc2VsZWN0U2hvcHMubGVuZ3RoOyBpKyspIHsKICAgICAgICAgIHZhciBkYXRhID0gbmV3IFNldCgpOwogICAgICAgICAgdmFyIHNob3BMaXN0ID0gc2VsZWN0U2hvcHNbaV1bInNob3BMaXN0Il07CiAgICAgICAgICBmb3IgKHZhciBqID0gMDsgaiA8IHNob3BMaXN0Lmxlbmd0aDsgaisrKSB7CiAgICAgICAgICAgIGlmICh1c2VyU2hvcC5pbmRleE9mKHNob3BMaXN0W2pdWyJpZCJdKSA+PSAwKSB7CiAgICAgICAgICAgICAgZGF0YS5hZGQoc2hvcExpc3Rbal1bImlkIl0pOwogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgICBjaGVja0FycmF5W2ldID0gQXJyYXkuZnJvbShkYXRhKTsKICAgICAgICB9CiAgICAgICAgdGhhdC5jaGVja0FsbEdyb3VwID0gY2hlY2tBcnJheTsKICAgICAgICB0aGF0LmNoZWNrQm94TG9hZGluZyA9IGZhbHNlOwogICAgICAgIHRoYXQubW9kYWxWaXNpYmxlID0gdHJ1ZTsKICAgICAgfSk7CiAgICB9LAogICAgY2hhbmdlQ2hlY2tCb3g6IGZ1bmN0aW9uIGNoYW5nZUNoZWNrQm94KHJlcykgewogICAgICAvL+WkmumAieaMiemSruaUueWPmAogICAgICB0aGlzLmNoZWNrQWxsR3JvdXBbcmVzLmluZGV4XSA9IHJlcy5kYXRhOwogICAgICAvL3RoaXMuc2V0Q2hlY2tEYXRhKCk7CiAgICAgIHRoaXMuY2hlY2tBbGxHcm91cCA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkodGhpcy5jaGVja0FsbEdyb3VwKSk7CiAgICB9LAogICAgc2V0Q2hlY2tEYXRhOiBmdW5jdGlvbiBzZXRDaGVja0RhdGEoKSB7CiAgICAgIC8v5pu05paw5aSa6YCJ5pWw5o2uCiAgICAgIHZhciBjaGVja0RhdGEgPSBuZXcgU2V0KCksCiAgICAgICAgZGF0YSA9IFstMSwgMCwgMSwgMiwgMywgNF07CiAgICAgIHZhciBjaGVjayA9IHRoaXMuY2hlY2tBbGxHcm91cEFycmF5LnNsaWNlKCk7CiAgICAgIGNoZWNrLm1hcChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgIGl0ZW0uc2xpY2UoKTsKICAgICAgICB2YXIgYXJyYXkgPSBpdGVtLmZpbHRlcihmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgcmV0dXJuIGRhdGEuaW5kZXhPZihpdGVtKSA8IDA7CiAgICAgICAgfSk7CiAgICAgICAgYXJyYXkubWFwKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgICBjaGVja0RhdGEuYWRkKGl0ZW0pOwogICAgICAgIH0pOwogICAgICB9KTsKICAgICAgdGhpcy5jaGVja0FsbEdyb3VwID0gQXJyYXkuZnJvbShjaGVja0RhdGEpOwogICAgfSwKICAgIGhhbmRsZUxvYWRVc2VyR3JhbnRlZDogZnVuY3Rpb24gaGFuZGxlTG9hZFVzZXJHcmFudGVkKHVzZXJJZCkgewogICAgICB2YXIgdGhhdCA9IHRoaXM7CiAgICAgIHZhciBwMSA9IEF1dGhvcml0eS5nZXRBdXRob3JpdHlNZW51KCIiKTsKICAgICAgdmFyIHAyID0gQXV0aG9yaXR5LmdldEF1dGhvcml0eUZvclVzZXIodXNlcklkKTsKICAgICAgdmFyIHJvbGVBdXRob3JpdGVzID0gW107CiAgICAgIFByb21pc2UuYWxsKFtwMSwgcDJdKS50aGVuKGZ1bmN0aW9uICh2YWx1ZXMpIHsKICAgICAgICB2YXIgcmVzMSA9IHZhbHVlc1swXTsKICAgICAgICB2YXIgcmVzMiA9IHZhbHVlc1sxXTsKICAgICAgICBpZiAocmVzMS5jb2RlID09PSAwICYmIHJlczEuZGF0YSkgewogICAgICAgICAgdmFyIG9wdCA9IHsKICAgICAgICAgICAgcHJpbWFyeUtleTogImlkIiwKICAgICAgICAgICAgcGFyZW50S2V5OiAicGFyZW50SWQiLAogICAgICAgICAgICBzdGFydFBpZDogIjAiCiAgICAgICAgICB9OwogICAgICAgICAgaWYgKHJlczIuY29kZSA9PT0gMCAmJiByZXMyLmRhdGEgJiYgcmVzMi5kYXRhLmxlbmd0aCA+IDApIHsKICAgICAgICAgICAgdmFyIG1lbnVzID0gW107CiAgICAgICAgICAgIHZhciBhY3Rpb25zID0gW107CiAgICAgICAgICAgIHJlczIuZGF0YS5tYXAoZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICAgICAgICBpZiAoaXRlbS5vd25lciA9PT0gInJvbGUiKSB7CiAgICAgICAgICAgICAgICByb2xlQXV0aG9yaXRlcy5wdXNoKGl0ZW0uYXV0aG9yaXR5SWQpOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAvLyDoj5zljZXmnYPpmZAKICAgICAgICAgICAgICBpZiAoaXRlbS5hdXRob3JpdHkuaW5kZXhPZigiTUVOVV8iKSAhPT0gLTEgJiYgIW1lbnVzLmluY2x1ZGVzKGl0ZW0uYXV0aG9yaXR5SWQpKSB7CiAgICAgICAgICAgICAgICBtZW51cy5wdXNoKGl0ZW0uYXV0aG9yaXR5SWQpOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAvLyDmk43kvZzmnYPpmZAKICAgICAgICAgICAgICBpZiAoaXRlbS5hdXRob3JpdHkuaW5kZXhPZigiQUNUSU9OXyIpICE9PSAtMSAmJiAhYWN0aW9ucy5pbmNsdWRlcyhpdGVtLmF1dGhvcml0eUlkKSkgewogICAgICAgICAgICAgICAgYWN0aW9ucy5wdXNoKGl0ZW0uYXV0aG9yaXR5SWQpOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIHRoYXQuZm9ybUl0ZW0uZ3JhbnRNZW51cyA9IG1lbnVzOwogICAgICAgICAgICB0aGF0LmZvcm1JdGVtLmdyYW50QWN0aW9ucyA9IGFjdGlvbnM7CiAgICAgICAgICB9CiAgICAgICAgICByZXMxLmRhdGEubWFwKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgICAgIC8vIOiPnOWNlemAieS4rQogICAgICAgICAgICBpZiAodGhhdC5mb3JtSXRlbS5ncmFudE1lbnVzLmluY2x1ZGVzKGl0ZW0uYXV0aG9yaXR5SWQpKSB7CiAgICAgICAgICAgICAgaXRlbS5faXNDaGVja2VkID0gdHJ1ZTsKICAgICAgICAgICAgICAvLyDlvZLlsZ7op5LoibLmnYPpmZAs56aB5q2i5o6I5p2DCiAgICAgICAgICAgICAgaWYgKHJvbGVBdXRob3JpdGVzLmluY2x1ZGVzKGl0ZW0uYXV0aG9yaXR5SWQpKSB7CiAgICAgICAgICAgICAgICAvLyDmj5Lku7bkuI3mlK/mjIEs56aB55SoCiAgICAgICAgICAgICAgICBpdGVtLmRpc2FibGVkID0gdHJ1ZTsKICAgICAgICAgICAgICAgIGl0ZW0ubWVudU5hbWUgKz0gIiAo56aB5q2i5Yu+6YCJKSI7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9CiAgICAgICAgICAgIC8vIOWKn+iDveadg+mZkAogICAgICAgICAgICBpdGVtLmFjdGlvbkxpc3QubWFwKGZ1bmN0aW9uIChhY3Rpb24pIHsKICAgICAgICAgICAgICBpZiAocm9sZUF1dGhvcml0ZXMuaW5jbHVkZXMoYWN0aW9uLmF1dGhvcml0eUlkKSkgewogICAgICAgICAgICAgICAgYWN0aW9uLmRpc2FibGVkID0gdHJ1ZTsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfSk7CiAgICAgICAgICB0aGF0LnNlbGVjdE1lbnVzID0gbGlzdENvbnZlcnRUcmVlKHJlczEuZGF0YSwgb3B0KTsKICAgICAgICAgIHRoYXQuQ29tbU1lbnVzID0gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeSh0aGF0LnNlbGVjdE1lbnVzKSk7CiAgICAgICAgfQogICAgICAgIHRoYXQubW9kYWxWaXNpYmxlID0gdHJ1ZTsKICAgICAgfSk7CiAgICB9LAogICAgaGFuZGxlTG9hZFJvbGVzOiBmdW5jdGlvbiBoYW5kbGVMb2FkUm9sZXModXNlcklkKSB7CiAgICAgIGlmICghdXNlcklkKSB7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIHZhciB0aGF0ID0gdGhpczsKICAgICAgdmFyIHAxID0gUm9sZS5nZXRBbGwoKTsKICAgICAgdmFyIHAyID0gVXNlci5nZXRVc2VyUm9sZXModXNlcklkKTsKICAgICAgUHJvbWlzZS5hbGwoW3AxLCBwMl0pLnRoZW4oZnVuY3Rpb24gKHZhbHVlcykgewogICAgICAgIHZhciByZXMxID0gdmFsdWVzWzBdOwogICAgICAgIHZhciByZXMyID0gdmFsdWVzWzFdOwogICAgICAgIGlmIChyZXMxLmNvZGUgPT09IDApIHsKICAgICAgICAgIHZhciByZXN1bHQxID0gW107CiAgICAgICAgICByZXMxLmRhdGEubWFwKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgICAgIGlmIChpdGVtLnJvbGVJZCAhPT0gMSkgewogICAgICAgICAgICAgIHJlc3VsdDEucHVzaChpdGVtKTsKICAgICAgICAgICAgfQogICAgICAgICAgfSk7CiAgICAgICAgICB0aGF0LnNlbGVjdFJvbGVzID0gcmVzdWx0MTsKICAgICAgICAgIHRoYXQuQ29tbXNlbGVjdFJvbGVzID0gcmVzdWx0MTsKICAgICAgICB9CiAgICAgICAgaWYgKHJlczIuY29kZSA9PT0gMCkgewogICAgICAgICAgdmFyIHJlc3VsdCA9IFtdOwogICAgICAgICAgcmVzMi5kYXRhLm1hcChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgICByZXN1bHQucHVzaChpdGVtLmlkKTsKICAgICAgICAgIH0pOwogICAgICAgICAgdGhhdC5mb3JtSXRlbS5ncmFudFJvbGVzID0gcmVzdWx0OwogICAgICAgIH0KICAgICAgICB0aGF0Lm1vZGFsVmlzaWJsZSA9IHRydWU7CiAgICAgIH0pOwogICAgfSwKICAgIGhhbmRsZVBhZ2U6IGZ1bmN0aW9uIGhhbmRsZVBhZ2UoY3VycmVudCkgewogICAgICB0aGlzLnBhZ2VJbmZvLnBhZ2UgPSBjdXJyZW50OwogICAgICB0aGlzLmhhbmRsZVNlYXJjaCgpOwogICAgfSwKICAgIGhhbmRsZVBhZ2VTaXplOiBmdW5jdGlvbiBoYW5kbGVQYWdlU2l6ZShzaXplKSB7CiAgICAgIHRoaXMucGFnZUluZm8ubGltaXQgPSBzaXplOwogICAgICB0aGlzLmhhbmRsZVNlYXJjaCgpOwogICAgfSwKICAgIGhhbmRsZUNvbXBhbnk6IGZ1bmN0aW9uIGhhbmRsZUNvbXBhbnkoKSB7CiAgICAgIHZhciBfdGhpczggPSB0aGlzOwogICAgICBDb21tb25BcGkuZ2V0QWxsQ29tcGFueSgpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIF90aGlzOC5jb21wYW55TGlzdCA9IHJlcy5kYXRhOwogICAgICB9KTsKICAgIH0sCiAgICBnZXREZXB0TGlzdDogZnVuY3Rpb24gZ2V0RGVwdExpc3QodmFsKSB7CiAgICAgIHZhciBfdGhpczkgPSB0aGlzOwogICAgICBpZiAodmFsICE9IG51bGwpIHsKICAgICAgICB0aGlzLmZvcm1JdGVtLmRlcGFydG1lbnRJZCA9IG51bGw7CiAgICAgICAgZ2V0QnlDb21wYW55SWQodmFsKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICAgIF90aGlzOS5kZXB0U2VhcmNoTGlzdCA9IHJlcy5kYXRhOwogICAgICAgICAgdmFyIG9wdCA9IHsKICAgICAgICAgICAgcHJpbWFyeUtleTogImlkIiwKICAgICAgICAgICAgcGFyZW50S2V5OiAicGFyZW50SWQiLAogICAgICAgICAgICBzdGFydFBpZDogIjAiCiAgICAgICAgICB9OwogICAgICAgICAgX3RoaXM5LnNlbGVjdFRyZWVEYXRhID0gbGlzdENvbnZlcnRUcmVlKHJlcy5kYXRhLCBvcHQpOwogICAgICAgIH0pOwogICAgICB9CiAgICB9LAogICAgZ2V0Um9sZXNBbGw6IGZ1bmN0aW9uIGdldFJvbGVzQWxsKCkgewogICAgICB2YXIgX3RoaXMxMCA9IHRoaXM7CiAgICAgIENvbW1vbkFwaS5nZXRBbGxSb2xlKCkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgaWYgKHJlc1snY29kZSddID09PSAwKSB7CiAgICAgICAgICByZXMuZGF0YS5tYXAoZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICAgICAgaWYgKGl0ZW0ucm9sZUlkICE9PSAiMSIpIF90aGlzMTAucm9sZUFsbC5wdXNoKGl0ZW0pOwogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICBoYW5kbGVUYWJDbGljazogZnVuY3Rpb24gaGFuZGxlVGFiQ2xpY2sobmFtZSkgewogICAgICB0aGlzLmN1cnJlbnQgPSBuYW1lOwogICAgICB0aGlzLmhhbmRsZU1vZGFsKHRoaXMuYWN0aW9uVHlwZSwgdGhpcy5yb3cpOwogICAgfSwKICAgIHNldFRyZWVEYXRhOiBmdW5jdGlvbiBzZXRUcmVlRGF0YSgpIHsKICAgICAgdmFyIF90aGlzMTEgPSB0aGlzOwogICAgICB2YXIgYXJyID0gYXJndW1lbnRzLmxlbmd0aCA+IDAgJiYgYXJndW1lbnRzWzBdICE9PSB1bmRlZmluZWQgPyBhcmd1bWVudHNbMF0gOiBbXTsKICAgICAgdmFyIHJlc2V0ID0gYXJndW1lbnRzLmxlbmd0aCA+IDEgJiYgYXJndW1lbnRzWzFdICE9PSB1bmRlZmluZWQgPyBhcmd1bWVudHNbMV0gOiBmYWxzZTsKICAgICAgdmFyIGNoZWNrZWRJZHMgPSB0aGlzLmNoZWNrZWRJZHM7CiAgICAgIGFyci5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgaWYgKHJlc2V0ID09PSB0cnVlKSB7CiAgICAgICAgICBpdGVtLmluZGV0ZXJtaW5hdGUgPSBmYWxzZTsKICAgICAgICAgIGl0ZW0uY2hlY2tlZCA9IGZhbHNlOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICBpdGVtLnRpdGxlID0gaXRlbS5kZXNjcmlwdGlvbjsKICAgICAgICAgIGl0ZW0uZXhwYW5kID0gaXRlbS5sZXZlbCA8IDI7CiAgICAgICAgICBpdGVtLmNoZWNrZWQgPSBjaGVja2VkSWRzLmluY2x1ZGVzKGl0ZW0uaWQpOwogICAgICAgIH0KICAgICAgICBpZiAoaXRlbS5jaGlsZHJlbiAmJiBpdGVtLmNoaWxkcmVuLmxlbmd0aCA+IDApIHsKICAgICAgICAgIF90aGlzMTEuc2V0VHJlZURhdGEoaXRlbS5jaGlsZHJlbiwgcmVzZXQpOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgb25WaXNpYmxlQ2hhbmdlOiBmdW5jdGlvbiBvblZpc2libGVDaGFuZ2UodmlzaWJsZSkgewogICAgICBpZiAodmlzaWJsZSA9PT0gZmFsc2UpIHsKICAgICAgICB0aGlzLnNldFRyZWVEYXRhKHRoaXMucHJvZHVjdENhdGVnb3J5T3BzLCB0cnVlKTsKICAgICAgfQogICAgfQogIH0KfTs="}, {"version": 3, "names": ["Role", "listConvertTree", "Authority", "DepartmentSelect", "autoTableHeight", "isEmpty", "Common", "CommonApi", "Shop", "User", "getByCompanyId", "CheckboxGroups", "name", "components", "data", "_this", "h", "$createElement", "validateEn", "rule", "value", "callback", "reg", "Error", "test", "validatePass", "reg2", "formItem", "password", "validatePassConfirm", "validateMobile", "ActionType", "userStatusOps", "yesNoOps", "checkBoxLoading", "loading", "saving", "modalVisible", "changeShow", "changeLoading", "modalTitle", "current", "forms", "selectMenus", "selectRoles", "selectShops", "companyList", "deptSearchList", "selectPlatforms", "checkAllGroup", "selectTreeData", "formSelectRoles", "pageInfo", "page", "pageSize", "sort", "order", "userName", "nick<PERSON><PERSON>", "roleIds", "departmentId", "status", "roleAll", "formItemRules", "required", "message", "companyId", "trigger", "validator", "passwordConfirm", "searchVal", "email", "type", "mobile", "userId", "enName", "dingId", "intentAccess", "userDesc", "avatar", "grantRoles", "grantShops", "grantActions", "grantMenus", "columns", "title", "key", "align", "min<PERSON><PERSON><PERSON>", "render", "_ref", "row", "_ref2", "width", "sortable", "slot", "fixed", "treeColumns", "template", "flag", "bool1", "bool2", "productCategoryOps", "checkedIds", "mounted", "handleSearch", "handleCompany", "getRolesAll", "methods", "changeRoleCheck", "v", "_this2", "CommselectRoles", "filter", "item", "includes", "roleId", "queryMenuName", "commData", "JSON", "parse", "stringify", "CommMenus", "mapTree", "arr", "newArr", "for<PERSON>ach", "element", "menuName", "indexOf", "push", "children", "length", "reData", "obj", "_objectSpread", "queryShopName", "handleLoadShopGranted", "id", "checkAllShop", "_this3", "arguments", "undefined", "$Modal", "confirm", "isAllShop", "concat", "content", "okText", "cancelText", "onOk", "addAllShops", "then", "res", "$Message", "success", "finally", "onCancel", "setTimeout", "personSyn", "_this4", "syncWechatUser", "handleModal", "actionType", "getDeptList", "Object", "assign", "handleLoadRoles", "handleLoadUserGranted", "roleSort", "getRuleIds", "join", "handleResetForm", "form", "$refs", "resetFields", "handleReset", "_this5", "map", "handleSubmit", "_this6", "validate", "valid", "edit", "add", "saveUserRoles", "authorityIds", "getCheckedAuthorities", "getParentIds", "_iterator", "_createForOfIteratorHelper", "_step", "s", "n", "done", "xItem", "childrenIds", "authorityId", "_iterator2", "_step2", "yItem", "err", "e", "f", "_iterator3", "_step3", "_iterator4", "_step4", "grantAuthorityForUser", "shopIds", "allShopIds", "each", "saveUserShops", "updatePassword", "_this7", "params", "departmentIds", "toString", "listPage", "records", "total", "parseInt", "menus", "getCheckedProp", "shopName", "that", "res1", "getUserShops", "res2", "Promise", "all", "result", "allShop", "shopObj", "<PERSON><PERSON><PERSON>", "list", "o1", "o2", "localeCompare", "checkArray", "userShop", "i", "Set", "shopList", "j", "Array", "from", "changeCheckBox", "index", "setCheckData", "checkData", "check", "checkAllGroupArray", "slice", "array", "p1", "getAuthorityMenu", "p2", "getAuthorityForUser", "roleAuthorites", "values", "code", "opt", "<PERSON><PERSON><PERSON>", "parent<PERSON><PERSON>", "startPid", "actions", "owner", "authority", "_isChecked", "disabled", "actionList", "action", "getAll", "getUserRoles", "result1", "handlePage", "handlePageSize", "size", "limit", "_this8", "getAllCompany", "val", "_this9", "_this10", "getAllRole", "handleTabClick", "setTreeData", "_this11", "reset", "indeterminate", "checked", "description", "expand", "level", "onVisibleChange", "visible"], "sources": ["src/view/module/base/user/index.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <Card class=\"userManage\">\r\n      <Form ref=\"searchForm\" :model=\"pageInfo\" class=\"searchForm\" inline\r\n            @keydown.enter.native=\" e => {e.preventDefault();handleSearch(1);}\">\r\n        <FormItem prop=\"userName\">\r\n          <Input style=\"width: 160px\" v-model=\"pageInfo.userName\" placeholder=\"请输入登录名\"/>\r\n        </FormItem>\r\n        <FormItem prop=\"nickName\">\r\n          <Input style=\"width: 180px\" v-model=\"pageInfo.nickName\" placeholder=\"请输入姓名\"/>\r\n        </FormItem>\r\n        <FormItem prop=\"formSelectRoles\">\r\n          <Select v-model=\"formSelectRoles\" multiple=\"multiple\" placeholder=\"角色\" @on-change=\"getRuleIds\"\r\n                  :max-tag-count=\"1\" style=\"width:250px\" :multiple=\"true\" :filterable=\"true\" :transfer=\"false\">\r\n            <Option v-for=\"(item, index) in roleAll\" :value=\"item.id\" :key=\"index\">{{ item.roleName }}</Option>\r\n          </Select>\r\n        </FormItem>\r\n\r\n        <FormItem prop=\"departmentId\">\r\n          <DepartmentSelect v-model=\"pageInfo.departmentId\" placeholder=\"所属部门\" groupName=\"user-manage-search\"/>\r\n        </FormItem>\r\n        <FormItem prop=\"status\">\r\n          <Select v-model=\"pageInfo.status\" style=\"width: 100px\" placeholder=\"请选择状态\" :clearable=\"false\" :transfer=\"true\">\r\n            <Option v-for=\"v in userStatusOps\" :value=\"v.key\" :key=\"v.key\">{{ v.name }}</Option>\r\n          </Select>\r\n        </FormItem>\r\n        <FormItem :label-width=\"0\">\r\n          <Button type=\"primary\" @click=\"handleSearch(1)\">查询</Button>\r\n          <Button @click=\"handleResetForm('searchForm')\" style=\"\">重置</Button>\r\n        </FormItem>\r\n      </Form>\r\n      <div class=\"search-con search-con-top\">\r\n        <Button @click=\"personSyn('searchForm')\" :loading=\"flag\" v-if=\"hasAuthority('userSync')\">人员同步</Button>\r\n        <Button type=\"primary\" @click=\"handleModal('add',null)\" v-if=\"hasAuthority('userAdd')\" style=\"margin-left: 15px\">\r\n          <span>添加</span>\r\n        </Button>\r\n      </div>\r\n      <Table :border=\"true\" :columns=\"columns\" :data=\"data\" @on-sort-change=\"roleSort\" :loading=\"loading\"\r\n             ref=\"autoTableRef\" :max-height=\"autoTableHeight($refs.autoTableRef)\">\r\n        <template v-slot:status=\"{row}\">\r\n          <Badge v-for=\"v in userStatusOps\" :text=\"v.name\" v-if=\"v.key === row.status\"\r\n                 :status=\"v.key === 0?'success':(v.key ===1?'error':'warning')\" v-bind:key=\"v.key\"></Badge>\r\n        </template>\r\n        <template v-slot:intentAccess=\"{ row }\">\r\n          <Badge v-for=\"v in yesNoOps\" :text=\"v.name\" v-if=\"v.key === row.intentAccess\"\r\n                 :status=\"v.key === 0?'success':'warning'\" v-bind:key=\"v.key\"></Badge>\r\n        </template>\r\n        <template v-slot:action=\"{ row }\">\r\n          <a @click=\"handleModal('view',row)\">查看</a>&nbsp\r\n          <a @click=\"handleModal('edit',row)\" v-if=\"hasAuthority('userEdit')\" >编辑</a>&nbsp\r\n        </template>\r\n        <template v-slot:isAllShop=\"{ row }\">\r\n          <Checkbox\r\n              v-model=\"row.isAllShop === 1\"\r\n              @on-change=\"value => checkAllShop(value, row)\"\r\n              v-if=\"changeShow\" :disabled=\"changeLoading\"\r\n          ></Checkbox>\r\n        </template>\r\n      </Table>\r\n      <Page :transfer=\"true\" :total=\"pageInfo.total\" :current=\"pageInfo.page\" :page-size=\"pageInfo.limit\" size=\"small\"\r\n            :show-elevator=\"true\"\r\n            :show-sizer=\"true\" :show-total=\"true\" @on-change=\"handlePage\" @on-page-size-change=\"handlePageSize\">\r\n      </Page>\r\n    </Card>\r\n    <Modal v-model=\"modalVisible\" :title=\"modalTitle\" width=\"40\" :styles=\"{ top: '20px' }\" @on-cancel=\"handleReset\"\r\n           @on-visible-change=\"onVisibleChange\">\r\n      <Tabs @on-click=\"handleTabClick\" :value=\"current\">\r\n        <TabPane label=\"用户信息\" name=\"form1\">\r\n          <Form v-show=\"current === 'form1'\" ref=\"form1\" :model=\"formItem\" :rules=\"formItemRules\" :label-width=\"100\">\r\n            <FormItem label=\"姓名\" prop=\"nickName\">\r\n              <Input v-model=\"formItem.nickName\" placeholder=\"请输入\"></Input>\r\n            </FormItem>\r\n            <FormItem label=\"登录名\" prop=\"userName\">\r\n              <Input :disabled=\"!!formItem.id\" v-model=\"formItem.userName\" placeholder=\"请输入\"></Input>\r\n            </FormItem>\r\n            <FormItem v-if=\"!formItem.id\" label=\"登录密码\" prop=\"password\">\r\n              <Input type=\"password\" v-model=\"formItem.password\" placeholder=\"请输入内容\"></Input>\r\n            </FormItem>\r\n            <FormItem v-if=\"!formItem.id\" label=\"再次确认密码\" prop=\"passwordConfirm\">\r\n              <Input type=\"password\" v-model=\"formItem.passwordConfirm\" placeholder=\"请输入内容\"></Input>\r\n            </FormItem>\r\n            <FormItem label=\"所属公司\" prop=\"companyId\">\r\n              <Select v-model=\"formItem.companyId\" @on-change=\"getDeptList\">\r\n                <Option v-for=\"(item, index) in companyList\" :value=\"item.id\" :key=\"index\">{{\r\n                    item.companyName\r\n                  }}\r\n                </Option>\r\n              </Select>\r\n            </FormItem>\r\n            <FormItem label=\"所属部门\" prop=\"departmentId\">\r\n              <DepartmentSelect v-model=\"formItem.departmentId\" groupName=\"user-manage-edit\" width=\"100%\"\r\n                                :companyId=\"formItem.companyId || '0'\" :appendToBody=\"false\"/>\r\n            </FormItem>\r\n            <FormItem label=\"邮箱\" prop=\"email\">\r\n              <Input v-model=\"formItem.email\" placeholder=\"请输入内容\"></Input>\r\n            </FormItem>\r\n            <FormItem label=\"手机号\" prop=\"mobile\">\r\n              <Input v-model=\"formItem.mobile\" placeholder=\"请输入内容\"></Input>\r\n            </FormItem>\r\n            <FormItem label=\"外网访问\">\r\n              <RadioGroup v-model=\"formItem.intentAccess\" type=\"button\">\r\n                <Radio v-for=\"v in yesNoOps\" :label=\"v.key\" v-bind:key=\"v.key\">{{ v.name }}</Radio>\r\n              </RadioGroup>\r\n            </FormItem>\r\n            <FormItem label=\"状态\">\r\n              <RadioGroup v-model=\"formItem.status\" type=\"button\">\r\n                <Radio v-for=\"v in userStatusOps\" :label=\"v.key\" v-if=\"v.key !== -1\" v-bind:key=\"v.key\">{{ v.name }}</Radio>\r\n              </RadioGroup>\r\n            </FormItem>\r\n            <FormItem label=\"描述\">\r\n              <Input v-model=\"formItem.userDesc\" type=\"textarea\" placeholder=\"请输入内容\"></Input>\r\n            </FormItem>\r\n          </Form>\r\n        </TabPane>\r\n        <TabPane :disabled=\"!formItem.id\" label=\"分配角色\" name=\"form2\">\r\n          <Form v-show=\"current === 'form2'\" ref=\"form2\" :model=\"formItem\" :label-width=\"100\" :rules=\"formItemRules\">\r\n            <FormItem label=\"\" prop=\"grantRoles\" style=\"margin-bottom:0;\">\r\n              <Checkbox v-model=\"bool1\" @on-change=\"changeRoleCheck\">只显示已勾选的角色</Checkbox>\r\n            </FormItem>\r\n            <FormItem label=\"分配角色\" prop=\"grantRoles\">\r\n              <CheckboxGroup v-model=\"formItem.grantRoles\">\r\n                <Checkbox v-for=\"item in selectRoles\" :key=\"item.id\" :label=\"item.id\"><span>{{item.roleName }}</span></Checkbox>\r\n              </CheckboxGroup>\r\n            </FormItem>\r\n          </Form>\r\n        </TabPane>\r\n        <TabPane :disabled=\"!formItem.id\" label=\"分配权限\" name=\"form3\">\r\n          <Alert type=\"info\" :show-icon=\"true\">支持用户单独分配功能权限<code>(除角色已经分配菜单功能,禁止勾选!)</code>\r\n          </Alert>\r\n          <Form @keydown.enter.native=\" e => {e.preventDefault();}\" v-show=\"current === 'form3'\" ref=\"form3\"\r\n                :model=\"formItem\" :rules=\"formItemRules\" :label-width=\"100\">\r\n            <FormItem label=\"菜单查询\" prop=\"menuName\" style=\"margin-bottom:10px;\">\r\n              <Input v-model=\"formItem.menuName\" @on-enter=\"queryMenuName\" placeholder=\"请输入菜单名称，且点击回车\"\r\n                     style=\"width:200px;margin-right:10px\"/>\r\n            </FormItem>\r\n            <FormItem label=\"功能菜单\" prop=\"grantMenus\">\r\n              <tree-table ref=\"tree\" style=\"max-height:450px;overflow: auto\" expand-key=\"menuName\" :expand-type=\"false\" :is-fold=\"false\"\r\n                          :tree-type=\"true\" :selectable=\"true\" :columns=\"treeColumns\" :data=\"selectMenus\">\r\n                <template v-slot:operation=\"scope\">\r\n                  <CheckboxGroup v-model=\"formItem.grantActions\">\r\n                    <Checkbox :disabled=\"item.disabled\" v-for=\"item in scope.row['actionList']\" :label=\"item['authorityId']\" v-bind:key=\"item['authorityId']\">\r\n                      <span :title=\"item.actionDesc\">{{ item.actionName }}</span>\r\n                    </Checkbox>\r\n                  </CheckboxGroup>\r\n                </template>\r\n              </tree-table>\r\n            </FormItem>\r\n          </Form>\r\n        </TabPane>\r\n        <TabPane :disabled=\"!formItem.id\" label=\"授权网店\" name=\"form4\">\r\n          <Form @keydown.enter.native=\" e => {e.preventDefault();}\" v-show=\"current === 'form4'\" ref=\"form4\" :model=\"formItem\"\r\n                :label-width=\"100\" :rules=\"formItemRules\">\r\n            <FormItem label=\"店铺名称\" prop=\"searchVal\">\r\n              <Input v-model=\"formItem.searchVal\" @on-enter=\"queryShopName\" placeholder=\"请输入店铺名称，点击回车健\"/>\r\n            </FormItem>\r\n            <div style=\"position:relative;padding-left:40px;\">\r\n              <Spin :fix=\"true\" v-if=\"checkBoxLoading\" size=\"large\"></Spin>\r\n              <div v-for=\"(items1, index) in selectShops\" style=\"border-bottom: 1px solid #e9e9e9;padding-bottom:6px;margin-bottom:6px;\">\r\n                <CheckboxGroups ref=\"CheckboxGroupsEls\" :checkItem=\"items1\" :index=\"index\" :checkAllGroup=\"checkAllGroup[index]\"\r\n                    :loading=\"checkBoxLoading\" @onChange=\"changeCheckBox\"\r\n                ></CheckboxGroups>\r\n              </div>\r\n            </div>\r\n          </Form>\r\n        </TabPane>\r\n        <TabPane :disabled=\"!formItem.id\" label=\"修改密码\" name=\"form5\">\r\n          <Form v-show=\"current === 'form5'\" ref=\"form5\" :model=\"formItem\" :rules=\"formItemRules\" :label-width=\"100\">\r\n            <FormItem label=\"登录名\" prop=\"userName\">\r\n              <Input :disabled=\"!!formItem.id\" v-model=\"formItem.userName\" placeholder=\"请输入内容\"></Input>\r\n            </FormItem>\r\n            <FormItem label=\"登录密码\" prop=\"password\">\r\n              <Input type=\"password\" v-model=\"formItem.password\" placeholder=\"请输入内容\"></Input>\r\n            </FormItem>\r\n            <FormItem label=\"再次确认密码\" prop=\"passwordConfirm\">\r\n              <Input type=\"password\" v-model=\"formItem.passwordConfirm\" placeholder=\"请输入内容\"></Input>\r\n            </FormItem>\r\n          </Form>\r\n        </TabPane>\r\n      </Tabs>\r\n      <div class=\"drawer-footer\" style=\"border-top: none\">\r\n        <Button type=\"default\" @click=\"handleReset\">取消</Button>&nbsp;&nbsp;&nbsp;\r\n        <Button type=\"primary\" v-if=\"ActionType !== 'view'\" @click=\"handleSubmit\" :loading=\"saving\" :disabled=\"!hasAuthority('userEdit')\">保存</Button>\r\n      </div>\r\n    </Modal>\r\n  </div>\r\n</template>\r\n<script>\r\nimport Role from \"@/api/base/role\";\r\nimport {listConvertTree} from \"@/libs/util\";\r\nimport Authority from \"@/api/system/authority_1\";\r\nimport DepartmentSelect from \"_c/department-select/index.vue\"; // 引入部门选择组件\r\nimport {autoTableHeight, isEmpty} from \"@/libs/tools.js\";\r\nimport Common from '@/api/basic/common'\r\nimport CommonApi from '@/api/base/commonApi'\r\nimport Shop from '@/api/basf/shop'\r\nimport User from '@/api/base/user'\r\nimport {getByCompanyId} from '@/api/base/department'\r\nimport CheckboxGroups from \"@/view/module/basic/ShopAuthority/components/checkboxGroups.vue\";\r\nexport default {\r\n  name: \"systemUser\",\r\n  components: {CheckboxGroups, DepartmentSelect},\r\n  data() {\r\n    const validateEn = (rule, value, callback) => {\r\n      let reg = /^[A-Za-z0-9_\\-]+$/gi;\r\n      if (value === \"\") {\r\n        callback(new Error(\"登录名不能为空\"));\r\n      } else if (value !== \"\" && !reg.test(value)) {\r\n        callback(new Error(\"只允许字母、数字、下划线,英文中划线\"));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n    const validatePass = (rule, value, callback) => {\r\n      let reg2 = /^.{6,18}$/;\r\n      if (value === \"\") {\r\n        callback(new Error(\"请输入密码\"));\r\n      } else if (value !== this.formItem.password) {\r\n        callback(new Error(\"两次输入密码不一致\"));\r\n      } else if (value !== \"\" && !reg2.test(value)) {\r\n        callback(new Error(\"长度6到18个字符\"));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n    const validatePassConfirm = (rule, value, callback) => {\r\n      if (value === \"\") {\r\n        callback(new Error(\"请再次输入密码\"));\r\n      } else if (value !== this.formItem.password) {\r\n        callback(new Error(\"两次输入密码不一致\"));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n    const validateMobile = (rule, value, callback) => {\r\n      let reg = /^1\\d{10}$/;\r\n      if (value !== \"\" && !reg.test(value)) {\r\n        callback(new Error(\"手机号码格式不正确\"));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n    return {\r\n      ActionType: 'none',\r\n      autoTableHeight,\r\n      userStatusOps: Common.userStatusOps,\r\n      yesNoOps: Common.yesNoOps,\r\n      checkBoxLoading:false,\r\n      loading: false,\r\n      saving: false,\r\n      modalVisible: false,\r\n      changeShow:true,\r\n      changeLoading:false,\r\n      modalTitle: \"\",\r\n      current: \"form1\",\r\n      forms: [\"form1\", \"form2\", \"form3\", \"form4\", \"form5\"],\r\n      selectMenus: [],\r\n      selectRoles: [],\r\n      selectShops: [],\r\n      companyList: [],\r\n      deptSearchList: [],\r\n      selectPlatforms: [],\r\n      checkAllGroup: [],\r\n      selectTreeData: [],\r\n      formSelectRoles: [],\r\n      pageInfo: {\r\n        page: 1,\r\n        pageSize: 10,\r\n        sort: \"createTime\",\r\n        order: \"desc\",\r\n        userName: '',\r\n        nickName: '',\r\n        roleIds: '',\r\n        departmentId: '',\r\n        status: -1\r\n      },\r\n      roleAll: [],\r\n      formItemRules: {\r\n        departmentId: [{required: true, message: \"部门不能为空\"}],\r\n        companyId: [\r\n          {required: true, message: \"公司不能为空\", trigger: \"blur\"}\r\n        ],\r\n        password: [\r\n          {required: true, validator: validatePass, trigger: \"blur\"}\r\n        ],\r\n        passwordConfirm: [\r\n          {required: true, validator: validatePassConfirm, trigger: \"blur\"}\r\n        ],\r\n        searchVal: [\r\n          {required: false, trigger: \"blur\"}\r\n        ],\r\n        nickName: [\r\n          {required: true, message: \"姓名不能为空\", trigger: \"blur\"}\r\n        ],\r\n        email: [{required: false, type: \"email\", message: \"邮箱格式不正确\", trigger: \"blur\"}],\r\n        mobile: [{validator: validateMobile, trigger: \"blur\"}]\r\n      },\r\n      formItem: {\r\n        userId: \"\",\r\n        userName: \"\",\r\n        nickName: \"\",\r\n        enName: \"\",\r\n        dingId: \"\",\r\n        password: \"\",\r\n        passwordConfirm: \"\",\r\n        status: 0,\r\n        intentAccess: 1,\r\n        companyId: \"\",\r\n        email: \"\",\r\n        mobile: \"\",\r\n        userDesc: \"\",\r\n        searchVal:\"\",\r\n        avatar: \"\",\r\n        grantRoles: [],\r\n        grantShops: [],\r\n        grantActions: [],\r\n        grantMenus: [],\r\n        departmentId: null\r\n      },\r\n      columns: [\r\n        {\r\n          title: \"登录名\",\r\n          key: \"userName\",\r\n          align: \"center\",\r\n          minWidth: 120,\r\n          render: (h, {row}) => (\r\n            <span v-copytext={row.userName}>{row.userName}</span>\r\n          )\r\n        },\r\n        {\r\n          title: \"姓名\",\r\n          key: \"nickName\",\r\n          align: \"center\",\r\n          minWidth: 120,\r\n          render: (h, {row}) => (\r\n            <span v-copytext={row.nickName}>{row.nickName}</span>\r\n          )\r\n        },\r\n        {\r\n          title: '所属公司',\r\n          key: 'companyName',\r\n          width: 200\r\n        },\r\n        {\r\n          title: \"所属部门\",\r\n          key: \"departmentName\",\r\n          minWidth: 200\r\n        },\r\n        {\r\n          title: \"角色\",\r\n          key: \"roleNames\",\r\n          align: \"center\",\r\n          minWidth: 150,\r\n          sortable: true\r\n        },\r\n        {\r\n          title: \"状态\",\r\n          slot: \"status\",\r\n          key: \"status\",\r\n          align: \"center\",\r\n          width: 100\r\n        },\r\n        {\r\n          title: \"外网访问\",\r\n          key: \"intentAccess\",\r\n          align: \"center\",\r\n          slot: \"intentAccess\",\r\n          width: 100\r\n        },\r\n        {\r\n          title: \"注册时间\",\r\n          key: \"createTime\",\r\n          align: \"center\",\r\n          width: 145\r\n        },\r\n        {\r\n          title: '描述',\r\n          key: 'userDesc',\r\n          width: 180,\r\n        },\r\n        {\r\n          title: \"全部店铺\",\r\n          key: \"isAllShop\",\r\n          width: 80,\r\n          slot: \"isAllShop\",\r\n          align: \"center\"\r\n        },\r\n        {\r\n          title: \"操作\",\r\n          slot: \"action\",\r\n          fixed: \"right\",\r\n          align: \"center\",\r\n          width: 120\r\n        }\r\n      ],\r\n      treeColumns: [\r\n        {\r\n          title: \"菜单\",\r\n          key: \"menuName\",\r\n          minWidth: \"250px\"\r\n        },\r\n        {\r\n          title: \"操作\",\r\n          type: \"template\",\r\n          template: \"operation\",\r\n          minWidth: \"200px\"\r\n        }\r\n      ],\r\n      data: [],\r\n      flag: false,\r\n      bool1: false,\r\n      bool2: false,\r\n      productCategoryOps: [],\r\n      checkedIds: [] //勾选的类目、系列id\r\n    };\r\n  },\r\n  mounted: function () {\r\n    this.handleSearch();\r\n    this.handleCompany();\r\n    this.getRolesAll();\r\n  },\r\n  methods: {\r\n    changeRoleCheck(v) {\r\n      if (v) {\r\n        this.selectRoles = this.CommselectRoles.filter(item => this.formItem.grantRoles.includes(item.roleId))\r\n      } else {\r\n        this.selectRoles = this.CommselectRoles;\r\n      }\r\n    },\r\n    queryMenuName() {\r\n      const commData = JSON.parse(JSON.stringify(this.CommMenus))\r\n      let mapTree = (value, arr) => {\r\n        let newArr = [];\r\n        arr.forEach(element => {\r\n          if (element.menuName.indexOf(value) > -1) { // 判断条件\r\n            newArr.push(element);\r\n          } else {\r\n            if (element.children && element.children.length > 0) {\r\n              let reData = mapTree(value, element.children);\r\n              if (reData && reData.length > 0) {\r\n                let obj = {...element, children: reData};\r\n                newArr.push(obj);\r\n              }\r\n            }\r\n          }\r\n        });\r\n        return newArr;\r\n      };\r\n      if (isEmpty(this.formItem.menuName)) {\r\n        this.selectMenus = this.CommMenus;\r\n      } else {\r\n        this.selectMenus = mapTree(this.formItem.menuName, commData)\r\n      }\r\n    },\r\n    queryShopName(){\r\n      this.handleLoadShopGranted(this.formItem.id,this.formItem.searchVal);\r\n    },\r\n    checkAllShop(value, row = {}) {\r\n      if (!row.id) return;\r\n      this.$Modal.confirm({\r\n        title:row.isAllShop === 1? `确定要取消赋予 ${row.nickName} 全部店铺权限吗？` : `确定要给 ${row.nickName} 赋予全部店铺的权限吗？`,\r\n        content: row.isAllShop === 1 ? \"注：此操作会保留该用户现有的店铺权限，但是后续新增店铺时不会自动授予该用户所有店铺权限！\" : \"\",\r\n        okText: \"确定\",\r\n        cancelText: \"取消\",\r\n        onOk: () => {\r\n          this.changeLoading = true;\r\n          User.addAllShops({\"id\":row.id,\"isCheck\":value?1:0}).then(res => {\r\n            if (res && res['code'] === 0 && res['message'] === \"success\") {\r\n              this.$Message.success(\"授权成功！\");\r\n            }\r\n            this.handleSearch();\r\n          }).finally(() => {\r\n            this.changeLoading = false;\r\n          });\r\n        },\r\n        onCancel: () => {\r\n          this.changeShow = false;\r\n          setTimeout(() => {\r\n            this.changeShow = true;\r\n          }, 100);\r\n        }\r\n      });\r\n    },\r\n    personSyn() {\r\n      this.flag = true;\r\n      User.syncWechatUser().then(res => {\r\n          if (res['code'] === 0) {\r\n            this.$Message.success(\"同步成功\");\r\n            this.handleSearch();\r\n          }\r\n        }).finally(() => {\r\n        this.flag = false;\r\n      });\r\n    },\r\n    handleModal(actionType, data) {\r\n      this.ActionType = actionType;\r\n      if(this.ActionType === 'add'){\r\n        this.formItem.userName='';\r\n        this.formItem.password= '';\r\n      }\r\n      this.row = data;\r\n      this.bool1 = false;\r\n      this.bool2 = false;\r\n      if (data) {\r\n        this.getDeptList(data.companyId);\r\n        this.formItem = Object.assign({}, this.formItem, data);\r\n      }\r\n      if (this.current === this.forms[0]) {\r\n        this.modalTitle = data ? (this.ActionType === 'edit' ? \"编辑用户 - \" : '查看用户 - ') + data.userName : \"添加用户\";\r\n        this.modalVisible = true;\r\n      }\r\n      if (this.current === this.forms[1]) {\r\n        this.modalTitle = data ? (this.ActionType === 'edit' ? \"编辑分配角色 - \" : \"查看分配角色 -\") + data.userName : \"分配角色\";\r\n        this.handleLoadRoles(this.formItem.id);\r\n      }\r\n      if (this.current === this.forms[2]) {\r\n        this.modalTitle = data ? (this.ActionType === 'edit' ? \"编辑分配权限 - \" : \"查看分配权限 -\") + data.userName : \"分配权限\";\r\n        this.handleLoadUserGranted(this.formItem.id);\r\n      }\r\n      if (this.current === this.forms[3]) {\r\n        this.modalTitle = data ? (this.ActionType === 'edit' ? \"编辑网店权限 - \" : \"查看网店权限 -\") + data.userName : \"网店权限\";\r\n        this.handleLoadShopGranted(this.formItem.id);\r\n      }\r\n      if (this.current === this.forms[4]) {\r\n        this.modalTitle = data ? (this.ActionType === 'edit' ? \"修改密码 - \" : \"查看密码 -\") + data.userName : \"修改密码\";\r\n        this.modalVisible = true;\r\n      }\r\n    },\r\n    //角色排序\r\n    roleSort(obj) {\r\n      this.pageInfo.order = obj.order;\r\n      this.pageInfo.page = 1;\r\n      this.pageInfo.pageSize = 10;\r\n      this.handleSearch();\r\n    },\r\n    getRuleIds() {\r\n      this.pageInfo.roleIds = this.formSelectRoles.join(\",\");\r\n    },\r\n    handleResetForm(form) {\r\n      if (this.$refs[form] !== undefined) {\r\n        this.$refs[form].resetFields();\r\n        if (form === \"searchForm\") {\r\n          this.formSelectRoles = [];\r\n          this.pageInfo.departmentId = undefined;\r\n        }\r\n      }\r\n    },\r\n    handleReset() {\r\n      this.formItem = {\r\n        userId: \"\",\r\n        userName: \"\",\r\n        nickName: \"\",\r\n        enName: \"\",\r\n        password: \"\",\r\n        passwordConfirm: \"\",\r\n        status: 0,\r\n        intentAccess: 1,\r\n        companyId: \"\",\r\n        departmentId: null,\r\n        email: \"\",\r\n        mobile: \"\",\r\n        userDesc: \"\",\r\n        avatar: \"\",\r\n        grantRoles: [],\r\n        grantShops: [],\r\n        grantMenus: [],\r\n        grantActions: []\r\n      };\r\n      //重置验证\r\n      this.forms.map(form => {\r\n        this.handleResetForm(form);\r\n      });\r\n      this.current = this.forms[0];\r\n      this.formItem.grantMenus = [];\r\n      this.formItem.grantActions = [];\r\n      this.selectTreeData = [];\r\n      this.modalVisible = false;\r\n      this.saving = false;\r\n      this.checkedIds = [];\r\n    },\r\n    handleSubmit() {\r\n      if (this.current === this.forms[0]) {\r\n        this.$refs[this.current].validate(valid => {\r\n          if (valid) {\r\n            this.saving = true;\r\n            if (this.formItem.id) {\r\n              User.edit(this.formItem).then(res => {\r\n                  if (res['code'] === 0) {\r\n                    this.$Message.success(\"保存成功\");\r\n                    this.modalVisible = false;\r\n                    this.handleReset();\r\n                  }\r\n                  this.handleSearch();\r\n                })\r\n                .finally(() => {\r\n                  this.saving = false;\r\n                });\r\n            } else {\r\n              User.add(this.formItem).then(res => {\r\n                  if (res['code'] === 0) {\r\n                    this.$Message.success(\"保存成功\");\r\n                    this.handleReset();\r\n                  }\r\n                  this.handleSearch();\r\n                })\r\n                .finally(() => {\r\n                  this.saving = false;\r\n                });\r\n            }\r\n          }\r\n        });\r\n      }\r\n      if (this.current === this.forms[1] && this.formItem.id) {\r\n        this.$refs[this.current].validate(valid => {\r\n          if (valid) {\r\n            this.saving = true;\r\n            User.saveUserRoles(this.formItem)\r\n              .then(res => {\r\n                if (res['code'] === 0) {\r\n                  this.$Message.success(\"分配角色成功\");\r\n                  this.modalVisible = false;\r\n                  this.handleReset();\r\n                }\r\n                this.handleSearch();\r\n              })\r\n              .finally(() => {\r\n                this.saving = false;\r\n              });\r\n          }\r\n        });\r\n      }\r\n      if (this.current === this.forms[2] && this.formItem.id) {\r\n        this.$refs[this.current].validate(valid => {\r\n          if (valid) {\r\n            const authorityIds = this.getCheckedAuthorities();\r\n            //解决部分勾选问题\r\n            const getParentIds = (arr = []) => {\r\n              for (const xItem of arr) {\r\n                const childrenIds = xItem.children ? xItem.children.map(v => v.authorityId) : [];\r\n                let flag = false;\r\n                for (const yItem of childrenIds) {\r\n                  if (authorityIds.includes(yItem)) {\r\n                    flag = true;\r\n                    break;\r\n                  }\r\n                }\r\n                if (flag === true && !authorityIds.includes(xItem.authorityId)) {\r\n                  authorityIds.push(xItem.authorityId);\r\n                }\r\n                if (xItem.children) getParentIds(xItem.children);\r\n              }\r\n            };\r\n            getParentIds(this.selectMenus);\r\n            for (const xItem of this.selectMenus) {\r\n              const childrenIds = xItem.children ? xItem.children.map(v => v.authorityId) : [];\r\n              let flag = false;\r\n              for (const yItem of childrenIds) {\r\n                if (authorityIds.includes(yItem)) {\r\n                  flag = true;\r\n                  break;\r\n                }\r\n              }\r\n              if (flag === true && !authorityIds.includes(xItem.authorityId)) {\r\n                authorityIds.push(xItem.authorityId);\r\n              }\r\n            }\r\n            this.saving = true;\r\n            Authority.grantAuthorityForUser({\r\n              userId: this.formItem.id,\r\n              authorityIds: authorityIds\r\n            }).then(res => {\r\n              if (res['code'] === 0) {\r\n                this.$Message.success(\"授权成功\");\r\n                this.modalVisible = false;\r\n                this.handleReset();\r\n              }\r\n              this.handleSearch();\r\n            }).finally(() => {\r\n              this.saving = false;\r\n            });\r\n          }\r\n        });\r\n      }\r\n      if (this.current === this.forms[3] && this.formItem.id) {\r\n        this.$refs[this.current].validate(valid => {\r\n          if (!valid) {\r\n            return;\r\n          }\r\n          this.saving = true;\r\n          let shopIds = \"\";\r\n          this.checkAllGroup.map(function(item) {\r\n            if (item) shopIds += item + \",\";\r\n          });\r\n          let allShopIds = \"\";\r\n          this.selectShops.map(item=> item['shopList'].map(each=>allShopIds += each.id+\",\"))\r\n          User.saveUserShops({id: this.formItem.id,\"shopIds\":shopIds,\"allShopIds\":allShopIds}).then(res=>{\r\n            if (res['code'] === 0) {\r\n              this.$Message.success(\"分配店铺权限成功\");\r\n              this.formItem.searchVal = \"\";\r\n            }\r\n            this.handleSearch();\r\n          }).finally(() => {\r\n            this.saving = false;\r\n            this.modalVisible = false;\r\n          })\r\n        });\r\n      }\r\n      if (this.current === this.forms[4] && this.formItem.id) {\r\n        this.$refs[this.current].validate(valid => {\r\n          if (valid) {\r\n            this.saving = true;\r\n            User.updatePassword({id: this.formItem.id, password: this.formItem.password})\r\n              .then(res => {\r\n                if (res['code'] === 0) {\r\n                  this.$Message.success(\"修改成功\");\r\n                  this.modalVisible = false;\r\n                  this.handleReset();\r\n                }\r\n                this.handleSearch();\r\n              })\r\n              .finally(() => {\r\n                this.saving = false;\r\n                this.modalVisible = false;\r\n              });\r\n          }\r\n        });\r\n      }\r\n    },\r\n    handleSearch(page) {\r\n      if (page) {\r\n        this.pageInfo.page = page;\r\n      }\r\n      this.loading = true;\r\n      const params = {\r\n        ...this.pageInfo,\r\n        departmentIds: this.pageInfo.departmentId ? this.pageInfo.departmentId.toString() : undefined\r\n      };\r\n      delete params.departmentId;\r\n      User.listPage(params).then(res => {\r\n        this.data = res.data.records;\r\n        this.pageInfo.total = parseInt(res.data.total);\r\n      }).finally(() => {\r\n        this.loading = false;\r\n      });\r\n    },\r\n    getCheckedAuthorities() {\r\n      const menus = this.$refs[\"tree\"].getCheckedProp(\"authorityId\");\r\n      return menus.concat(this.formItem.grantActions);\r\n    },\r\n    handleLoadShopGranted(id,shopName){\r\n      const that = this;\r\n      that.checkBoxLoading = true;\r\n      let res1 = User.getUserShops({id:id});\r\n      let res2 = User.getUserShops({name:shopName});\r\n      Promise.all([res1,res2]).then((result)=>{\r\n        const res1 = result[0] || {};\r\n        const res2 = result[1] || {};\r\n        let allShop = res2.data||[];\r\n        let shopObj= {};\r\n        allShop.map(item=>{\r\n          let nameKey = item['nameKey'];\r\n          let list = shopObj[nameKey]||[];\r\n          list.push(item);\r\n          shopObj[nameKey] = list;\r\n        })\r\n        let selectShops = [];\r\n        for (let nameKey in shopObj) {\r\n          selectShops.push({\"nameKey\":nameKey,\"shopList\":shopObj[nameKey]});\r\n        }\r\n        selectShops.sort((o1,o2)=> o1['nameKey'].localeCompare(o2['nameKey']));\r\n        that.selectShops = selectShops;\r\n        let checkArray = [];\r\n        let userShop = (res1.data||[]).map(item=>item[\"id\"]);\r\n        for (let i = 0; i < selectShops.length; i++) {\r\n          let data = new Set();\r\n          let shopList = selectShops[i][\"shopList\"];\r\n          for(let j = 0; j < shopList.length; j++){\r\n            if(userShop.indexOf(shopList[j][\"id\"])>=0){\r\n              data.add(shopList[j][\"id\"]);\r\n            }\r\n          }\r\n          checkArray[i] = Array.from(data);\r\n        }\r\n        that.checkAllGroup = checkArray;\r\n        that.checkBoxLoading = false;\r\n        that.modalVisible = true;\r\n      })\r\n    },\r\n    changeCheckBox(res) {\r\n      //多选按钮改变\r\n      this.checkAllGroup[res.index] = res.data;\r\n      //this.setCheckData();\r\n      this.checkAllGroup = JSON.parse(\r\n          JSON.stringify(this.checkAllGroup)\r\n      );\r\n    },\r\n    setCheckData() {\r\n      //更新多选数据\r\n      let checkData = new Set(),\r\n          data = [-1, 0, 1, 2, 3, 4];\r\n      let check = this.checkAllGroupArray.slice();\r\n      check.map(item => {\r\n        item.slice();\r\n        let array = item.filter(item => {\r\n          return data.indexOf(item) < 0;\r\n        });\r\n        array.map(item => {\r\n          checkData.add(item);\r\n        });\r\n      });\r\n      this.checkAllGroup = Array.from(checkData);\r\n    },\r\n    handleLoadUserGranted(userId) {\r\n      const that = this;\r\n      const p1 = Authority.getAuthorityMenu(\"\");\r\n      const p2 = Authority.getAuthorityForUser(userId);\r\n      const roleAuthorites = [];\r\n      Promise.all([p1, p2]).then(function (values) {\r\n        let res1 = values[0];\r\n        let res2 = values[1];\r\n        if (res1.code === 0 && res1.data) {\r\n          let opt = {primaryKey: \"id\", parentKey: \"parentId\", startPid: \"0\"};\r\n          if (res2.code === 0 && res2.data && res2.data.length > 0) {\r\n            let menus = [];\r\n            let actions = [];\r\n            res2.data.map(item => {\r\n              if (item.owner === \"role\") {\r\n                roleAuthorites.push(item.authorityId);\r\n              }\r\n              // 菜单权限\r\n              if (item.authority.indexOf(\"MENU_\") !== -1 && !menus.includes(item.authorityId)) {\r\n                menus.push(item.authorityId);\r\n              }\r\n              // 操作权限\r\n              if (item.authority.indexOf(\"ACTION_\") !== -1 && !actions.includes(item.authorityId)) {\r\n                actions.push(item.authorityId);\r\n              }\r\n            });\r\n            that.formItem.grantMenus = menus;\r\n            that.formItem.grantActions = actions;\r\n          }\r\n          res1.data.map(item => {\r\n            // 菜单选中\r\n            if (that.formItem.grantMenus.includes(item.authorityId)) {\r\n              item._isChecked = true;\r\n              // 归属角色权限,禁止授权\r\n              if (roleAuthorites.includes(item.authorityId)) {\r\n                // 插件不支持,禁用\r\n                item.disabled = true;\r\n                item.menuName += \" (禁止勾选)\";\r\n              }\r\n            }\r\n            // 功能权限\r\n            item.actionList.map(action => {\r\n              if (roleAuthorites.includes(action.authorityId)) {\r\n                action.disabled = true;\r\n              }\r\n            });\r\n          });\r\n          that.selectMenus = listConvertTree(res1.data, opt);\r\n          that.CommMenus = JSON.parse(JSON.stringify(that.selectMenus))\r\n        }\r\n        that.modalVisible = true;\r\n      });\r\n    },\r\n    handleLoadRoles(userId) {\r\n      if (!userId) {\r\n        return;\r\n      }\r\n      const that = this;\r\n      const p1 = Role.getAll();\r\n      const p2 = User.getUserRoles(userId);\r\n      Promise.all([p1, p2]).then(function (values) {\r\n        let res1 = values[0];\r\n        let res2 = values[1];\r\n        if (res1.code === 0) {\r\n          let result1 = [];\r\n          res1.data.map(item => {\r\n            if (item.roleId !== 1) {\r\n              result1.push(item);\r\n            }\r\n          });\r\n          that.selectRoles = result1;\r\n          that.CommselectRoles = result1;\r\n        }\r\n        if (res2.code === 0) {\r\n          let result = [];\r\n          res2.data.map(item => {\r\n            result.push(item.id);\r\n          });\r\n          that.formItem.grantRoles = result;\r\n        }\r\n        that.modalVisible = true;\r\n      });\r\n    },\r\n    handlePage(current) {\r\n      this.pageInfo.page = current;\r\n      this.handleSearch();\r\n    },\r\n    handlePageSize(size) {\r\n      this.pageInfo.limit = size;\r\n      this.handleSearch();\r\n    },\r\n    handleCompany() {\r\n      CommonApi.getAllCompany().then(res => {\r\n        this.companyList = res.data;\r\n      });\r\n    },\r\n    getDeptList(val) {\r\n      if (val != null) {\r\n        this.formItem.departmentId = null;\r\n        getByCompanyId(val).then(res => {\r\n          this.deptSearchList = res.data;\r\n          let opt = {\r\n            primaryKey: \"id\",\r\n            parentKey: \"parentId\",\r\n            startPid: \"0\"\r\n          };\r\n          this.selectTreeData = listConvertTree(res.data, opt);\r\n        });\r\n      }\r\n    },\r\n    getRolesAll() {\r\n      CommonApi.getAllRole().then(res => {\r\n        if (res['code'] === 0) {\r\n          res.data.map(item => {\r\n            if (item.roleId !== \"1\") this.roleAll.push(item);\r\n          });\r\n        }\r\n      });\r\n    },\r\n    handleTabClick(name) {\r\n      this.current = name;\r\n      this.handleModal(this.actionType, this.row);\r\n    },\r\n    setTreeData(arr = [], reset = false) {\r\n      const {checkedIds} = this;\r\n      arr.forEach(item => {\r\n        if (reset === true) {\r\n          item.indeterminate = false;\r\n          item.checked = false;\r\n        } else {\r\n          item.title = item.description;\r\n          item.expand = item.level < 2;\r\n          item.checked = checkedIds.includes(item.id);\r\n        }\r\n        if (item.children && item.children.length > 0) {\r\n          this.setTreeData(item.children, reset);\r\n        }\r\n      });\r\n    },\r\n    onVisibleChange(visible) {\r\n      if (visible === false) {\r\n        this.setTreeData(this.productCategoryOps, true);\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\">\r\n.userManage {\r\n  .userManageForm {\r\n    position: relative;\r\n    padding-right: 135px;\r\n  }\r\n\r\n  .ivu-table-wrapper {\r\n    .ivu-table-cell {\r\n      padding-left: 4px;\r\n      padding-right: 4px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AA2LA,OAAAA,IAAA;AACA,SAAAC,eAAA;AACA,OAAAC,SAAA;AACA,OAAAC,gBAAA;AACA,SAAAC,eAAA,EAAAC,OAAA;AACA,OAAAC,MAAA;AACA,OAAAC,SAAA;AACA,OAAAC,IAAA;AACA,OAAAC,IAAA;AACA,SAAAC,cAAA;AACA,OAAAC,cAAA;AACA;EACAC,IAAA;EACAC,UAAA;IAAAF,cAAA,EAAAA,cAAA;IAAAR,gBAAA,EAAAA;EAAA;EACAW,IAAA,WAAAA,KAAA;IAAA,IAAAC,KAAA;IAAA,IAAAC,CAAA,QAAAC,cAAA;IACA,IAAAC,UAAA,YAAAA,WAAAC,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,IAAAC,GAAA;MACA,IAAAF,KAAA;QACAC,QAAA,KAAAE,KAAA;MACA,WAAAH,KAAA,YAAAE,GAAA,CAAAE,IAAA,CAAAJ,KAAA;QACAC,QAAA,KAAAE,KAAA;MACA;QACAF,QAAA;MACA;IACA;IACA,IAAAI,YAAA,YAAAA,aAAAN,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,IAAAK,IAAA;MACA,IAAAN,KAAA;QACAC,QAAA,KAAAE,KAAA;MACA,WAAAH,KAAA,KAAAL,KAAA,CAAAY,QAAA,CAAAC,QAAA;QACAP,QAAA,KAAAE,KAAA;MACA,WAAAH,KAAA,YAAAM,IAAA,CAAAF,IAAA,CAAAJ,KAAA;QACAC,QAAA,KAAAE,KAAA;MACA;QACAF,QAAA;MACA;IACA;IACA,IAAAQ,mBAAA,YAAAA,oBAAAV,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,IAAAD,KAAA;QACAC,QAAA,KAAAE,KAAA;MACA,WAAAH,KAAA,KAAAL,KAAA,CAAAY,QAAA,CAAAC,QAAA;QACAP,QAAA,KAAAE,KAAA;MACA;QACAF,QAAA;MACA;IACA;IACA,IAAAS,cAAA,YAAAA,eAAAX,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,IAAAC,GAAA;MACA,IAAAF,KAAA,YAAAE,GAAA,CAAAE,IAAA,CAAAJ,KAAA;QACAC,QAAA,KAAAE,KAAA;MACA;QACAF,QAAA;MACA;IACA;IACA;MACAU,UAAA;MACA3B,eAAA,EAAAA,eAAA;MACA4B,aAAA,EAAA1B,MAAA,CAAA0B,aAAA;MACAC,QAAA,EAAA3B,MAAA,CAAA2B,QAAA;MACAC,eAAA;MACAC,OAAA;MACAC,MAAA;MACAC,YAAA;MACAC,UAAA;MACAC,aAAA;MACAC,UAAA;MACAC,OAAA;MACAC,KAAA;MACAC,WAAA;MACAC,WAAA;MACAC,WAAA;MACAC,WAAA;MACAC,cAAA;MACAC,eAAA;MACAC,aAAA;MACAC,cAAA;MACAC,eAAA;MACAC,QAAA;QACAC,IAAA;QACAC,QAAA;QACAC,IAAA;QACAC,KAAA;QACAC,QAAA;QACAC,QAAA;QACAC,OAAA;QACAC,YAAA;QACAC,MAAA;MACA;MACAC,OAAA;MACAC,aAAA;QACAH,YAAA;UAAAI,QAAA;UAAAC,OAAA;QAAA;QACAC,SAAA,GACA;UAAAF,QAAA;UAAAC,OAAA;UAAAE,OAAA;QAAA,EACA;QACAvC,QAAA,GACA;UAAAoC,QAAA;UAAAI,SAAA,EAAA3C,YAAA;UAAA0C,OAAA;QAAA,EACA;QACAE,eAAA,GACA;UAAAL,QAAA;UAAAI,SAAA,EAAAvC,mBAAA;UAAAsC,OAAA;QAAA,EACA;QACAG,SAAA,GACA;UAAAN,QAAA;UAAAG,OAAA;QAAA,EACA;QACAT,QAAA,GACA;UAAAM,QAAA;UAAAC,OAAA;UAAAE,OAAA;QAAA,EACA;QACAI,KAAA;UAAAP,QAAA;UAAAQ,IAAA;UAAAP,OAAA;UAAAE,OAAA;QAAA;QACAM,MAAA;UAAAL,SAAA,EAAAtC,cAAA;UAAAqC,OAAA;QAAA;MACA;MACAxC,QAAA;QACA+C,MAAA;QACAjB,QAAA;QACAC,QAAA;QACAiB,MAAA;QACAC,MAAA;QACAhD,QAAA;QACAyC,eAAA;QACAR,MAAA;QACAgB,YAAA;QACAX,SAAA;QACAK,KAAA;QACAE,MAAA;QACAK,QAAA;QACAR,SAAA;QACAS,MAAA;QACAC,UAAA;QACAC,UAAA;QACAC,YAAA;QACAC,UAAA;QACAvB,YAAA;MACA;MACAwB,OAAA,GACA;QACAC,KAAA;QACAC,GAAA;QACAC,KAAA;QACAC,QAAA;QACAC,MAAA,WAAAA,OAAAzE,CAAA,EAAA0E,IAAA;UAAA,IAAAC,GAAA,GAAAD,IAAA,CAAAC,GAAA;UAAA,OAAA3E,CAAA;YAAA;cAAAJ,IAAA;cAAAQ,KAAA,EACAuE,GAAA,CAAAlC;YAAA;UAAA,IAAAkC,GAAA,CAAAlC,QAAA;QAAA;MAEA,GACA;QACA4B,KAAA;QACAC,GAAA;QACAC,KAAA;QACAC,QAAA;QACAC,MAAA,WAAAA,OAAAzE,CAAA,EAAA4E,KAAA;UAAA,IAAAD,GAAA,GAAAC,KAAA,CAAAD,GAAA;UAAA,OAAA3E,CAAA;YAAA;cAAAJ,IAAA;cAAAQ,KAAA,EACAuE,GAAA,CAAAjC;YAAA;UAAA,IAAAiC,GAAA,CAAAjC,QAAA;QAAA;MAEA,GACA;QACA2B,KAAA;QACAC,GAAA;QACAO,KAAA;MACA,GACA;QACAR,KAAA;QACAC,GAAA;QACAE,QAAA;MACA,GACA;QACAH,KAAA;QACAC,GAAA;QACAC,KAAA;QACAC,QAAA;QACAM,QAAA;MACA,GACA;QACAT,KAAA;QACAU,IAAA;QACAT,GAAA;QACAC,KAAA;QACAM,KAAA;MACA,GACA;QACAR,KAAA;QACAC,GAAA;QACAC,KAAA;QACAQ,IAAA;QACAF,KAAA;MACA,GACA;QACAR,KAAA;QACAC,GAAA;QACAC,KAAA;QACAM,KAAA;MACA,GACA;QACAR,KAAA;QACAC,GAAA;QACAO,KAAA;MACA,GACA;QACAR,KAAA;QACAC,GAAA;QACAO,KAAA;QACAE,IAAA;QACAR,KAAA;MACA,GACA;QACAF,KAAA;QACAU,IAAA;QACAC,KAAA;QACAT,KAAA;QACAM,KAAA;MACA,EACA;MACAI,WAAA,GACA;QACAZ,KAAA;QACAC,GAAA;QACAE,QAAA;MACA,GACA;QACAH,KAAA;QACAb,IAAA;QACA0B,QAAA;QACAV,QAAA;MACA,EACA;MACA1E,IAAA;MACAqF,IAAA;MACAC,KAAA;MACAC,KAAA;MACAC,kBAAA;MACAC,UAAA;IACA;EACA;;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,YAAA;IACA,KAAAC,aAAA;IACA,KAAAC,WAAA;EACA;EACAC,OAAA;IACAC,eAAA,WAAAA,gBAAAC,CAAA;MAAA,IAAAC,MAAA;MACA,IAAAD,CAAA;QACA,KAAAlE,WAAA,QAAAoE,eAAA,CAAAC,MAAA,WAAAC,IAAA;UAAA,OAAAH,MAAA,CAAApF,QAAA,CAAAqD,UAAA,CAAAmC,QAAA,CAAAD,IAAA,CAAAE,MAAA;QAAA;MACA;QACA,KAAAxE,WAAA,QAAAoE,eAAA;MACA;IACA;IACAK,aAAA,WAAAA,cAAA;MACA,IAAAC,QAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,MAAAC,SAAA;MACA,IAAAC,OAAA,YAAAA,QAAAvG,KAAA,EAAAwG,GAAA;QACA,IAAAC,MAAA;QACAD,GAAA,CAAAE,OAAA,WAAAC,OAAA;UACA,IAAAA,OAAA,CAAAC,QAAA,CAAAC,OAAA,CAAA7G,KAAA;YAAA;YACAyG,MAAA,CAAAK,IAAA,CAAAH,OAAA;UACA;YACA,IAAAA,OAAA,CAAAI,QAAA,IAAAJ,OAAA,CAAAI,QAAA,CAAAC,MAAA;cACA,IAAAC,MAAA,GAAAV,OAAA,CAAAvG,KAAA,EAAA2G,OAAA,CAAAI,QAAA;cACA,IAAAE,MAAA,IAAAA,MAAA,CAAAD,MAAA;gBACA,IAAAE,GAAA,GAAAC,aAAA,CAAAA,aAAA,KAAAR,OAAA;kBAAAI,QAAA,EAAAE;gBAAA;gBACAR,MAAA,CAAAK,IAAA,CAAAI,GAAA;cACA;YACA;UACA;QACA;QACA,OAAAT,MAAA;MACA;MACA,IAAAxH,OAAA,MAAAsB,QAAA,CAAAqG,QAAA;QACA,KAAArF,WAAA,QAAA+E,SAAA;MACA;QACA,KAAA/E,WAAA,GAAAgF,OAAA,MAAAhG,QAAA,CAAAqG,QAAA,EAAAV,QAAA;MACA;IACA;IACAkB,aAAA,WAAAA,cAAA;MACA,KAAAC,qBAAA,MAAA9G,QAAA,CAAA+G,EAAA,OAAA/G,QAAA,CAAA2C,SAAA;IACA;IACAqE,YAAA,WAAAA,aAAAvH,KAAA;MAAA,IAAAwH,MAAA;MAAA,IAAAjD,GAAA,GAAAkD,SAAA,CAAAT,MAAA,QAAAS,SAAA,QAAAC,SAAA,GAAAD,SAAA;MACA,KAAAlD,GAAA,CAAA+C,EAAA;MACA,KAAAK,MAAA,CAAAC,OAAA;QACA3D,KAAA,EAAAM,GAAA,CAAAsD,SAAA,uDAAAC,MAAA,CAAAvD,GAAA,CAAAjC,QAAA,qFAAAwF,MAAA,CAAAvD,GAAA,CAAAjC,QAAA;QACAyF,OAAA,EAAAxD,GAAA,CAAAsD,SAAA;QACAG,MAAA;QACAC,UAAA;QACAC,IAAA,WAAAA,KAAA;UACAV,MAAA,CAAArG,aAAA;UACA9B,IAAA,CAAA8I,WAAA;YAAA,MAAA5D,GAAA,CAAA+C,EAAA;YAAA,WAAAtH,KAAA;UAAA,GAAAoI,IAAA,WAAAC,GAAA;YACA,IAAAA,GAAA,IAAAA,GAAA,kBAAAA,GAAA;cACAb,MAAA,CAAAc,QAAA,CAAAC,OAAA;YACA;YACAf,MAAA,CAAAnC,YAAA;UACA,GAAAmD,OAAA;YACAhB,MAAA,CAAArG,aAAA;UACA;QACA;QACAsH,QAAA,WAAAA,SAAA;UACAjB,MAAA,CAAAtG,UAAA;UACAwH,UAAA;YACAlB,MAAA,CAAAtG,UAAA;UACA;QACA;MACA;IACA;IACAyH,SAAA,WAAAA,UAAA;MAAA,IAAAC,MAAA;MACA,KAAA7D,IAAA;MACA1F,IAAA,CAAAwJ,cAAA,GAAAT,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA;UACAO,MAAA,CAAAN,QAAA,CAAAC,OAAA;UACAK,MAAA,CAAAvD,YAAA;QACA;MACA,GAAAmD,OAAA;QACAI,MAAA,CAAA7D,IAAA;MACA;IACA;IACA+D,WAAA,WAAAA,YAAAC,UAAA,EAAArJ,IAAA;MACA,KAAAiB,UAAA,GAAAoI,UAAA;MACA,SAAApI,UAAA;QACA,KAAAJ,QAAA,CAAA8B,QAAA;QACA,KAAA9B,QAAA,CAAAC,QAAA;MACA;MACA,KAAA+D,GAAA,GAAA7E,IAAA;MACA,KAAAsF,KAAA;MACA,KAAAC,KAAA;MACA,IAAAvF,IAAA;QACA,KAAAsJ,WAAA,CAAAtJ,IAAA,CAAAoD,SAAA;QACA,KAAAvC,QAAA,GAAA0I,MAAA,CAAAC,MAAA,UAAA3I,QAAA,EAAAb,IAAA;MACA;MACA,SAAA2B,OAAA,UAAAC,KAAA;QACA,KAAAF,UAAA,GAAA1B,IAAA,SAAAiB,UAAA,uCAAAjB,IAAA,CAAA2C,QAAA;QACA,KAAApB,YAAA;MACA;MACA,SAAAI,OAAA,UAAAC,KAAA;QACA,KAAAF,UAAA,GAAA1B,IAAA,SAAAiB,UAAA,0CAAAjB,IAAA,CAAA2C,QAAA;QACA,KAAA8G,eAAA,MAAA5I,QAAA,CAAA+G,EAAA;MACA;MACA,SAAAjG,OAAA,UAAAC,KAAA;QACA,KAAAF,UAAA,GAAA1B,IAAA,SAAAiB,UAAA,0CAAAjB,IAAA,CAAA2C,QAAA;QACA,KAAA+G,qBAAA,MAAA7I,QAAA,CAAA+G,EAAA;MACA;MACA,SAAAjG,OAAA,UAAAC,KAAA;QACA,KAAAF,UAAA,GAAA1B,IAAA,SAAAiB,UAAA,0CAAAjB,IAAA,CAAA2C,QAAA;QACA,KAAAgF,qBAAA,MAAA9G,QAAA,CAAA+G,EAAA;MACA;MACA,SAAAjG,OAAA,UAAAC,KAAA;QACA,KAAAF,UAAA,GAAA1B,IAAA,SAAAiB,UAAA,sCAAAjB,IAAA,CAAA2C,QAAA;QACA,KAAApB,YAAA;MACA;IACA;IACA;IACAoI,QAAA,WAAAA,SAAAnC,GAAA;MACA,KAAAlF,QAAA,CAAAI,KAAA,GAAA8E,GAAA,CAAA9E,KAAA;MACA,KAAAJ,QAAA,CAAAC,IAAA;MACA,KAAAD,QAAA,CAAAE,QAAA;MACA,KAAAmD,YAAA;IACA;IACAiE,UAAA,WAAAA,WAAA;MACA,KAAAtH,QAAA,CAAAO,OAAA,QAAAR,eAAA,CAAAwH,IAAA;IACA;IACAC,eAAA,WAAAA,gBAAAC,IAAA;MACA,SAAAC,KAAA,CAAAD,IAAA,MAAA/B,SAAA;QACA,KAAAgC,KAAA,CAAAD,IAAA,EAAAE,WAAA;QACA,IAAAF,IAAA;UACA,KAAA1H,eAAA;UACA,KAAAC,QAAA,CAAAQ,YAAA,GAAAkF,SAAA;QACA;MACA;IACA;IACAkC,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,KAAAtJ,QAAA;QACA+C,MAAA;QACAjB,QAAA;QACAC,QAAA;QACAiB,MAAA;QACA/C,QAAA;QACAyC,eAAA;QACAR,MAAA;QACAgB,YAAA;QACAX,SAAA;QACAN,YAAA;QACAW,KAAA;QACAE,MAAA;QACAK,QAAA;QACAC,MAAA;QACAC,UAAA;QACAC,UAAA;QACAE,UAAA;QACAD,YAAA;MACA;MACA;MACA,KAAAxC,KAAA,CAAAwI,GAAA,WAAAL,IAAA;QACAI,MAAA,CAAAL,eAAA,CAAAC,IAAA;MACA;MACA,KAAApI,OAAA,QAAAC,KAAA;MACA,KAAAf,QAAA,CAAAwD,UAAA;MACA,KAAAxD,QAAA,CAAAuD,YAAA;MACA,KAAAhC,cAAA;MACA,KAAAb,YAAA;MACA,KAAAD,MAAA;MACA,KAAAmE,UAAA;IACA;IACA4E,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,SAAA3I,OAAA,UAAAC,KAAA;QACA,KAAAoI,KAAA,MAAArI,OAAA,EAAA4I,QAAA,WAAAC,KAAA;UACA,IAAAA,KAAA;YACAF,MAAA,CAAAhJ,MAAA;YACA,IAAAgJ,MAAA,CAAAzJ,QAAA,CAAA+G,EAAA;cACAjI,IAAA,CAAA8K,IAAA,CAAAH,MAAA,CAAAzJ,QAAA,EAAA6H,IAAA,WAAAC,GAAA;gBACA,IAAAA,GAAA;kBACA2B,MAAA,CAAA1B,QAAA,CAAAC,OAAA;kBACAyB,MAAA,CAAA/I,YAAA;kBACA+I,MAAA,CAAAJ,WAAA;gBACA;gBACAI,MAAA,CAAA3E,YAAA;cACA,GACAmD,OAAA;gBACAwB,MAAA,CAAAhJ,MAAA;cACA;YACA;cACA3B,IAAA,CAAA+K,GAAA,CAAAJ,MAAA,CAAAzJ,QAAA,EAAA6H,IAAA,WAAAC,GAAA;gBACA,IAAAA,GAAA;kBACA2B,MAAA,CAAA1B,QAAA,CAAAC,OAAA;kBACAyB,MAAA,CAAAJ,WAAA;gBACA;gBACAI,MAAA,CAAA3E,YAAA;cACA,GACAmD,OAAA;gBACAwB,MAAA,CAAAhJ,MAAA;cACA;YACA;UACA;QACA;MACA;MACA,SAAAK,OAAA,UAAAC,KAAA,YAAAf,QAAA,CAAA+G,EAAA;QACA,KAAAoC,KAAA,MAAArI,OAAA,EAAA4I,QAAA,WAAAC,KAAA;UACA,IAAAA,KAAA;YACAF,MAAA,CAAAhJ,MAAA;YACA3B,IAAA,CAAAgL,aAAA,CAAAL,MAAA,CAAAzJ,QAAA,EACA6H,IAAA,WAAAC,GAAA;cACA,IAAAA,GAAA;gBACA2B,MAAA,CAAA1B,QAAA,CAAAC,OAAA;gBACAyB,MAAA,CAAA/I,YAAA;gBACA+I,MAAA,CAAAJ,WAAA;cACA;cACAI,MAAA,CAAA3E,YAAA;YACA,GACAmD,OAAA;cACAwB,MAAA,CAAAhJ,MAAA;YACA;UACA;QACA;MACA;MACA,SAAAK,OAAA,UAAAC,KAAA,YAAAf,QAAA,CAAA+G,EAAA;QACA,KAAAoC,KAAA,MAAArI,OAAA,EAAA4I,QAAA,WAAAC,KAAA;UACA,IAAAA,KAAA;YACA,IAAAI,YAAA,GAAAN,MAAA,CAAAO,qBAAA;YACA;YACA,IAAAC,YAAA,YAAAA,aAAA;cAAA,IAAAhE,GAAA,GAAAiB,SAAA,CAAAT,MAAA,QAAAS,SAAA,QAAAC,SAAA,GAAAD,SAAA;cAAA,IAAAgD,SAAA,GAAAC,0BAAA,CACAlE,GAAA;gBAAAmE,KAAA;cAAA;gBAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAI,CAAA,IAAAC,IAAA;kBAAA,IAAAC,KAAA,GAAAJ,KAAA,CAAA3K,KAAA;kBACA,IAAAgL,WAAA,GAAAD,KAAA,CAAAhE,QAAA,GAAAgE,KAAA,CAAAhE,QAAA,CAAA+C,GAAA,WAAApE,CAAA;oBAAA,OAAAA,CAAA,CAAAuF,WAAA;kBAAA;kBACA,IAAAlG,IAAA;kBAAA,IAAAmG,UAAA,GAAAR,0BAAA,CACAM,WAAA;oBAAAG,MAAA;kBAAA;oBAAA,KAAAD,UAAA,CAAAN,CAAA,MAAAO,MAAA,GAAAD,UAAA,CAAAL,CAAA,IAAAC,IAAA;sBAAA,IAAAM,KAAA,GAAAD,MAAA,CAAAnL,KAAA;sBACA,IAAAsK,YAAA,CAAAvE,QAAA,CAAAqF,KAAA;wBACArG,IAAA;wBACA;sBACA;oBACA;kBAAA,SAAAsG,GAAA;oBAAAH,UAAA,CAAAI,CAAA,CAAAD,GAAA;kBAAA;oBAAAH,UAAA,CAAAK,CAAA;kBAAA;kBACA,IAAAxG,IAAA,cAAAuF,YAAA,CAAAvE,QAAA,CAAAgF,KAAA,CAAAE,WAAA;oBACAX,YAAA,CAAAxD,IAAA,CAAAiE,KAAA,CAAAE,WAAA;kBACA;kBACA,IAAAF,KAAA,CAAAhE,QAAA,EAAAyD,YAAA,CAAAO,KAAA,CAAAhE,QAAA;gBACA;cAAA,SAAAsE,GAAA;gBAAAZ,SAAA,CAAAa,CAAA,CAAAD,GAAA;cAAA;gBAAAZ,SAAA,CAAAc,CAAA;cAAA;YACA;YACAf,YAAA,CAAAR,MAAA,CAAAzI,WAAA;YAAA,IAAAiK,UAAA,GAAAd,0BAAA,CACAV,MAAA,CAAAzI,WAAA;cAAAkK,MAAA;YAAA;cAAA,KAAAD,UAAA,CAAAZ,CAAA,MAAAa,MAAA,GAAAD,UAAA,CAAAX,CAAA,IAAAC,IAAA;gBAAA,IAAAC,KAAA,GAAAU,MAAA,CAAAzL,KAAA;gBACA,IAAAgL,WAAA,GAAAD,KAAA,CAAAhE,QAAA,GAAAgE,KAAA,CAAAhE,QAAA,CAAA+C,GAAA,WAAApE,CAAA;kBAAA,OAAAA,CAAA,CAAAuF,WAAA;gBAAA;gBACA,IAAAlG,IAAA;gBAAA,IAAA2G,UAAA,GAAAhB,0BAAA,CACAM,WAAA;kBAAAW,MAAA;gBAAA;kBAAA,KAAAD,UAAA,CAAAd,CAAA,MAAAe,MAAA,GAAAD,UAAA,CAAAb,CAAA,IAAAC,IAAA;oBAAA,IAAAM,KAAA,GAAAO,MAAA,CAAA3L,KAAA;oBACA,IAAAsK,YAAA,CAAAvE,QAAA,CAAAqF,KAAA;sBACArG,IAAA;sBACA;oBACA;kBACA;gBAAA,SAAAsG,GAAA;kBAAAK,UAAA,CAAAJ,CAAA,CAAAD,GAAA;gBAAA;kBAAAK,UAAA,CAAAH,CAAA;gBAAA;gBACA,IAAAxG,IAAA,cAAAuF,YAAA,CAAAvE,QAAA,CAAAgF,KAAA,CAAAE,WAAA;kBACAX,YAAA,CAAAxD,IAAA,CAAAiE,KAAA,CAAAE,WAAA;gBACA;cACA;YAAA,SAAAI,GAAA;cAAAG,UAAA,CAAAF,CAAA,CAAAD,GAAA;YAAA;cAAAG,UAAA,CAAAD,CAAA;YAAA;YACAvB,MAAA,CAAAhJ,MAAA;YACAlC,SAAA,CAAA8M,qBAAA;cACAtI,MAAA,EAAA0G,MAAA,CAAAzJ,QAAA,CAAA+G,EAAA;cACAgD,YAAA,EAAAA;YACA,GAAAlC,IAAA,WAAAC,GAAA;cACA,IAAAA,GAAA;gBACA2B,MAAA,CAAA1B,QAAA,CAAAC,OAAA;gBACAyB,MAAA,CAAA/I,YAAA;gBACA+I,MAAA,CAAAJ,WAAA;cACA;cACAI,MAAA,CAAA3E,YAAA;YACA,GAAAmD,OAAA;cACAwB,MAAA,CAAAhJ,MAAA;YACA;UACA;QACA;MACA;MACA,SAAAK,OAAA,UAAAC,KAAA,YAAAf,QAAA,CAAA+G,EAAA;QACA,KAAAoC,KAAA,MAAArI,OAAA,EAAA4I,QAAA,WAAAC,KAAA;UACA,KAAAA,KAAA;YACA;UACA;UACAF,MAAA,CAAAhJ,MAAA;UACA,IAAA6K,OAAA;UACA7B,MAAA,CAAAnI,aAAA,CAAAiI,GAAA,WAAAhE,IAAA;YACA,IAAAA,IAAA,EAAA+F,OAAA,IAAA/F,IAAA;UACA;UACA,IAAAgG,UAAA;UACA9B,MAAA,CAAAvI,WAAA,CAAAqI,GAAA,WAAAhE,IAAA;YAAA,OAAAA,IAAA,aAAAgE,GAAA,WAAAiC,IAAA;cAAA,OAAAD,UAAA,IAAAC,IAAA,CAAAzE,EAAA;YAAA;UAAA;UACAjI,IAAA,CAAA2M,aAAA;YAAA1E,EAAA,EAAA0C,MAAA,CAAAzJ,QAAA,CAAA+G,EAAA;YAAA,WAAAuE,OAAA;YAAA,cAAAC;UAAA,GAAA1D,IAAA,WAAAC,GAAA;YACA,IAAAA,GAAA;cACA2B,MAAA,CAAA1B,QAAA,CAAAC,OAAA;cACAyB,MAAA,CAAAzJ,QAAA,CAAA2C,SAAA;YACA;YACA8G,MAAA,CAAA3E,YAAA;UACA,GAAAmD,OAAA;YACAwB,MAAA,CAAAhJ,MAAA;YACAgJ,MAAA,CAAA/I,YAAA;UACA;QACA;MACA;MACA,SAAAI,OAAA,UAAAC,KAAA,YAAAf,QAAA,CAAA+G,EAAA;QACA,KAAAoC,KAAA,MAAArI,OAAA,EAAA4I,QAAA,WAAAC,KAAA;UACA,IAAAA,KAAA;YACAF,MAAA,CAAAhJ,MAAA;YACA3B,IAAA,CAAA4M,cAAA;cAAA3E,EAAA,EAAA0C,MAAA,CAAAzJ,QAAA,CAAA+G,EAAA;cAAA9G,QAAA,EAAAwJ,MAAA,CAAAzJ,QAAA,CAAAC;YAAA,GACA4H,IAAA,WAAAC,GAAA;cACA,IAAAA,GAAA;gBACA2B,MAAA,CAAA1B,QAAA,CAAAC,OAAA;gBACAyB,MAAA,CAAA/I,YAAA;gBACA+I,MAAA,CAAAJ,WAAA;cACA;cACAI,MAAA,CAAA3E,YAAA;YACA,GACAmD,OAAA;cACAwB,MAAA,CAAAhJ,MAAA;cACAgJ,MAAA,CAAA/I,YAAA;YACA;UACA;QACA;MACA;IACA;IACAoE,YAAA,WAAAA,aAAApD,IAAA;MAAA,IAAAiK,MAAA;MACA,IAAAjK,IAAA;QACA,KAAAD,QAAA,CAAAC,IAAA,GAAAA,IAAA;MACA;MACA,KAAAlB,OAAA;MACA,IAAAoL,MAAA,GAAAhF,aAAA,CAAAA,aAAA,KACA,KAAAnF,QAAA;QACAoK,aAAA,OAAApK,QAAA,CAAAQ,YAAA,QAAAR,QAAA,CAAAQ,YAAA,CAAA6J,QAAA,KAAA3E;MAAA,EACA;MACA,OAAAyE,MAAA,CAAA3J,YAAA;MACAnD,IAAA,CAAAiN,QAAA,CAAAH,MAAA,EAAA/D,IAAA,WAAAC,GAAA;QACA6D,MAAA,CAAAxM,IAAA,GAAA2I,GAAA,CAAA3I,IAAA,CAAA6M,OAAA;QACAL,MAAA,CAAAlK,QAAA,CAAAwK,KAAA,GAAAC,QAAA,CAAApE,GAAA,CAAA3I,IAAA,CAAA8M,KAAA;MACA,GAAAhE,OAAA;QACA0D,MAAA,CAAAnL,OAAA;MACA;IACA;IACAwJ,qBAAA,WAAAA,sBAAA;MACA,IAAAmC,KAAA,QAAAhD,KAAA,SAAAiD,cAAA;MACA,OAAAD,KAAA,CAAA5E,MAAA,MAAAvH,QAAA,CAAAuD,YAAA;IACA;IACAuD,qBAAA,WAAAA,sBAAAC,EAAA,EAAAsF,QAAA;MACA,IAAAC,IAAA;MACAA,IAAA,CAAA/L,eAAA;MACA,IAAAgM,IAAA,GAAAzN,IAAA,CAAA0N,YAAA;QAAAzF,EAAA,EAAAA;MAAA;MACA,IAAA0F,IAAA,GAAA3N,IAAA,CAAA0N,YAAA;QAAAvN,IAAA,EAAAoN;MAAA;MACAK,OAAA,CAAAC,GAAA,EAAAJ,IAAA,EAAAE,IAAA,GAAA5E,IAAA,WAAA+E,MAAA;QACA,IAAAL,IAAA,GAAAK,MAAA;QACA,IAAAH,IAAA,GAAAG,MAAA;QACA,IAAAC,OAAA,GAAAJ,IAAA,CAAAtN,IAAA;QACA,IAAA2N,OAAA;QACAD,OAAA,CAAAtD,GAAA,WAAAhE,IAAA;UACA,IAAAwH,OAAA,GAAAxH,IAAA;UACA,IAAAyH,IAAA,GAAAF,OAAA,CAAAC,OAAA;UACAC,IAAA,CAAAzG,IAAA,CAAAhB,IAAA;UACAuH,OAAA,CAAAC,OAAA,IAAAC,IAAA;QACA;QACA,IAAA9L,WAAA;QACA,SAAA6L,OAAA,IAAAD,OAAA;UACA5L,WAAA,CAAAqF,IAAA;YAAA,WAAAwG,OAAA;YAAA,YAAAD,OAAA,CAAAC,OAAA;UAAA;QACA;QACA7L,WAAA,CAAAU,IAAA,WAAAqL,EAAA,EAAAC,EAAA;UAAA,OAAAD,EAAA,YAAAE,aAAA,CAAAD,EAAA;QAAA;QACAZ,IAAA,CAAApL,WAAA,GAAAA,WAAA;QACA,IAAAkM,UAAA;QACA,IAAAC,QAAA,IAAAd,IAAA,CAAApN,IAAA,QAAAoK,GAAA,WAAAhE,IAAA;UAAA,OAAAA,IAAA;QAAA;QACA,SAAA+H,CAAA,MAAAA,CAAA,GAAApM,WAAA,CAAAuF,MAAA,EAAA6G,CAAA;UACA,IAAAnO,IAAA,OAAAoO,GAAA;UACA,IAAAC,QAAA,GAAAtM,WAAA,CAAAoM,CAAA;UACA,SAAAG,CAAA,MAAAA,CAAA,GAAAD,QAAA,CAAA/G,MAAA,EAAAgH,CAAA;YACA,IAAAJ,QAAA,CAAA/G,OAAA,CAAAkH,QAAA,CAAAC,CAAA;cACAtO,IAAA,CAAA0K,GAAA,CAAA2D,QAAA,CAAAC,CAAA;YACA;UACA;UACAL,UAAA,CAAAE,CAAA,IAAAI,KAAA,CAAAC,IAAA,CAAAxO,IAAA;QACA;QACAmN,IAAA,CAAAhL,aAAA,GAAA8L,UAAA;QACAd,IAAA,CAAA/L,eAAA;QACA+L,IAAA,CAAA5L,YAAA;MACA;IACA;IACAkN,cAAA,WAAAA,eAAA9F,GAAA;MACA;MACA,KAAAxG,aAAA,CAAAwG,GAAA,CAAA+F,KAAA,IAAA/F,GAAA,CAAA3I,IAAA;MACA;MACA,KAAAmC,aAAA,GAAAsE,IAAA,CAAAC,KAAA,CACAD,IAAA,CAAAE,SAAA,MAAAxE,aAAA,CACA;IACA;IACAwM,YAAA,WAAAA,aAAA;MACA;MACA,IAAAC,SAAA,OAAAR,GAAA;QACApO,IAAA;MACA,IAAA6O,KAAA,QAAAC,kBAAA,CAAAC,KAAA;MACAF,KAAA,CAAAzE,GAAA,WAAAhE,IAAA;QACAA,IAAA,CAAA2I,KAAA;QACA,IAAAC,KAAA,GAAA5I,IAAA,CAAAD,MAAA,WAAAC,IAAA;UACA,OAAApG,IAAA,CAAAmH,OAAA,CAAAf,IAAA;QACA;QACA4I,KAAA,CAAA5E,GAAA,WAAAhE,IAAA;UACAwI,SAAA,CAAAlE,GAAA,CAAAtE,IAAA;QACA;MACA;MACA,KAAAjE,aAAA,GAAAoM,KAAA,CAAAC,IAAA,CAAAI,SAAA;IACA;IACAlF,qBAAA,WAAAA,sBAAA9F,MAAA;MACA,IAAAuJ,IAAA;MACA,IAAA8B,EAAA,GAAA7P,SAAA,CAAA8P,gBAAA;MACA,IAAAC,EAAA,GAAA/P,SAAA,CAAAgQ,mBAAA,CAAAxL,MAAA;MACA,IAAAyL,cAAA;MACA9B,OAAA,CAAAC,GAAA,EAAAyB,EAAA,EAAAE,EAAA,GAAAzG,IAAA,WAAA4G,MAAA;QACA,IAAAlC,IAAA,GAAAkC,MAAA;QACA,IAAAhC,IAAA,GAAAgC,MAAA;QACA,IAAAlC,IAAA,CAAAmC,IAAA,UAAAnC,IAAA,CAAApN,IAAA;UACA,IAAAwP,GAAA;YAAAC,UAAA;YAAAC,SAAA;YAAAC,QAAA;UAAA;UACA,IAAArC,IAAA,CAAAiC,IAAA,UAAAjC,IAAA,CAAAtN,IAAA,IAAAsN,IAAA,CAAAtN,IAAA,CAAAsH,MAAA;YACA,IAAA0F,KAAA;YACA,IAAA4C,OAAA;YACAtC,IAAA,CAAAtN,IAAA,CAAAoK,GAAA,WAAAhE,IAAA;cACA,IAAAA,IAAA,CAAAyJ,KAAA;gBACAR,cAAA,CAAAjI,IAAA,CAAAhB,IAAA,CAAAmF,WAAA;cACA;cACA;cACA,IAAAnF,IAAA,CAAA0J,SAAA,CAAA3I,OAAA,qBAAA6F,KAAA,CAAA3G,QAAA,CAAAD,IAAA,CAAAmF,WAAA;gBACAyB,KAAA,CAAA5F,IAAA,CAAAhB,IAAA,CAAAmF,WAAA;cACA;cACA;cACA,IAAAnF,IAAA,CAAA0J,SAAA,CAAA3I,OAAA,uBAAAyI,OAAA,CAAAvJ,QAAA,CAAAD,IAAA,CAAAmF,WAAA;gBACAqE,OAAA,CAAAxI,IAAA,CAAAhB,IAAA,CAAAmF,WAAA;cACA;YACA;YACA4B,IAAA,CAAAtM,QAAA,CAAAwD,UAAA,GAAA2I,KAAA;YACAG,IAAA,CAAAtM,QAAA,CAAAuD,YAAA,GAAAwL,OAAA;UACA;UACAxC,IAAA,CAAApN,IAAA,CAAAoK,GAAA,WAAAhE,IAAA;YACA;YACA,IAAA+G,IAAA,CAAAtM,QAAA,CAAAwD,UAAA,CAAAgC,QAAA,CAAAD,IAAA,CAAAmF,WAAA;cACAnF,IAAA,CAAA2J,UAAA;cACA;cACA,IAAAV,cAAA,CAAAhJ,QAAA,CAAAD,IAAA,CAAAmF,WAAA;gBACA;gBACAnF,IAAA,CAAA4J,QAAA;gBACA5J,IAAA,CAAAc,QAAA;cACA;YACA;YACA;YACAd,IAAA,CAAA6J,UAAA,CAAA7F,GAAA,WAAA8F,MAAA;cACA,IAAAb,cAAA,CAAAhJ,QAAA,CAAA6J,MAAA,CAAA3E,WAAA;gBACA2E,MAAA,CAAAF,QAAA;cACA;YACA;UACA;UACA7C,IAAA,CAAAtL,WAAA,GAAA1C,eAAA,CAAAiO,IAAA,CAAApN,IAAA,EAAAwP,GAAA;UACArC,IAAA,CAAAvG,SAAA,GAAAH,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAAwG,IAAA,CAAAtL,WAAA;QACA;QACAsL,IAAA,CAAA5L,YAAA;MACA;IACA;IACAkI,eAAA,WAAAA,gBAAA7F,MAAA;MACA,KAAAA,MAAA;QACA;MACA;MACA,IAAAuJ,IAAA;MACA,IAAA8B,EAAA,GAAA/P,IAAA,CAAAiR,MAAA;MACA,IAAAhB,EAAA,GAAAxP,IAAA,CAAAyQ,YAAA,CAAAxM,MAAA;MACA2J,OAAA,CAAAC,GAAA,EAAAyB,EAAA,EAAAE,EAAA,GAAAzG,IAAA,WAAA4G,MAAA;QACA,IAAAlC,IAAA,GAAAkC,MAAA;QACA,IAAAhC,IAAA,GAAAgC,MAAA;QACA,IAAAlC,IAAA,CAAAmC,IAAA;UACA,IAAAc,OAAA;UACAjD,IAAA,CAAApN,IAAA,CAAAoK,GAAA,WAAAhE,IAAA;YACA,IAAAA,IAAA,CAAAE,MAAA;cACA+J,OAAA,CAAAjJ,IAAA,CAAAhB,IAAA;YACA;UACA;UACA+G,IAAA,CAAArL,WAAA,GAAAuO,OAAA;UACAlD,IAAA,CAAAjH,eAAA,GAAAmK,OAAA;QACA;QACA,IAAA/C,IAAA,CAAAiC,IAAA;UACA,IAAA9B,MAAA;UACAH,IAAA,CAAAtN,IAAA,CAAAoK,GAAA,WAAAhE,IAAA;YACAqH,MAAA,CAAArG,IAAA,CAAAhB,IAAA,CAAAwB,EAAA;UACA;UACAuF,IAAA,CAAAtM,QAAA,CAAAqD,UAAA,GAAAuJ,MAAA;QACA;QACAN,IAAA,CAAA5L,YAAA;MACA;IACA;IACA+O,UAAA,WAAAA,WAAA3O,OAAA;MACA,KAAAW,QAAA,CAAAC,IAAA,GAAAZ,OAAA;MACA,KAAAgE,YAAA;IACA;IACA4K,cAAA,WAAAA,eAAAC,IAAA;MACA,KAAAlO,QAAA,CAAAmO,KAAA,GAAAD,IAAA;MACA,KAAA7K,YAAA;IACA;IACAC,aAAA,WAAAA,cAAA;MAAA,IAAA8K,MAAA;MACAjR,SAAA,CAAAkR,aAAA,GAAAjI,IAAA,WAAAC,GAAA;QACA+H,MAAA,CAAA1O,WAAA,GAAA2G,GAAA,CAAA3I,IAAA;MACA;IACA;IACAsJ,WAAA,WAAAA,YAAAsH,GAAA;MAAA,IAAAC,MAAA;MACA,IAAAD,GAAA;QACA,KAAA/P,QAAA,CAAAiC,YAAA;QACAlD,cAAA,CAAAgR,GAAA,EAAAlI,IAAA,WAAAC,GAAA;UACAkI,MAAA,CAAA5O,cAAA,GAAA0G,GAAA,CAAA3I,IAAA;UACA,IAAAwP,GAAA;YACAC,UAAA;YACAC,SAAA;YACAC,QAAA;UACA;UACAkB,MAAA,CAAAzO,cAAA,GAAAjD,eAAA,CAAAwJ,GAAA,CAAA3I,IAAA,EAAAwP,GAAA;QACA;MACA;IACA;IACA3J,WAAA,WAAAA,YAAA;MAAA,IAAAiL,OAAA;MACArR,SAAA,CAAAsR,UAAA,GAAArI,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA;UACAA,GAAA,CAAA3I,IAAA,CAAAoK,GAAA,WAAAhE,IAAA;YACA,IAAAA,IAAA,CAAAE,MAAA,UAAAwK,OAAA,CAAA9N,OAAA,CAAAoE,IAAA,CAAAhB,IAAA;UACA;QACA;MACA;IACA;IACA4K,cAAA,WAAAA,eAAAlR,IAAA;MACA,KAAA6B,OAAA,GAAA7B,IAAA;MACA,KAAAsJ,WAAA,MAAAC,UAAA,OAAAxE,GAAA;IACA;IACAoM,WAAA,WAAAA,YAAA;MAAA,IAAAC,OAAA;MAAA,IAAApK,GAAA,GAAAiB,SAAA,CAAAT,MAAA,QAAAS,SAAA,QAAAC,SAAA,GAAAD,SAAA;MAAA,IAAAoJ,KAAA,GAAApJ,SAAA,CAAAT,MAAA,QAAAS,SAAA,QAAAC,SAAA,GAAAD,SAAA;MACA,IAAAtC,UAAA,QAAAA,UAAA;MACAqB,GAAA,CAAAE,OAAA,WAAAZ,IAAA;QACA,IAAA+K,KAAA;UACA/K,IAAA,CAAAgL,aAAA;UACAhL,IAAA,CAAAiL,OAAA;QACA;UACAjL,IAAA,CAAA7B,KAAA,GAAA6B,IAAA,CAAAkL,WAAA;UACAlL,IAAA,CAAAmL,MAAA,GAAAnL,IAAA,CAAAoL,KAAA;UACApL,IAAA,CAAAiL,OAAA,GAAA5L,UAAA,CAAAY,QAAA,CAAAD,IAAA,CAAAwB,EAAA;QACA;QACA,IAAAxB,IAAA,CAAAiB,QAAA,IAAAjB,IAAA,CAAAiB,QAAA,CAAAC,MAAA;UACA4J,OAAA,CAAAD,WAAA,CAAA7K,IAAA,CAAAiB,QAAA,EAAA8J,KAAA;QACA;MACA;IACA;IACAM,eAAA,WAAAA,gBAAAC,OAAA;MACA,IAAAA,OAAA;QACA,KAAAT,WAAA,MAAAzL,kBAAA;MACA;IACA;EACA;AACA"}]}