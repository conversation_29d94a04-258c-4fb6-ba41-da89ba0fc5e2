{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\login\\login-success.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\login\\login-success.vue", "mtime": 1752737748507}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\.babelrc", "mtime": 1752737748316}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgZ2V0UGFyYW1zIH0gZnJvbSAnQC9saWJzL3V0aWwnOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ0xvZ2luU3VjY2VzcycsCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGZyYW1lSGVpZ2h0OiAwLAogICAgICBzY3JlZW5IZWlnaHQ6IDAKICAgIH07CiAgfSwKICB3YXRjaDogewogICAgJyRyb3V0ZSc6IGZ1bmN0aW9uICRyb3V0ZSh2YWwpIHsKICAgICAgdGhpcy5nZXRSb3V0ZXJEYXRhKCk7CiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICBnZXRSb3V0ZXJEYXRhOiBmdW5jdGlvbiBnZXRSb3V0ZXJEYXRhKCkgewogICAgICAvL+iOt+WPliBpZnJhbWUgc3JjIOi3r+W+hAogICAgICB2YXIgcGFyYW1zID0gZ2V0UGFyYW1zKHdpbmRvdy5sb2NhdGlvbi5ocmVmKTsKICAgICAgdmFyIHRva2VuID0gZGVjb2RlVVJJQ29tcG9uZW50KHBhcmFtcy50b2tlbik7CiAgICAgIGlmICh0b2tlbikgewogICAgICAgIHRoaXMuJHN0b3JlLmNvbW1pdCgic2V0VG9rZW4iLCB7CiAgICAgICAgICB0b2tlbjogdG9rZW4sCiAgICAgICAgICBhdXRvOiB0cnVlCiAgICAgICAgfSk7CiAgICAgICAgaWYgKHdpbmRvdy5vcGVuZXIgJiYgIXdpbmRvdy5vcGVuZXIuY2xvc2VkKSB7CiAgICAgICAgICB3aW5kb3cucGFyZW50Lm9wZW5lci5sb2NhdGlvbi5yZWxvYWQoKTsKICAgICAgICB9CiAgICAgICAgd2luZG93LmNsb3NlKCk7CiAgICAgIH0KICAgIH0KICB9LAogIG1vdW50ZWQ6IGZ1bmN0aW9uIG1vdW50ZWQoKSB7CiAgICB0aGlzLmdldFJvdXRlckRhdGEoKTsKICB9Cn07"}, {"version": 3, "names": ["getParams", "name", "data", "frameHeight", "screenHeight", "watch", "$route", "val", "getRouterData", "methods", "params", "window", "location", "href", "token", "decodeURIComponent", "$store", "commit", "auto", "opener", "closed", "parent", "reload", "close", "mounted"], "sources": ["src/view/login/login-success.vue"], "sourcesContent": ["<template id=\"success\">\r\n  <div>\r\n    登录成功\r\n  </div>\r\n</template>\r\n<script>\r\n  import { getParams } from '@/libs/util'\r\n  export default {\r\n    name: 'LoginSuccess',\r\n    data () {\r\n      return {\r\n        frameHeight: 0,\r\n        screenHeight:0\r\n      }\r\n    }\r\n    ,\r\n    watch: {\r\n      '$route'(val){\r\n        this.getRouterData()\r\n      }\r\n    },\r\n    methods: {\r\n      getRouterData: function () {//获取 iframe src 路径\r\n        const params = getParams(window.location.href);\r\n        let token = decodeURIComponent(params.token);\r\n        if(token){\r\n          this.$store.commit(\"setToken\",{token , auto:true})\r\n          if (window.opener && !window.opener.closed) {\r\n            window.parent.opener.location.reload();\r\n          }\r\n          window.close();\r\n        }\r\n      },\r\n    },\r\n    mounted(){\r\n      this.getRouterData()\r\n    }\r\n  }\r\n</script>\r\n"], "mappings": "AAMA,SAAAA,SAAA;AACA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA;MACAC,YAAA;IACA;EACA;EAEAC,KAAA;IACA,mBAAAC,OAAAC,GAAA;MACA,KAAAC,aAAA;IACA;EACA;EACAC,OAAA;IACAD,aAAA,WAAAA,cAAA;MAAA;MACA,IAAAE,MAAA,GAAAV,SAAA,CAAAW,MAAA,CAAAC,QAAA,CAAAC,IAAA;MACA,IAAAC,KAAA,GAAAC,kBAAA,CAAAL,MAAA,CAAAI,KAAA;MACA,IAAAA,KAAA;QACA,KAAAE,MAAA,CAAAC,MAAA;UAAAH,KAAA,EAAAA,KAAA;UAAAI,IAAA;QAAA;QACA,IAAAP,MAAA,CAAAQ,MAAA,KAAAR,MAAA,CAAAQ,MAAA,CAAAC,MAAA;UACAT,MAAA,CAAAU,MAAA,CAAAF,MAAA,CAAAP,QAAA,CAAAU,MAAA;QACA;QACAX,MAAA,CAAAY,KAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAhB,aAAA;EACA;AACA"}]}