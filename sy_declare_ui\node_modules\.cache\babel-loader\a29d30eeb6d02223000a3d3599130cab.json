{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\store\\module\\user.js", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\store\\module\\user.js", "mtime": 1752737748505}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\.babelrc", "mtime": 1752737748316}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["login", "logout", "getUserInfo", "getCurrentUserMenu", "setToken", "getToken", "state", "userName", "userId", "nick<PERSON><PERSON>", "avatar<PERSON><PERSON>g<PERSON><PERSON>", "token", "access", "hasGetInfo", "userDesc", "mobile", "email", "dingId", "menus", "mutations", "set<PERSON>vat<PERSON>", "avat<PERSON><PERSON><PERSON>", "setNickName", "setUserId", "id", "setDingId", "setUserName", "name", "setAccess", "_ref", "auto", "setHasGetInfo", "status", "setUserMenus", "setMobile", "setEmail", "setUserDesc", "actions", "handleLogin", "_ref2", "_ref3", "commit", "username", "password", "trim", "Promise", "resolve", "reject", "then", "res", "catch", "err", "handleLogout", "_ref4", "localStorage", "removeItem", "_ref5", "data", "avatar", "authorities", "map", "item", "authority", "push"], "sources": ["D:/Users/<USER>/Desktop/dev/sy_declare_ui/src/store/module/user.js"], "sourcesContent": ["import { login, logout, getUserInfo, getCurrentUserMenu } from '@/api/system/user'\r\nimport { setToken, getToken } from '@/libs/util'\r\n\r\nexport default {\r\n  state: {\r\n    userName: '',\r\n    userId: '',\r\n    nickName: '',\r\n    avatarImgPath: '',\r\n    token: getToken(),\r\n    access: [],\r\n    hasGetInfo: false,\r\n    userDesc: '',\r\n    mobile: '',\r\n    email: '',\r\n    dingId:'',\r\n    menus: []// 用户菜单\r\n  },\r\n  mutations: {\r\n    setAvatar (state, avatarPath) {\r\n      state.avatarImgPath = avatarPath\r\n    },\r\n    setNickName (state, nickName) {\r\n      state.nickName = nickName\r\n    },\r\n    setUserId (state, id) {\r\n      state.userId = id\r\n    },\r\n    setDingId (state, id) {\r\n      state.dingId = id\r\n    },\r\n    setUserName (state, name) {\r\n      state.userName = name\r\n    },\r\n    setAccess (state, access) {\r\n      state.access = access\r\n    },\r\n    setToken (state, { token, auto }) {\r\n      state.token = token\r\n      setToken(token, auto)\r\n    },\r\n    setHasGetInfo (state, status) {\r\n      state.hasGetInfo = status\r\n    },\r\n    setUserMenus (state, menus) {\r\n      state.menus = menus\r\n    },\r\n    setMobile (state, mobile) {\r\n      state.mobile = mobile\r\n    },\r\n    setEmail (state, email) {\r\n      state.email = email\r\n    },\r\n    setUserDesc (state, userDesc) {\r\n      state.userDesc = userDesc\r\n    }\r\n  },\r\n  actions: {\r\n    // 登录\r\n    handleLogin ({ commit }, { username, password, auto }) {\r\n      username = username.trim()\r\n      return new Promise((resolve, reject) => {\r\n        login({\r\n          username,\r\n          password\r\n        }).then(res => {\r\n          if (res) {\r\n            if (res[\"code\"] === 0) {\r\n              let token = res[\"data\"][\"access_token\"]\r\n              commit('setToken', { token, auto })\r\n              resolve(res)\r\n            }\r\n          }\r\n        }).catch(err => {\r\n          reject(err)\r\n        })\r\n      })\r\n    },\r\n    // 退出登录\r\n    handleLogout ({commit }) {\r\n      return new Promise((resolve, reject) => {\r\n        logout().then(res => {\r\n          commit('setToken', '')\r\n          commit('setAccess', [])\r\n          commit('setHasGetInfo', false)\r\n          resolve(res)\r\n          localStorage.removeItem('tagNaveList') //清除导航栏菜单显示\r\n        }).catch(err => {\r\n          reject(err)\r\n        })\r\n      })\r\n    },\r\n    // 获取用户相关信息\r\n    getUserInfo ({ state, commit }) {\r\n      return new Promise((resolve, reject) => {\r\n        getUserInfo().then(res => {\r\n          if (res[\"code\"] === 0) {\r\n            commit('setAvatar', res.data.avatar)\r\n            commit('setUserName', res.data.username)\r\n            commit('setNickName', res.data.nickName)\r\n            commit('setUserId', res.data.userId)\r\n            commit('setDingId', res.data.dingId)\r\n            commit('setEmail', res.data.email)\r\n            commit('setMobile', res.data.mobile)\r\n            commit('setUserDesc', res.data.userDesc)\r\n            const access = []\r\n            if (res.data.authorities) {\r\n              res.data.authorities.map(item => {\r\n                if (item.authority) {\r\n                  access.push(item.authority)\r\n                }\r\n              })\r\n            }\r\n            // 转换权限\r\n            commit('setAccess', access)\r\n            commit('setHasGetInfo', true)\r\n            getCurrentUserMenu().then(res => {\r\n              if (res[\"code\"] === 0) {\r\n                commit('setUserMenus', res.data)\r\n                resolve(state)\r\n              }\r\n            }).catch(err => {\r\n              reject(err)\r\n            })\r\n          }\r\n        }).catch(err => {\r\n          reject(err)\r\n        })\r\n      })\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";;;;AAAA,SAASA,KAAK,EAAEC,MAAM,EAAEC,WAAW,IAAXA,YAAW,EAAEC,kBAAkB,QAAQ,mBAAmB;AAClF,SAASC,QAAQ,IAARA,SAAQ,EAAEC,QAAQ,QAAQ,aAAa;AAEhD,eAAe;EACbC,KAAK,EAAE;IACLC,QAAQ,EAAE,EAAE;IACZC,MAAM,EAAE,EAAE;IACVC,QAAQ,EAAE,EAAE;IACZC,aAAa,EAAE,EAAE;IACjBC,KAAK,EAAEN,QAAQ,CAAC,CAAC;IACjBO,MAAM,EAAE,EAAE;IACVC,UAAU,EAAE,KAAK;IACjBC,QAAQ,EAAE,EAAE;IACZC,MAAM,EAAE,EAAE;IACVC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAC,EAAE;IACTC,KAAK,EAAE,EAAE;EACX,CAAC;;EACDC,SAAS,EAAE;IACTC,SAAS,WAAAA,UAAEd,KAAK,EAAEe,UAAU,EAAE;MAC5Bf,KAAK,CAACI,aAAa,GAAGW,UAAU;IAClC,CAAC;IACDC,WAAW,WAAAA,YAAEhB,KAAK,EAAEG,QAAQ,EAAE;MAC5BH,KAAK,CAACG,QAAQ,GAAGA,QAAQ;IAC3B,CAAC;IACDc,SAAS,WAAAA,UAAEjB,KAAK,EAAEkB,EAAE,EAAE;MACpBlB,KAAK,CAACE,MAAM,GAAGgB,EAAE;IACnB,CAAC;IACDC,SAAS,WAAAA,UAAEnB,KAAK,EAAEkB,EAAE,EAAE;MACpBlB,KAAK,CAACW,MAAM,GAAGO,EAAE;IACnB,CAAC;IACDE,WAAW,WAAAA,YAAEpB,KAAK,EAAEqB,IAAI,EAAE;MACxBrB,KAAK,CAACC,QAAQ,GAAGoB,IAAI;IACvB,CAAC;IACDC,SAAS,WAAAA,UAAEtB,KAAK,EAAEM,MAAM,EAAE;MACxBN,KAAK,CAACM,MAAM,GAAGA,MAAM;IACvB,CAAC;IACDR,QAAQ,WAAAA,SAAEE,KAAK,EAAAuB,IAAA,EAAmB;MAAA,IAAflB,KAAK,GAAAkB,IAAA,CAALlB,KAAK;QAAEmB,IAAI,GAAAD,IAAA,CAAJC,IAAI;MAC5BxB,KAAK,CAACK,KAAK,GAAGA,KAAK;MACnBP,SAAQ,CAACO,KAAK,EAAEmB,IAAI,CAAC;IACvB,CAAC;IACDC,aAAa,WAAAA,cAAEzB,KAAK,EAAE0B,MAAM,EAAE;MAC5B1B,KAAK,CAACO,UAAU,GAAGmB,MAAM;IAC3B,CAAC;IACDC,YAAY,WAAAA,aAAE3B,KAAK,EAAEY,KAAK,EAAE;MAC1BZ,KAAK,CAACY,KAAK,GAAGA,KAAK;IACrB,CAAC;IACDgB,SAAS,WAAAA,UAAE5B,KAAK,EAAES,MAAM,EAAE;MACxBT,KAAK,CAACS,MAAM,GAAGA,MAAM;IACvB,CAAC;IACDoB,QAAQ,WAAAA,SAAE7B,KAAK,EAAEU,KAAK,EAAE;MACtBV,KAAK,CAACU,KAAK,GAAGA,KAAK;IACrB,CAAC;IACDoB,WAAW,WAAAA,YAAE9B,KAAK,EAAEQ,QAAQ,EAAE;MAC5BR,KAAK,CAACQ,QAAQ,GAAGA,QAAQ;IAC3B;EACF,CAAC;EACDuB,OAAO,EAAE;IACP;IACAC,WAAW,WAAAA,YAAAC,KAAA,EAAAC,KAAA,EAA4C;MAAA,IAAxCC,MAAM,GAAAF,KAAA,CAANE,MAAM;MAAA,IAAMC,QAAQ,GAAAF,KAAA,CAARE,QAAQ;QAAEC,QAAQ,GAAAH,KAAA,CAARG,QAAQ;QAAEb,IAAI,GAAAU,KAAA,CAAJV,IAAI;MACjDY,QAAQ,GAAGA,QAAQ,CAACE,IAAI,CAAC,CAAC;MAC1B,OAAO,IAAIC,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;QACtC/C,KAAK,CAAC;UACJ0C,QAAQ,EAARA,QAAQ;UACRC,QAAQ,EAARA;QACF,CAAC,CAAC,CAACK,IAAI,CAAC,UAAAC,GAAG,EAAI;UACb,IAAIA,GAAG,EAAE;YACP,IAAIA,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;cACrB,IAAItC,KAAK,GAAGsC,GAAG,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC;cACvCR,MAAM,CAAC,UAAU,EAAE;gBAAE9B,KAAK,EAALA,KAAK;gBAAEmB,IAAI,EAAJA;cAAK,CAAC,CAAC;cACnCgB,OAAO,CAACG,GAAG,CAAC;YACd;UACF;QACF,CAAC,CAAC,CAACC,KAAK,CAAC,UAAAC,GAAG,EAAI;UACdJ,MAAM,CAACI,GAAG,CAAC;QACb,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IACD;IACAC,YAAY,WAAAA,aAAAC,KAAA,EAAa;MAAA,IAAVZ,MAAM,GAAAY,KAAA,CAANZ,MAAM;MACnB,OAAO,IAAII,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;QACtC9C,MAAM,CAAC,CAAC,CAAC+C,IAAI,CAAC,UAAAC,GAAG,EAAI;UACnBR,MAAM,CAAC,UAAU,EAAE,EAAE,CAAC;UACtBA,MAAM,CAAC,WAAW,EAAE,EAAE,CAAC;UACvBA,MAAM,CAAC,eAAe,EAAE,KAAK,CAAC;UAC9BK,OAAO,CAACG,GAAG,CAAC;UACZK,YAAY,CAACC,UAAU,CAAC,aAAa,CAAC,EAAC;QACzC,CAAC,CAAC,CAACL,KAAK,CAAC,UAAAC,GAAG,EAAI;UACdJ,MAAM,CAACI,GAAG,CAAC;QACb,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IACD;IACAjD,WAAW,WAAAA,YAAAsD,KAAA,EAAqB;MAAA,IAAjBlD,KAAK,GAAAkD,KAAA,CAALlD,KAAK;QAAEmC,MAAM,GAAAe,KAAA,CAANf,MAAM;MAC1B,OAAO,IAAII,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;QACtC7C,YAAW,CAAC,CAAC,CAAC8C,IAAI,CAAC,UAAAC,GAAG,EAAI;UACxB,IAAIA,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;YACrBR,MAAM,CAAC,WAAW,EAAEQ,GAAG,CAACQ,IAAI,CAACC,MAAM,CAAC;YACpCjB,MAAM,CAAC,aAAa,EAAEQ,GAAG,CAACQ,IAAI,CAACf,QAAQ,CAAC;YACxCD,MAAM,CAAC,aAAa,EAAEQ,GAAG,CAACQ,IAAI,CAAChD,QAAQ,CAAC;YACxCgC,MAAM,CAAC,WAAW,EAAEQ,GAAG,CAACQ,IAAI,CAACjD,MAAM,CAAC;YACpCiC,MAAM,CAAC,WAAW,EAAEQ,GAAG,CAACQ,IAAI,CAACxC,MAAM,CAAC;YACpCwB,MAAM,CAAC,UAAU,EAAEQ,GAAG,CAACQ,IAAI,CAACzC,KAAK,CAAC;YAClCyB,MAAM,CAAC,WAAW,EAAEQ,GAAG,CAACQ,IAAI,CAAC1C,MAAM,CAAC;YACpC0B,MAAM,CAAC,aAAa,EAAEQ,GAAG,CAACQ,IAAI,CAAC3C,QAAQ,CAAC;YACxC,IAAMF,MAAM,GAAG,EAAE;YACjB,IAAIqC,GAAG,CAACQ,IAAI,CAACE,WAAW,EAAE;cACxBV,GAAG,CAACQ,IAAI,CAACE,WAAW,CAACC,GAAG,CAAC,UAAAC,IAAI,EAAI;gBAC/B,IAAIA,IAAI,CAACC,SAAS,EAAE;kBAClBlD,MAAM,CAACmD,IAAI,CAACF,IAAI,CAACC,SAAS,CAAC;gBAC7B;cACF,CAAC,CAAC;YACJ;YACA;YACArB,MAAM,CAAC,WAAW,EAAE7B,MAAM,CAAC;YAC3B6B,MAAM,CAAC,eAAe,EAAE,IAAI,CAAC;YAC7BtC,kBAAkB,CAAC,CAAC,CAAC6C,IAAI,CAAC,UAAAC,GAAG,EAAI;cAC/B,IAAIA,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;gBACrBR,MAAM,CAAC,cAAc,EAAEQ,GAAG,CAACQ,IAAI,CAAC;gBAChCX,OAAO,CAACxC,KAAK,CAAC;cAChB;YACF,CAAC,CAAC,CAAC4C,KAAK,CAAC,UAAAC,GAAG,EAAI;cACdJ,MAAM,CAACI,GAAG,CAAC;YACb,CAAC,CAAC;UACJ;QACF,CAAC,CAAC,CAACD,KAAK,CAAC,UAAAC,GAAG,EAAI;UACdJ,MAAM,CAACI,GAAG,CAAC;QACb,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF;AACF,CAAC"}]}