{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\api\\custom\\customClass.js", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\api\\custom\\customClass.js", "mtime": 1752737748405}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\.babelrc", "mtime": 1752737748316}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "NespostRequest", "exportRequest", "categoryPath", "listTree", "params", "url", "method", "saveCustomClass", "saveDeclareElement", "getDeclareElement", "saveClearanceElement", "delClearanceElement", "getClearanceElement", "delDeclareElement", "get<PERSON>y", "remove", "getLogRefType", "getAll", "download", "callback", "config", "fileName", "downloadClearance"], "sources": ["D:/Users/<USER>/Desktop/dev/sy_declare_ui/src/api/custom/customClass.js"], "sourcesContent": ["import request from '@/libs/request'\r\nimport {NespostRequest} from '@/libs/axios.js';\r\nimport exportRequest from \"@/libs/exportRequest\";\r\n\r\nconst categoryPath = \"/base/customClass\";\r\n/**\r\n * 获取分页数据\r\n */\r\nconst listTree = (params) => {\r\n  return request({\r\n    url: categoryPath + '/listTree',\r\n    params,\r\n    method: 'post'\r\n  })\r\n}\r\n/**\r\n * 添加数据\r\n */\r\nconst saveCustomClass = (params) => {\r\n  return NespostRequest(categoryPath + '/saveCustomClass', params)\r\n}\r\nconst saveDeclareElement = (params)=>{\r\n  return NespostRequest(categoryPath + '/saveDeclareElement', params)\r\n}\r\n/**\r\n * 获取明细数据\r\n */\r\nconst getDeclareElement = (params) => {\r\n  return request({\r\n    url: categoryPath + '/getDeclareElement',\r\n    params,\r\n    method: 'get'\r\n  })\r\n}\r\nconst saveClearanceElement = (params)=>{\r\n  return NespostRequest(categoryPath + '/saveClearanceElement', params)\r\n}\r\n/**\r\n * 获取明细数据\r\n */\r\nconst delClearanceElement = (params) => {\r\n  return request({\r\n    url: categoryPath + '/delClearanceElement',\r\n    params,\r\n    method: 'post'\r\n  })\r\n}\r\n/**\r\n * 获取明细数据\r\n */\r\nconst getClearanceElement = (params) => {\r\n  return request({\r\n    url: categoryPath + '/getClearanceElement',\r\n    params,\r\n    method: 'get'\r\n  })\r\n}\r\n/**\r\n * 获取明细数据\r\n */\r\nconst delDeclareElement = (params) => {\r\n  return request({\r\n    url: categoryPath + '/delDeclareElement',\r\n    params,\r\n    method: 'post'\r\n  })\r\n}\r\n/**\r\n * 获取明细数据\r\n */\r\nconst getBy = (params) => {\r\n  return request({\r\n    url: categoryPath + '/getBy',\r\n    params,\r\n    method: 'get'\r\n  })\r\n}\r\n/**\r\n * 删除数据\r\n */\r\nconst remove = (params) => {\r\n  return request({\r\n    url: categoryPath + '/delCustomClass',\r\n    params,\r\n    method: 'post'\r\n  })\r\n}\r\n\r\n/**\r\n * 获取日志\r\n */\r\nconst getLogRefType = () => {\r\n  return request({\r\n    url: categoryPath + '/getLogRefType',\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n/**\r\n * 获取所有\r\n */\r\nconst getAll = (params) => {\r\n  return request({\r\n    url: categoryPath + '/getAll',\r\n    params,\r\n    method: 'get'\r\n  })\r\n}\r\nconst download = (params,callback)=>{\r\n  const config = {\r\n    params:params,\r\n    method: 'get',\r\n    fileName:params['fileName']\r\n  }\r\n  return exportRequest(categoryPath + '/download',config,callback);\r\n}\r\nconst downloadClearance = (params,callback)=>{\r\n  const config = {\r\n    params:params,\r\n    method: 'get',\r\n    fileName:params['fileName']\r\n  }\r\n  return exportRequest(categoryPath + '/downloadClearance',config,callback);\r\n}\r\nexport default {\r\n  listTree,\r\n  saveCustomClass,\r\n  getBy,\r\n  getAll,\r\n  saveDeclareElement,\r\n  delDeclareElement,\r\n  getDeclareElement,\r\n  saveClearanceElement,\r\n  delClearanceElement,\r\n  getClearanceElement,\r\n  remove,\r\n  getLogRefType,\r\n  download,\r\n  downloadClearance\r\n}\r\n\r\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,gBAAgB;AACpC,SAAQC,cAAc,QAAO,iBAAiB;AAC9C,OAAOC,aAAa,MAAM,sBAAsB;AAEhD,IAAMC,YAAY,GAAG,mBAAmB;AACxC;AACA;AACA;AACA,IAAMC,QAAQ,GAAG,SAAXA,QAAQA,CAAIC,MAAM,EAAK;EAC3B,OAAOL,OAAO,CAAC;IACbM,GAAG,EAAEH,YAAY,GAAG,WAAW;IAC/BE,MAAM,EAANA,MAAM;IACNE,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;AACD;AACA;AACA;AACA,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAIH,MAAM,EAAK;EAClC,OAAOJ,cAAc,CAACE,YAAY,GAAG,kBAAkB,EAAEE,MAAM,CAAC;AAClE,CAAC;AACD,IAAMI,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAIJ,MAAM,EAAG;EACnC,OAAOJ,cAAc,CAACE,YAAY,GAAG,qBAAqB,EAAEE,MAAM,CAAC;AACrE,CAAC;AACD;AACA;AACA;AACA,IAAMK,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAIL,MAAM,EAAK;EACpC,OAAOL,OAAO,CAAC;IACbM,GAAG,EAAEH,YAAY,GAAG,oBAAoB;IACxCE,MAAM,EAANA,MAAM;IACNE,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;AACD,IAAMI,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAIN,MAAM,EAAG;EACrC,OAAOJ,cAAc,CAACE,YAAY,GAAG,uBAAuB,EAAEE,MAAM,CAAC;AACvE,CAAC;AACD;AACA;AACA;AACA,IAAMO,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAIP,MAAM,EAAK;EACtC,OAAOL,OAAO,CAAC;IACbM,GAAG,EAAEH,YAAY,GAAG,sBAAsB;IAC1CE,MAAM,EAANA,MAAM;IACNE,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;AACD;AACA;AACA;AACA,IAAMM,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAIR,MAAM,EAAK;EACtC,OAAOL,OAAO,CAAC;IACbM,GAAG,EAAEH,YAAY,GAAG,sBAAsB;IAC1CE,MAAM,EAANA,MAAM;IACNE,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;AACD;AACA;AACA;AACA,IAAMO,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAIT,MAAM,EAAK;EACpC,OAAOL,OAAO,CAAC;IACbM,GAAG,EAAEH,YAAY,GAAG,oBAAoB;IACxCE,MAAM,EAANA,MAAM;IACNE,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;AACD;AACA;AACA;AACA,IAAMQ,KAAK,GAAG,SAARA,KAAKA,CAAIV,MAAM,EAAK;EACxB,OAAOL,OAAO,CAAC;IACbM,GAAG,EAAEH,YAAY,GAAG,QAAQ;IAC5BE,MAAM,EAANA,MAAM;IACNE,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;AACD;AACA;AACA;AACA,IAAMS,MAAM,GAAG,SAATA,MAAMA,CAAIX,MAAM,EAAK;EACzB,OAAOL,OAAO,CAAC;IACbM,GAAG,EAAEH,YAAY,GAAG,iBAAiB;IACrCE,MAAM,EAANA,MAAM;IACNE,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA,IAAMU,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS;EAC1B,OAAOjB,OAAO,CAAC;IACbM,GAAG,EAAEH,YAAY,GAAG,gBAAgB;IACpCI,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA,IAAMW,MAAM,GAAG,SAATA,MAAMA,CAAIb,MAAM,EAAK;EACzB,OAAOL,OAAO,CAAC;IACbM,GAAG,EAAEH,YAAY,GAAG,SAAS;IAC7BE,MAAM,EAANA,MAAM;IACNE,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;AACD,IAAMY,QAAQ,GAAG,SAAXA,QAAQA,CAAId,MAAM,EAACe,QAAQ,EAAG;EAClC,IAAMC,MAAM,GAAG;IACbhB,MAAM,EAACA,MAAM;IACbE,MAAM,EAAE,KAAK;IACbe,QAAQ,EAACjB,MAAM,CAAC,UAAU;EAC5B,CAAC;EACD,OAAOH,aAAa,CAACC,YAAY,GAAG,WAAW,EAACkB,MAAM,EAACD,QAAQ,CAAC;AAClE,CAAC;AACD,IAAMG,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAIlB,MAAM,EAACe,QAAQ,EAAG;EAC3C,IAAMC,MAAM,GAAG;IACbhB,MAAM,EAACA,MAAM;IACbE,MAAM,EAAE,KAAK;IACbe,QAAQ,EAACjB,MAAM,CAAC,UAAU;EAC5B,CAAC;EACD,OAAOH,aAAa,CAACC,YAAY,GAAG,oBAAoB,EAACkB,MAAM,EAACD,QAAQ,CAAC;AAC3E,CAAC;AACD,eAAe;EACbhB,QAAQ,EAARA,QAAQ;EACRI,eAAe,EAAfA,eAAe;EACfO,KAAK,EAALA,KAAK;EACLG,MAAM,EAANA,MAAM;EACNT,kBAAkB,EAAlBA,kBAAkB;EAClBK,iBAAiB,EAAjBA,iBAAiB;EACjBJ,iBAAiB,EAAjBA,iBAAiB;EACjBC,oBAAoB,EAApBA,oBAAoB;EACpBC,mBAAmB,EAAnBA,mBAAmB;EACnBC,mBAAmB,EAAnBA,mBAAmB;EACnBG,MAAM,EAANA,MAAM;EACNC,aAAa,EAAbA,aAAa;EACbE,QAAQ,EAARA,QAAQ;EACRI,iBAAiB,EAAjBA;AACF,CAAC"}]}