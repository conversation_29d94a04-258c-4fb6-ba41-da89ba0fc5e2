{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\basf\\shop\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\basf\\shop\\index.vue", "mtime": 1752737748516}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings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file": "index.vue", "sourceRoot": "src/view/module/basf/shop", "sourcesContent": ["<template>\r\n  <div>\r\n    <Card class=\"shopManage\" :shadow=\"true\">\r\n      <Form ref=\"searchForm\" class=\"searchForm\" :model=\"pageInfo\" inline @submit.native.prevent>\r\n        <FormItem prop=\"platformId\">\r\n          <Select type=\"text\" v-model=\"pageInfo.platformId\" placeholder=\"平台名称\" @on-change=\"platformChange\" style=\"width:160px\" >\r\n            <Option v-for=\"(item, index) in platformList\" :value=\"item.id\" :key=\"index\" >{{ item.name }}</Option >\r\n          </Select>\r\n        </FormItem>\r\n        <FormItem prop=\"siteId\">\r\n          <Select v-model=\"pageInfo.siteId\" placeholder=\"站点名称\" style=\"width:160px\" >\r\n            <Option v-for=\"(item, index) in siteList\" :value=\"item.id\" :key=\"index\" >{{ item.name }}</Option >\r\n          </Select>\r\n        </FormItem>\r\n        <FormItem prop=\"name\">\r\n          <Input type=\"text\" placeholder=\"店铺名称\" v-model=\"pageInfo.name\" />\r\n        </FormItem>\r\n        <FormItem prop=\"aliaName\">\r\n          <Input type=\"text\" placeholder=\"店铺编号\" v-model=\"pageInfo.aliaName\"/>\r\n        </FormItem>\r\n        <FormItem prop=\"status\">\r\n          <Select v-model=\"pageInfo.status\" placeholder=\"店铺状态\" style=\"width:160px\">\r\n            <Option v-for=\"v in statusOps\" :value=\"v.key\" :key=\"v.key\">{{v.name }}</Option>\r\n          </Select>\r\n        </FormItem>\r\n        <FormItem prop=\"relate\">\r\n          <Select v-model=\"pageInfo.relate\" placeholder=\"关联领星\" style=\"width:160px\">\r\n            <Option v-for=\"v in relateOps\" :value=\"v.key\" :key=\"v.key\">{{v.name }}</Option>\r\n          </Select>\r\n        </FormItem>\r\n        <FormItem class=\"rightBtn\" :label-width=\"20\">\r\n          <Button type=\"primary\" @click=\"handleSearch(1)\">查询</Button>&nbsp;\r\n          <Button @click=\"handleResetForm('searchForm')\">重置</Button>\r\n        </FormItem>\r\n      </Form>\r\n      <div class=\"search-con search-con-top\" style=\"padding-bottom: 10px\">\r\n        <Button type=\"primary\" @click=\"handleModal()\" v-if=\"hasAuthority('shopAdd')\">添加</Button>\r\n        <Button @click=\"syncShop()\" style=\"margin-left: 15px\" :loading=\"loading\" v-if=\"hasAuthority('shopSync')\">同步</Button>\r\n      </div>\r\n      <Table :border=\"true\" ref=\"autoTableRef\" :max-height=\"autoTableHeight($refs.autoTableRef)\" :columns=\"columns\"\r\n             :data=\"data\" :loading=\"loading\">\r\n        <template v-slot:status=\"{ row }\">\r\n          <Badge v-for=\"v in statusOps\" :text=\"v.name\" v-if=\"v.key === row.status\"\r\n                 :status=\"v.key === 0?'success':'warning'\" v-bind:key=\"v.key\"></Badge>\r\n        </template>\r\n        <template v-slot:action=\"{ row }\">\r\n          <a v-if=\"hasAuthority('shopEdit')\" @click=\"handleModal(row)\">编辑</a>&nbsp;\r\n          <a @click=\"handleClick('view',row)\">查看</a>&nbsp;\r\n          <a v-if=\"hasAuthority('shopEdit')\" @click=\"handleClick('remove',row)\">{{row.status === 0?\"停用\":(row.status === 1?\"启用\":\"解锁\")}}</a>\r\n          <!--<a @click=\"handleAuth(row)\">AWM授权</a>&nbsp;-->\r\n        </template>\r\n      </Table>\r\n      <Page :total=\"pageInfo.total\" size=\"small\" :current=\"pageInfo.page\" :page-size=\"pageInfo.limit\" :show-elevator=\"true\"\r\n            :show-sizer=\"true\" :show-total=\"true\" @on-change=\"handlePage\" @on-page-size-change='handlePageSize'></Page>\r\n    </Card>\r\n    <Modal v-model=\"modalVisible\" :title=\"modalTitle\" @on-cancel=\"handleReset\" width=\"765px\" class-name=\"shopManageEditModal\">\r\n      <Form ref=\"form\" :model=\"formItem\" :rules=\"formItemRules\" :label-width=\"100\">\r\n        <FormItem label=\"平台名称\" prop=\"platformId\">\r\n          <Select v-model=\"formItem.platformId\" @on-change=\"change\" :disabled=\"actionType==='view'\">\r\n            <Option v-for=\"(item, index) in platformList\" :value=\"item.id\" :key=\"index\" >{{ item.name }}</Option >\r\n          </Select>\r\n        </FormItem>\r\n        <FormItem label=\"站点名称\" prop=\"siteId\" :rules=\"siteRule\">\r\n          <Select v-model=\"formItem.siteId\" :disabled=\"actionType==='view'\">\r\n            <Option v-for=\"(item, index) in siteList\" :value=\"item.id\" :key=\"index\" >{{ item.name }}</Option >\r\n          </Select>\r\n        </FormItem>\r\n        <FormItem label=\"店铺名称\" prop=\"name\">\r\n          <Input v-model=\"formItem.name\" placeholder=\"请输入内容\"></Input>\r\n        </FormItem>\r\n        <FormItem label=\"店铺编号\" prop=\"aliaName\">\r\n          <Input v-model=\"formItem.aliaName\" placeholder=\"请输入内容\"></Input>\r\n        </FormItem>\r\n        <FormItem label=\"客户编号\" prop=\"erpCustNo\">\r\n          <Input v-model=\"formItem.erpCustNo\" placeholder=\"请输入内容\"></Input>\r\n        </FormItem>\r\n        <FormItem prop=\"managed_user\" label=\"店铺负责人\" class=\"sellerSelectItem\">\r\n          <Select type=\"text\" v-model=\"formItem.managedUser\" :filterable=\"true\" style=\"width: 555px\" placeholder=\"请选择\" :transfer=\"true\" >\r\n            <Option v-for=\"item in usersOptions\" :value=\"item.userId\" :key=\"item.userId\" >{{ item.nickName }}</Option >\r\n          </Select>\r\n          <Button type=\"dashed\" @click=\"openPerson\" style=\"margin-left: 3px\" size=\"default\" >选择</Button >\r\n          <div class=\"closeIcon\" v-show=\"!!formItem.managedUser\" @click=\"() => (formItem.managedUser = undefined)\" >\r\n            <Icon type=\"md-close\" size=\"14\" />\r\n          </div>\r\n          <person-select :visible=\"personVisible\" :onCancel=\"() => (personVisible = false)\"\r\n                         @setPerson=\"arr => (formItem.managedUser = arr.map(v => v.id)[0])\"\r\n                         @setSelectInfo=\"setSelectInfo\" ref=\"personSelectRef\" groupName=\"shopmanage_operateuser_config\" :isQuery=\"true\" />\r\n        </FormItem>\r\n        <FormItem label=\"手机号码\" prop=\"phone\">\r\n          <Input v-model=\"formItem.phone\" maxlength=\"11\" width=\"100%\" />\r\n        </FormItem>\r\n        <FormItem label=\"状态\" prop=\"remark\">\r\n          <RadioGroup v-model=\"formItem.status\" type=\"button\">\r\n            <Radio v-for=\"v in statusOps\" :label=\"v.key\" v-if=\"v.key !== -1\" v-bind:key=\"v.key\">{{ v.name }}</Radio>\r\n          </RadioGroup>\r\n        </FormItem>\r\n        <FormItem label=\"备注\" prop=\"remark\">\r\n          <Input v-model=\"formItem.remark\" type=\"textarea\" placeholder=\"请输入内容\"></Input>\r\n        </FormItem>\r\n      </Form>\r\n      <div class=\"drawer-footer\">\r\n        <Button type=\"default\" @click=\"handleReset\">取消</Button>&nbsp;\r\n        <Button type=\"primary\" @click=\"handleSubmit\" :loading=\"saving\" >保存</Button >\r\n      </div>\r\n    </Modal>\r\n    <Modal v-model=\"modalViewVisible\" :title=\"modalTitle\" @on-cancel=\"handleReset\" width=\"765px\">\r\n      <Form ref=\"viewForm\" :model=\"formItem\" :label-width=\"100\">\r\n        <FormItem label=\"平台名称\" prop=\"name\">\r\n          <Input v-model=\"formItem.platform\" placeholder=\"\" :readonly=\"true\"></Input>\r\n        </FormItem>\r\n        <FormItem label=\"站点名称\" prop=\"siteName\">\r\n          <Input v-model=\"formItem.site\" placeholder=\"\" :readonly=\"true\"></Input>\r\n        </FormItem>\r\n        <FormItem label=\"店铺名称\" prop=\"name\">\r\n          <Input v-model=\"formItem.name\" placeholder=\"\" :readonly=\"true\"></Input>\r\n        </FormItem>\r\n        <FormItem label=\"店铺编号\" prop=\"aliaName\">\r\n          <Input v-model=\"formItem.aliaName\" placeholder=\"请输入内容\" :readonly=\"true\"></Input>\r\n        </FormItem>\r\n        <FormItem label=\"客户编号\" prop=\"erpCustNo\">\r\n          <Input v-model=\"formItem.erpCustNo\" placeholder=\"请输入内容\" :readonly=\"true\"></Input>\r\n        </FormItem>\r\n        <FormItem label=\"店铺负责人\" prop=\"managedUserName\">\r\n          <Input v-model=\"formItem.managedUserName\" placeholder=\"请输入内容\" :readonly=\"true\"></Input>\r\n        </FormItem>\r\n        <FormItem label=\"手机号码\" prop=\"phone\">\r\n          <Input v-model=\"formItem.phone\" placeholder=\"\" :readonly=\"true\" />\r\n        </FormItem>\r\n        <FormItem label=\"状态\">\r\n          <RadioGroup v-model=\"formItem.status\" type=\"button\" :readonly=\"true\">\r\n            <Radio v-for=\"v in statusOps\" :label=\"v.key\" v-if=\"v.key !== -1\" v-bind:key=\"v.key\">{{ v.name }}</Radio>\r\n          </RadioGroup>\r\n        </FormItem>\r\n        <FormItem label=\"备注\" prop=\"remark\">\r\n          <Input v-model=\"formItem.remark\" type=\"textarea\" :readonly=\"true\"></Input>\r\n        </FormItem>\r\n      </Form>\r\n      <div slot=\"footer\"></div>\r\n    </Modal>\r\n  </div>\r\n</template>\r\n<script>\r\n\r\nimport personSelect from \"@/components/person-select-radio/index.vue\";\r\nimport Shop from \"@/api/basf/shop\";\r\nimport Platform from \"@/api/basf/platform\";\r\nimport Site from \"@/api/basf/site\";\r\nimport {autoTableHeight} from \"@/libs/tools.js\";\r\nimport Common from '@/api/basic/common'\r\n\r\nexport default {\r\n  name: \"shopList\",\r\n  components: {\r\n    personSelect\r\n  },\r\n  data() {\r\n    return {\r\n      autoTableHeight,\r\n      statusOps: Common.userStatusOps,\r\n      relateOps: Common.relateOps,\r\n      actionType:'view',\r\n      personVisible: false,\r\n      loading: false,\r\n      saving: false,\r\n      modalVisible: false,\r\n      modalViewVisible: false,\r\n      shopNameShow: false,\r\n      siteRule: { required: false },\r\n      readName: false,\r\n      modalTitle: \"\",\r\n      platformList: [],\r\n      selectTreeData: [],\r\n      siteList: [],\r\n      selectPersons: [],\r\n      pageInfo: {\r\n        total: 0,\r\n        page: 1,\r\n        limit: 10,\r\n        status: -1,\r\n        relate: -1,\r\n        platformId:-1,\r\n        siteId:-1,\r\n        name: \"\",\r\n        aliaName: \"\"\r\n      },\r\n      formItemRules: {\r\n        name: [\r\n          { required: true, message: \"店铺名称不能为空\", trigger: \"blur\" }\r\n        ],\r\n        platformId: [\r\n          { required: true, message: \"所属平台不能为空\", trigger: \"blur\" }\r\n        ],\r\n        aliaName: [\r\n          { required: true, message: \"店铺编号不能为空\", trigger: \"blur\" }\r\n        ],\r\n        departmentId: [{ required: true, message: \"部门不能为空\" }]\r\n      },\r\n      formItem: {\r\n        id: \"\",\r\n        platformId: \"\",\r\n        platform: \"\",\r\n        siteId: \"\",\r\n        site: \"\",\r\n        name: \"\",\r\n        aliaName: \"\",\r\n        erpCustNo: \"\",\r\n        managedUserName:\"\",\r\n        phone:\"\",\r\n        status: 0,\r\n        remark: \"\"\r\n      },\r\n      columns: [\r\n        {\r\n          type: \"selection\",\r\n          maxWidth: 40\r\n        },\r\n        {\r\n          title: \"平台名称\",\r\n          key: \"platform\",\r\n          minWidth: 120,\r\n          align: \"center\"\r\n        },\r\n        {\r\n          title: \"站点名称\",\r\n          key: \"site\",\r\n          minWidth: 100,\r\n          align: \"center\"\r\n        },\r\n        {\r\n          title: \"店铺名称\",\r\n          key: \"name\",\r\n          minWidth: 150,\r\n          align: \"center\"\r\n        },\r\n        {\r\n          title: \"店铺编号\",\r\n          key: \"aliaName\",\r\n          minWidth: 200,\r\n          align: \"center\"\r\n        },\r\n        {\r\n          title: \"客户编号\",\r\n          key: \"erpCustNo\",\r\n          minWidth: 150,\r\n          align: \"center\"\r\n        },\r\n        {\r\n          title: \"店铺负责人\",\r\n          key: \"managedUserName\",\r\n          width: 110,\r\n          align: \"center\",\r\n          render: (_, { row }) => (\r\n              <div v-copytext={row.managedUserName}>{row.managedUserName}</div>\r\n          )\r\n        },\r\n        {\r\n          title: \"手机号码\",\r\n          key: \"phone\",\r\n          minWidth: 160,\r\n          align: \"center\"\r\n        },\r\n        {\r\n          title: \"状态\",\r\n          key: \"status\",\r\n          slot: \"status\",\r\n          minWidth: 100,\r\n          align: \"center\"\r\n        },\r\n        {\r\n          title: \"关联领星\",\r\n          key: \"sid\",\r\n          minWidth: 160,\r\n          align: \"center\",\r\n          render:(_, { row }) => (\r\n              <span>{row.sid>0?\"已关联\":\"未关联\"}</span>\r\n          )\r\n        },\r\n        {\r\n          title: \"新增修改时间\",\r\n          key: \"updateTime\",\r\n          sortable: \"custom\",\r\n          width: 140,\r\n          align: \"center\",\r\n          render: (_, { row }) => (\r\n              <div>\r\n                <p title=\"新增时间\">{row.createTime}</p>\r\n                <p title=\"修改时间\">{row.updateTime}</p>\r\n              </div>\r\n          )\r\n        },\r\n        {\r\n          title: \"操作\",\r\n          slot: \"action\",\r\n          fixed: \"right\",\r\n          minWidth: 150,\r\n          align: \"center\"\r\n        }\r\n      ],\r\n      data: [],\r\n      usersOptions: [],\r\n      allDepartments: []\r\n    };\r\n  },\r\n  methods: {\r\n    treeSelectNormalizer(node) {\r\n      return {\r\n        id: node.id,\r\n        label: node.departmentName,\r\n        children: node.children\r\n      };\r\n    },\r\n    onPersonCancel() {\r\n      this.personVisible = false;\r\n    },\r\n    // 把选中的人员设置在父组件中\r\n    setPerson(personArr = []) {\r\n      let personIds = [];\r\n      personArr.map(item => {\r\n        personIds.push(item.id);\r\n      });\r\n      if (!this.ArrayIsEqual(this.selectPersons, personIds)) {\r\n        Shop.addShopUsers({\r\n          id: this.id,\r\n          userIds: personIds.join(\",\")\r\n        }).then(res => {\r\n          if (res['code'] === 0) {\r\n            this.$Message.success(\"授权成功\");\r\n          } else {\r\n            this.$Message.error(\"授权失败\");\r\n          }\r\n        });\r\n      }\r\n    },\r\n    ArrayIsEqual(arr1, arr2) {\r\n      //判断2个数组是否相等\r\n      if (arr1 === arr2) {\r\n        //如果2个数组对应的指针相同，那么肯定相等，同时也对比一下类型\r\n        return true;\r\n      } else {\r\n        if (arr1.length !== arr2.length) {\r\n          return false;\r\n        } else {\r\n          //长度相同\r\n          for (let i in arr1) {\r\n            //循环遍历对比每个位置的元素\r\n            if (arr1[i] !== arr2[i]) {\r\n              //只要出现一次不相等，那么2个数组就不相等\r\n              return false;\r\n            }\r\n          } //for循环完成，没有出现不相等的情况，那么2个数组相等\r\n          return true;\r\n        }\r\n      }\r\n    },\r\n    syncShop(){\r\n      this.loading = true;\r\n      Shop.syncShop().then((res)=>{\r\n        if(res['code'] ===0){\r\n          this.$Message.success(\"同步成功\");\r\n          this.handleSearch();\r\n        }\r\n      }).finally(()=>{this.loading = false})\r\n    },\r\n    handleModal(data) {\r\n      if (data) {\r\n        this.readName = true;\r\n        this.shopNameShow = true;\r\n        this.modalTitle = \"编辑\";\r\n        this.actionType = \"edit\";\r\n        this.formItem = Object.assign({}, this.formItem, data);\r\n      } else {\r\n        this.modalTitle = \"添加\";\r\n        this.actionType = \"add\";\r\n      }\r\n      this.modalVisible = true;\r\n    },\r\n    handleView(data) {\r\n      this.formItem = data;\r\n      this.modalTitle = \"查看店铺\";\r\n      this.actionType = \"view\";\r\n      this.formItem = Object.assign({}, this.formItem, data);\r\n      this.modalViewVisible = true;\r\n    },\r\n    handleReset() {\r\n      this.formItem = {\r\n        id: \"\",\r\n        platformId: \"\",\r\n        platform: \"\",\r\n        siteId: \"\",\r\n        site: \"\",\r\n        name: \"\",\r\n        aliaName: \"\",\r\n        erpCustNo: \"\",\r\n        managedUserName:\"\",\r\n        phone:\"\",\r\n        status: 0,\r\n        remark: \"\"\r\n      };\r\n      let form = this.$refs[\"form\"];\r\n      form.resetFields();\r\n      this.modalVisible = false;\r\n      this.shopNameShow = false;\r\n      this.readName = false;\r\n      this.siteRule = { required: false };\r\n      this.saving = false;\r\n    },\r\n\r\n    getDepartNames() {\r\n      const { allDepartments, formItem } = this;\r\n      const departmentIds = formItem.departmentId || [];\r\n      const results = [];\r\n      departmentIds.forEach(id => {\r\n        const departmentName = (allDepartments.find(v => v.id === id) || {})\r\n            .departmentName;\r\n        if (departmentName) results.push(departmentName);\r\n      });\r\n      return results;\r\n    },\r\n    handleSubmit() {\r\n      let form = this.$refs[\"form\"];\r\n      const { formItem, usersOptions } = this;\r\n      form.validate(valid => {\r\n        const params = {\r\n          ...this.formItem,\r\n          departmentId: (this.formItem.departmentId || []).toString(),\r\n          departmentName: this.getDepartNames().toString(),\r\n          name: formItem.name ? formItem.name.trim() : formItem.name,\r\n          operateUser: (\r\n              usersOptions.find(v => v.userId === formItem.managedUser) || {}\r\n          ).nickName\r\n        };\r\n        if (valid) {\r\n          this.saving = true;\r\n          if (this.formItem.id) {\r\n            Shop.edit(params).then(res => {\r\n              if (res['code'] === 0) {\r\n                this.$Message.success(\"保存成功\");\r\n                this.handleReset()\r\n                this.handleSearch();\r\n              }}).finally(() => {this.saving = false;});\r\n          } else {\r\n            Shop.add(params).then(res => {\r\n              if (res['code'] === 0) {\r\n                this.handleReset();\r\n                this.handleSearch();\r\n                this.$Message.success(\"保存成功\");\r\n              }}).finally(() => {this.saving = false;});\r\n          }\r\n        }\r\n      });\r\n    },\r\n    handleResetForm(form) {\r\n      if (this.$refs[form]) this.$refs[form].resetFields();\r\n      this.pageInfo.deptId = undefined;\r\n    },\r\n    handleSearch(page) {\r\n      const { pageInfo } = this;\r\n      if (page) {\r\n        pageInfo.page = page;\r\n      }\r\n      this.loading = true;\r\n      const params = {\r\n        ...pageInfo,\r\n        total: undefined,\r\n        deptId: pageInfo.deptId\r\n            ? pageInfo.deptId.toString() || undefined\r\n            : undefined\r\n      };\r\n      Shop.listPage(params)\r\n          .then(res => {\r\n            this.data = res.data.records;\r\n            this.pageInfo.total = parseInt(res.data.total);\r\n          })\r\n          .finally(() => {\r\n            this.loading = false;\r\n          });\r\n    },\r\n    handlePage(current) {\r\n      this.pageInfo.page = current;\r\n      this.handleSearch();\r\n    },\r\n    handlePageSize(size) {\r\n      this.pageInfo.limit = size;\r\n      this.handleSearch();\r\n    },\r\n    handleRemove(data) {\r\n      let modal = this.$Modal;\r\n      let title = data.status === 0?\"停用\":(data.status === 1?\"启用\":\"解锁\");\r\n      modal.confirm({\r\n        title: \"确定\"+title+\"吗？\",\r\n        onOk: () => {\r\n          Shop.remove(data.id).then(res => {\r\n            if (res['code'] === 0) {\r\n              this.pageInfo.page = 1;\r\n              this.$Message.success(title+\"成功\");\r\n            }\r\n            this.handleSearch();\r\n          });\r\n        }\r\n      });\r\n    },\r\n    handlePlatform() {\r\n      Platform.getAll().then(res => {\r\n        this.platformList = res.data;\r\n        this.platformList.unshift({ id: -1,  name: \"全部\" });\r\n      });\r\n    },\r\n    handleSite() {\r\n      Site.getAll().then(res => {\r\n        this.siteList = res.data;\r\n        this.siteList.unshift({ id: -1, name: \"全部\" });\r\n      });\r\n    },\r\n    change(val) {\r\n      if (val === \"1\") {\r\n        this.siteRule = {\r\n          required: true,\r\n          message: \"所属站点不能为空\",\r\n          trigger: \"blur\"\r\n        };\r\n      } else {\r\n        this.siteRule = { required: false };\r\n      }\r\n      if (val != null) {\r\n        this.formItem.siteId = \"\";\r\n        Site.getByPlatformId(val).then(res => {\r\n          this.siteList = res.data;\r\n          if (val !== \"1\") {\r\n            this.siteList.unshift({ id: -1, name: \"全部\" });\r\n          }\r\n        });\r\n      }\r\n    },\r\n    //主表格排序查询\r\n    onSortChange({order }) {\r\n      if (order && order !== \"normal\") {\r\n        this.pageInfo.sort = \"c_update_time\";\r\n        this.pageInfo.order = order;\r\n      } else {\r\n        this.pageInfo.sort = null;\r\n        this.pageInfo.order = null;\r\n      }\r\n      this.handleSearch();\r\n    },\r\n    platformChange(pfId) {\r\n      if (pfId != null) {\r\n        Site.getByPlatformId(pfId).then(res => {\r\n          this.siteList = res.data;\r\n          this.siteList.unshift({ id: -1, name: \"全部\" });\r\n          this.pageInfo.siteId = -1;\r\n        });\r\n      }\r\n    },\r\n    handleClick(name, row) {\r\n      switch (name) {\r\n        case \"remove\":\r\n          this.handleRemove(row);\r\n          break;\r\n        case \"view\":\r\n          this.handleView(row);\r\n          break;\r\n      }\r\n    },\r\n    // 人员选择相关\r\n    setSelectInfo(info = {}) {\r\n      this.usersOptions = info.personArr || [];\r\n    },\r\n    openPerson() {\r\n      //打开人员选择\r\n      const { personSelectRef } = this.$refs;\r\n      const { usersOptions, formItem } = this;\r\n      const selectedId = formItem.managedUser || undefined;\r\n      if (personSelectRef)\r\n        personSelectRef.setDefault(usersOptions\r\n            .filter(v => selectedId === v.userId)\r\n            .map(v => ({ name: v.nickName, id: v.userId }))\r\n        ); //给组件设置默认选中\r\n      this.personVisible = true;\r\n    }\r\n  },\r\n  mounted: function() {\r\n    this.handleSearch();\r\n    this.handlePlatform();\r\n    this.handleSite();\r\n  }\r\n};\r\n</script>\r\n<style lang=\"less\">\r\n.shopManage {\r\n  .searchForm {\r\n    position: relative;\r\n    padding-right: 200px;\r\n    .rightBtn {\r\n      position: absolute;\r\n      top: 0;\r\n      right: 0;\r\n    }\r\n  }\r\n  .ivu-table-cell {\r\n    padding-left: 8px;\r\n    padding-right: 8px;\r\n  }\r\n}\r\n.shopManageEditModal {\r\n  .ivu-modal {\r\n    top: 30px;\r\n    .ivu-modal-body {\r\n      max-height: 720px;\r\n      overflow: auto;\r\n    }\r\n  }\r\n  .sellerSelectItem {\r\n    position: relative;\r\n    .ivu-select-multiple .ivu-tag span:not(.ivu-select-max-tag) {\r\n      max-width: 45px;\r\n    }\r\n    .closeIcon {\r\n      color: #ccc;\r\n      width: 16px;\r\n      position: absolute;\r\n      top: 0;\r\n      right: 80px;\r\n      font-size: 9px;\r\n      &:hover {\r\n        cursor: pointer;\r\n        color: #e53935;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}