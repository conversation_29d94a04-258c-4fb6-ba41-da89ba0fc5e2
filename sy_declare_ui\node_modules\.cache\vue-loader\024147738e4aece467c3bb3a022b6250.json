{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\base\\dictionary\\index.vue?vue&type=style&index=0&id=42690456&lang=less&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\base\\dictionary\\index.vue", "mtime": 1752737748510}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1752744819993}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQoNCi5kaWNMaXN0TW9kYWwgew0KICAuaXZ1LW1vZGFsLWJvZHkgew0KICAgIG1pbi1oZWlnaHQ6IDQ1MHB4Ow0KDQogICAgLml2dS10YWJsZS1jZWxsIHsNCiAgICAgIHBhZGRpbmctbGVmdDogNHB4Ow0KICAgICAgcGFkZGluZy1yaWdodDogNHB4Ow0KICAgIH0NCiAgfQ0KfQ0KDQouZGljTGlzdEZvcm0gew0KICBwYWRkaW5nLXJpZ2h0OiAyNXB4Ow0KDQogIHRleHRhcmVhIHsNCiAgICByZXNpemU6IG5vbmU7DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;AA0fA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/view/module/base/dictionary", "sourcesContent": ["<template>\r\n  <div>\r\n    <Card :shadow=\"true\">\r\n      <Form ref=\"formSearch1\" class=\"searchForm\" :model=\"formQuery1\" inline\r\n            @keydown.enter.native=\"handleSearch('formSearch1')\">\r\n        <FormItem prop=\"name\">\r\n          <Input type=\"text\" v-model=\"formQuery1.name\" placeholder=\"请输入字典名称\"/>\r\n        </FormItem>\r\n        <FormItem prop=\"code\">\r\n          <Input type=\"text\" v-model=\"formQuery1.code \" placeholder=\"请输入字典编号\"/>\r\n        </FormItem>\r\n        <FormItem prop=\"status\">\r\n          <Select v-model=\"formQuery1.status\" style=\"width: 100px\" placeholder=\"请选择状态\" :clearable=\"false\"\r\n                  :transfer=\"true\">\r\n            <Option v-for=\"v in statusOps\" :value=\"v.key\" :key=\"v.key\">{{v.name }}</Option>\r\n          </Select>\r\n        </FormItem>\r\n        <FormItem>\r\n          <Button type=\"primary\" @click=\"handleSearch('formSearch1')\">查询</Button>&nbsp;\r\n          <Button @click=\"handleResetForm('formSearch1')\">重置</Button>\r\n        </FormItem>\r\n      </Form>\r\n      <div class=\"search-con search-con-top \">\r\n        <Button class=\"search-btn\" type=\"primary\" @click=\"handleModal()\">\r\n          <span>添加</span>\r\n        </Button>\r\n      </div>\r\n      <Table :border=\"true\" ref=\"autoTableRef\" :max-height=\"autoTableHeight($refs.autoTableRef)\" :columns=\"columns\"\r\n             :data=\"data1\" :loading=\"loading1\">\r\n        <template v-slot:status=\"{row}\">\r\n          <Badge v-for=\"v in statusOps\" :text=\"v.name\" v-if=\"v.key === row.status\"\r\n                 :status=\"v.key ===0?'success':'warning'\" v-bind:key=\"v.key\"></Badge>\r\n        </template>\r\n        <template v-slot:action=\"{ row, index }\">\r\n          <a @click=\"handleModal(row)\" v-if=\"hasAuthority('dictionaryEdit')\">编辑</a>&nbsp;\r\n          <a @click=\"handleSet(row)\" v-if=\"hasAuthority('dictionarySet')\">字典配置</a>&nbsp;\r\n          <a @click=\"handleClick('remove',row,'formSearch1')\" v-if=\"hasAuthority('dictionaryDel')\">删除</a>&nbsp;\r\n        </template>\r\n      </Table>\r\n      <Page :total=\"total1\" size=\"small\" :current=\"formQuery1.page\" :page-size=\"formQuery1.limit\" :show-elevator=\"true\"\r\n            :show-sizer=\"true\" :show-total=\"true\" @on-change=\"handlePage1\"\r\n            @on-page-size-change=\"handlePageSize1\"></Page>\r\n    </Card>\r\n    <!-- 数据字典添加与编辑 -->\r\n    <Modal v-model=\"modalVisible1\" :title=\"modalTitle1\" :width=\"500\" @on-cancel=\"handleReset('form1')\">\r\n      <Form ref=\"form1\" :model=\"formItem1\" :rules=\"formItemRules1\" :label-width=\"100\" class=\"dicListForm\">\r\n        <FormItem label=\"字典编号\" prop=\"code\">\r\n          <Input v-model=\"formItem1.code\" :maxlength=\"50\" placeholder=\"请输入内容\"></Input>\r\n        </FormItem>\r\n        <FormItem label=\"字典名称\" prop=\"name\">\r\n          <Input v-model=\"formItem1.name\" :maxlength=\"50\" placeholder=\"请输入内容\"></Input>\r\n        </FormItem>\r\n        <FormItem label=\"描述\" prop=\"description\">\r\n          <Input v-model=\"formItem1.description\" :autosize=\"{minRows: 2,maxRows: 6}\" type=\"textarea\"\r\n                 placeholder=\"请输入\"/>\r\n        </FormItem>\r\n        <FormItem label=\"是否启用\" prop=\"status\">\r\n          <i-switch size=\"large\" v-model=\"formItem1.status\" :true-value=\"0\" :false-value=\"1\">\r\n            <span slot=\"open\">开启</span>\r\n            <span slot=\"close\">关闭</span>\r\n          </i-switch>\r\n        </FormItem>\r\n      </Form>\r\n      <template v-slot:footer=\"\">\r\n        <Button type=\"default\" @click=\"handleReset('form1')\">取消</Button>&nbsp;\r\n        <Button type=\"primary\" @click=\"handleSubmit1()\" :loading=\"saving1\">保存</Button>\r\n      </template>\r\n    </Modal>\r\n    <!-- 配置字典列表 -->\r\n    <Modal v-model=\"modalVisible2\" title=\"字典列表\" :width=\"900\" @on-cancel=\"handleReset('formSearch2')\"\r\n           class=\"dicListModal\">\r\n      <Form ref=\"formSearch2\" :model=\"formQuery2\" inline :label-width=\"40\"\r\n            @keydown.enter.native=\"handleSearch('formSearch2')\">\r\n        <FormItem label=\"名称\" prop=\"name\">\r\n          <Input v-model=\"formQuery2.name\" :maxlength=\"50\" placeholder=\"请输入内容\" style=\"width:200px\"></Input>\r\n        </FormItem>\r\n        <FormItem label=\"状态\" prop=\"status\">\r\n          <Select v-model=\"formQuery2.status\" style=\"width:200px\">\r\n            <Option v-for=\"v in statusOps\" :value=\"v.key\" :key=\"v.key\">{{ v.name }}</Option>\r\n          </Select>\r\n        </FormItem>\r\n        <FormItem>\r\n          <Button type=\"primary\" style=\"margin-right:15px;\" @click=\"handleSearch('formSearch2')\">查询</Button>\r\n          <Button @click=\"handleResetForm('formSearch2')\">重置</Button>\r\n        </FormItem>\r\n      </Form>\r\n      <div class=\"search-con search-con-top\" style=\"margin-top: 8px\">\r\n        <Button class=\"search-btn\" type=\"primary\" @click=\"handleModal2('add')\">\r\n          <span>添加</span>\r\n        </Button>\r\n      </div>\r\n      <Table :columns=\"valueColumns\" :data=\"data2\" :loading=\"loading2\" :border=\"true\" :max-height=\"560\">\r\n        <template v-slot:status=\"{row}\">\r\n          <Badge v-for=\"v in statusOps\" :text=\"v.name\" v-if=\"v.key === row.status\"\r\n                 :status=\"v.key ===0?'success':'warning'\" v-bind:key=\"v.key\"></Badge>\r\n        </template>\r\n        <template v-slot:action=\"{ row, index }\">\r\n          <Button type=\"text\" @click=\"handleModal2('edit',row)\">编辑</Button>\r\n          <Button type=\"text\" @click=\"handleClick('remove',row,'formSearch2')\">删除</Button>\r\n        </template>\r\n      </Table>\r\n      <Page :total=\"total2\" :current=\"formQuery2.page\" :page-size=\"formQuery2.limit\" :show-elevator=\"true\"\r\n            :show-sizer=\"true\" :show-total=\"true\" @on-change=\"handlePage2\"\r\n            @on-page-size-change=\"handlePageSize2\"></Page>\r\n      <template v-slot:footer=\"{}\">\r\n        <Button type=\"default\" @click=\"handleReset('formSearch2')\">关闭</Button>\r\n      </template>\r\n    </Modal>\r\n    <!-- 添加与编辑字典列表 -->\r\n    <Modal v-model=\"modalVisible3\" :title=\"modalTitle2\" :width=\"500\" @on-cancel=\"handleReset('form2')\">\r\n      <Form ref=\"form2\" :model=\"formItem2\" :rules=\"formItemRules2\" :label-width=\"80\" class=\"dicListForm\">\r\n        <FormItem label=\"名称\" prop=\"name\">\r\n          <Input v-model=\"formItem2.name\" :maxlength=\"100\" placeholder=\"请输入内容\"></Input>\r\n        </FormItem>\r\n        <FormItem label=\"数据值\" prop=\"value\">\r\n          <Input v-model=\"formItem2.value\" :maxlength=\"5000\" placeholder=\"请输入内容\"></Input>\r\n        </FormItem>\r\n        <FormItem label=\"描述\" prop=\"description\">\r\n          <Input v-model=\"formItem2.description\" :autosize=\"{minRows: 2,maxRows: 6}\" type=\"textarea\"\r\n                 placeholder=\"请输入\"/>\r\n        </FormItem>\r\n        <FormItem label=\"排序值\" prop=\"sort\">\r\n          <InputNumber :min=\"1\" v-model=\"formItem2.sort\"></InputNumber> &nbsp;<span>值越小越靠前,支持小数</span>\r\n        </FormItem>\r\n        <FormItem label=\"是否启用\" prop=\"status\">\r\n          <i-switch size=\"large\" v-model=\"formItem2.status\" :true-value=\"0\" :false-value=\"1\">\r\n            <span slot=\"open\">开启</span>\r\n            <span slot=\"close\">关闭</span>\r\n          </i-switch>\r\n        </FormItem>\r\n      </Form>\r\n      <template v-slot:footer=\"{}\">\r\n        <Button type=\"default\" @click=\"handleReset('form2')\">关闭</Button>&nbsp;\r\n        <Button type=\"primary\" @click=\"handleSubmit2()\" :loading=\"saving2\" v-if=\"hasAuthority('dictionarySet')\">确定</Button>\r\n      </template>\r\n    </Modal>\r\n  </div>\r\n</template>\r\n<script>\r\nimport Common from '@/api/basic/common'\r\nimport Dictionary from '@/api/base/dictionary'\r\nimport {autoTableHeight} from \"@/libs/tools.js\"\r\n\r\nexport default {\r\n  name: 'dict',\r\n  data() {\r\n    return {\r\n      autoTableHeight,\r\n      statusOps: Common.statusOps,\r\n\r\n      modalTitle1: '添加字典',\r\n      modalTitle2: '添加字典值',\r\n      state1: 'add',//字典操作\r\n      state2: 'add',//字典值操作\r\n      loading1: false,\r\n      loading2: false,\r\n      saving1: false,\r\n      saving2: false,\r\n      modalVisible1: false,//字典新增修改弹框\r\n      modalVisible2: false,//字典明细弹框\r\n      modalVisible3: false,//字典值新增修改弹框\r\n      data1: [],\r\n      data2: [],\r\n      total1: 0,\r\n      total2: 0,\r\n      selectRow: {},\r\n      columns: [\r\n        {\r\n          title: '序号',\r\n          type: 'index',\r\n          width: 80,\r\n          align: 'center'\r\n        },\r\n        {\r\n          title: '字典名称',\r\n          key: 'name',\r\n          align: 'center',\r\n          render: (h, {row}) => {\r\n            return <span v-copytext={row.name}>{row.name}</span>\r\n          },\r\n        },\r\n        {\r\n          title: '字典编号',\r\n          key: 'code',\r\n          align: 'center',\r\n          render: (h, {row}) => {\r\n            return <span v-copytext={row.code}>{row.code}</span>\r\n          },\r\n        },\r\n        {\r\n          title: '描述',\r\n          key: 'description',\r\n          align: 'center'\r\n        },\r\n        {\r\n          title: '状态',\r\n          key: 'status',\r\n          slot: 'status',\r\n          width: 100,\r\n        },\r\n        {\r\n          title: '操作',\r\n          slot: 'action',\r\n          width: 300,\r\n          align: 'center'\r\n        }\r\n      ],\r\n      valueColumns: [\r\n        {\r\n          title: '名称',\r\n          key: 'name',\r\n          align: 'center',\r\n          render: (h, {row}) => {\r\n            return <span v-copytext={row.name}>{row.name}</span>\r\n          },\r\n        },\r\n        {\r\n          title: '数据值',\r\n          key: 'value',\r\n          align: 'center',\r\n          render: (h, {row}) => {\r\n            return <span v-copytext={row.value}>{row.value}</span>\r\n          },\r\n        },\r\n        {\r\n          title: '描述',\r\n          key: 'description',\r\n          align: 'center',\r\n          tooltip: true\r\n        },\r\n        {\r\n          title: '状态',\r\n          key: 'status',\r\n          slot: 'status',\r\n          width: 100,\r\n        },\r\n        {\r\n          title: '操作',\r\n          slot: 'action',\r\n          width: 150,\r\n          align: 'center'\r\n        }\r\n      ],\r\n      formQuery1: {\r\n        name: '',\r\n        code: '',\r\n        status: -1,\r\n        page: 1,\r\n        limit: 10\r\n      },\r\n      formQuery2: {\r\n        parentId: null,\r\n        name: '',\r\n        status: -1,\r\n        page: 1,\r\n        limit: 10\r\n      },\r\n      formItem2: {\r\n        id: '',\r\n        parentId: '',\r\n        name: '',\r\n        value: '',\r\n        description: '',\r\n        status: 0,\r\n        sort: 1\r\n      },\r\n      formItem1: {\r\n        id: '',\r\n        code: '',\r\n        name: '',\r\n        description: '',\r\n        status: 0\r\n      },\r\n      formItemRules1: {\r\n        code: [\r\n          {required: true, message: '字典编号不能为空', trigger: 'blur'}\r\n        ],\r\n        name: [\r\n          {required: true, message: '字典名称不能为空', trigger: 'blur'}\r\n        ]\r\n      },\r\n      formItemRules2: {\r\n        name: [\r\n          {required: true, message: '字典值名称不能为空', trigger: 'blur'}\r\n        ],\r\n        value: [\r\n          {required: true, message: '字典值名称对应的值不能为空', trigger: 'blur'}\r\n        ]\r\n      },\r\n    }\r\n  },\r\n  methods: {\r\n    /**\r\n     * 数据字典添加与编辑\r\n     */\r\n    handleModal(data) {\r\n      if (data) {\r\n        this.state1 = 'edit';\r\n        this.modalTitle1 = '编辑字典';\r\n        this.formItem1 = Object.assign({}, this.formItem1, data);\r\n      } else {\r\n        this.state1 = 'add';\r\n        this.modalTitle1 = '添加字典';\r\n        this.formItem1 = {};\r\n      }\r\n      this.modalVisible1 = true;\r\n    },\r\n    /**\r\n     * 数据字典值添加与编辑\r\n     */\r\n    handleModal2(action,data) {\r\n      this.state2 = action;\r\n      if (this.state2 === 'edit') {\r\n        this.modalTitle2 = '编辑字典值';\r\n        this.formItem2 = Object.assign({}, this.formItem2, data);\r\n      } else {\r\n        this.modalTitle2 = '添加字典值';\r\n        this.formItem2 = {id: '',\r\n          parentId: '',\r\n          name: '',\r\n          value: '',\r\n          description: '',\r\n          status: 0,\r\n          sort: 1};\r\n      }\r\n      this.formItem2.parentId = this.selectRow.id\r\n      this.modalVisible3 = true;\r\n    },\r\n    /**\r\n     * 查询，重置\r\n     */\r\n    handleSearch(name) {\r\n      if (name === 'formSearch1') {\r\n        this.loading1 = true;\r\n        Dictionary.listPage(this.formQuery1).then(res => {\r\n          if (res[\"code\"] === 0) {\r\n            this.total1 = parseInt(res.data.total);\r\n            this.data1 = res.data.records;\r\n          }\r\n        }).finally(() => {\r\n          this.loading1 = false\r\n        });\r\n      } else {\r\n        this.loading2 = true;\r\n        this.formQuery2.parentId = this.selectRow.id;\r\n        Dictionary.listValuePage(this.formQuery2).then(res => {\r\n          if (res[\"code\"] === 0) {\r\n            this.total2 = parseInt(res.data.total);\r\n            this.data2 = res.data.records;\r\n          }\r\n        }).finally(() => {\r\n          this.loading2 = false\r\n        });\r\n      }\r\n    },\r\n    handleSet(row) {\r\n      this.modalVisible2 = true\r\n      this.selectRow = row\r\n      this.handleResetForm(\"formSearch2\");\r\n    },\r\n    handleResetForm(name) {\r\n      if(name === \"formSearch1\"){\r\n        this.$refs[\"formSearch1\"].resetFields();\r\n      }else{\r\n        this.$refs[\"formSearch2\"].resetFields();\r\n      }\r\n      this.handleSearch(name, 1, 10);\r\n    },\r\n    handleRemove(data, type) {\r\n      this.$Modal.confirm({\r\n        title: '提示！',\r\n        content: '你确定要删除这条数据吗',\r\n        onOk: () => {\r\n          if (type === 'formSearch1') {\r\n            Dictionary.remove(data.id).then(res => {\r\n              if (res[\"code\"] === 0) {\r\n                this.$Message.success('删除成功!');\r\n                this.handleSearch('formSearch1')\r\n              }\r\n            });\r\n          } else {\r\n            Dictionary.removeValue(data.id).then(res => {\r\n              if (res[\"code\"] === 0) {\r\n                this.$Message.success('删除成功!');\r\n                this.handleSearch('formSearch2')\r\n              }\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    handleClick(name,row, type) {\r\n      switch (name) {\r\n        case 'remove':\r\n          this.handleRemove(row, type)\r\n          break\r\n      }\r\n    },\r\n    handleSubmit1() {\r\n      this.saving1 = true;\r\n      this.$refs['form1'].validate((valid) => {\r\n        if (valid) {\r\n          if (this.state1 === 'add') {\r\n            Dictionary.add(this.formItem1).then(res => {\r\n              if (res[\"code\"] === 0) {\r\n                this.handleReset(\"form1\");\r\n                this.$Message.success('保存成功!');\r\n                this.handleSearch(\"formSearch1\")\r\n              }\r\n            }).finally(() => {\r\n              this.saving1 = false\r\n            })\r\n          } else {\r\n            Dictionary.edit(this.formItem1).then(res => {\r\n              if (res[\"code\"] === 0) {\r\n                this.handleReset(\"form1\");\r\n                this.$Message.success('编辑成功!');\r\n                this.handleSearch(\"formSearch1\")\r\n              }\r\n            }).finally(() => {\r\n              this.saving1 = false\r\n            })\r\n          }\r\n        } else {\r\n          this.saving1 = false\r\n        }\r\n      });\r\n    },\r\n    handleSubmit2() {\r\n      this.saving2 = true;\r\n      this.$refs['form2'].validate((valid) => {\r\n        if (valid) {\r\n          if (this.state2 === 'add') {\r\n            Dictionary.addValue(this.formItem2).then(res => {\r\n              if (res[\"code\"] === 0) {\r\n                this.handleReset(\"form2\");\r\n                this.$Message.success('保存成功!');\r\n                this.handleSearch(\"formSearch2\")\r\n              }\r\n            }).finally(() => {\r\n              this.saving2 = false\r\n            })\r\n          } else {\r\n            Dictionary.editValue(this.formItem2).then(res => {\r\n              if (res[\"code\"] === 0) {\r\n                this.handleReset(\"form2\");\r\n                this.$Message.success('保存成功!');\r\n                this.handleSearch(\"formSearch2\")\r\n              }\r\n            }).finally(() => {\r\n              this.saving2 = false\r\n            })\r\n          }\r\n        } else {\r\n          this.saving2 = false\r\n        }\r\n      });\r\n    },\r\n\r\n    handleReset(name) {\r\n      if (name === 'form1') {\r\n        this.modalVisible1 = false;\r\n        this.saving1 = false\r\n        this.$refs['form1'].resetFields();\r\n      } else if (name === 'form2') {\r\n        this.saving3 = false\r\n        this.modalVisible3 = false;\r\n        this.$refs['form2'].resetFields();\r\n      } else if (name === 'formSearch2') { // name=formQuery2\r\n        this.modalVisible2 = false;\r\n        this.saving2 = false\r\n        this.$refs['formSearch2'].resetFields();\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 分页\r\n     */\r\n    handlePage1(page) {\r\n      this.formQuery1.page = page;\r\n      this.handleSearch('formSearch1')\r\n    },\r\n    handlePageSize1(size) {\r\n      this.formQuery1.limit = size;\r\n      this.handleSearch('formSearch1')\r\n    },\r\n    /**\r\n     * 分页\r\n     */\r\n    handlePage2(page) {\r\n      this.formQuery2.page = page;\r\n      this.handleSearch('formSearch2')\r\n    },\r\n    handlePageSize2(size) {\r\n      this.formQuery2.limit = size;\r\n      this.handleSearch('formSearch2')\r\n    },\r\n  },\r\n  mounted() {\r\n    this.handlePage1(1)\r\n  }\r\n\r\n}\r\n</script>\r\n<style lang=\"less\">\r\n\r\n.dicListModal {\r\n  .ivu-modal-body {\r\n    min-height: 450px;\r\n\r\n    .ivu-table-cell {\r\n      padding-left: 4px;\r\n      padding-right: 4px;\r\n    }\r\n  }\r\n}\r\n\r\n.dicListForm {\r\n  padding-right: 25px;\r\n\r\n  textarea {\r\n    resize: none;\r\n  }\r\n}\r\n</style>\r\n\r\n"]}]}