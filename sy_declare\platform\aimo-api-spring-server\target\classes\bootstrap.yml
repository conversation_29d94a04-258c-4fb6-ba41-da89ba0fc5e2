server:
  port: 8886
  servlet:
    context-path: /api-v1.0
spring:
  mvc:
    favicon:
      enabled: false
  application:
    name: aimo-api-spring-server
  cloud:
    #手动配置Bus id,
    bus:
      id: ${spring.application.name}:${server.port}
    gateway:
      discovery:
        locator:
          #表明gateway开启服务注d册和发现的功能，并且spring cloud gateway自动根据服务发现为每一个服务创建了一个router，# 这个router将以服务名开头的请求路径转发到对应的服务
          enabled: false
          #将请求路径上的服务名配置为小写（因为服务注册的时候，向注册中心注册时将服务名转成大写的了,比如以/service-hi/*的请求路径被路由转发到服务名为service-hi的服务上
          lowerCaseServiceId: true
    nacos:
      config:
        enabled: true
        username: nacos
        password: sy@nacos2022
        file-extension: properties
        shared-configs[0]:
          data-id: common.properties
          refresh: true
          group: DEFAULT_GROUP
        shared-configs[1]:
          data-id: db.properties
          refresh: true
          group: DEFAULT_GROUP
        shared-configs[2]:
          data-id: redis.properties
          refresh: true
          group: DEFAULT_GROUP
        shared-configs[3]:
          data-id: rabbitmq.properties
          refresh: true
          group: DEFAULT_GROUP

  main:
    allow-bean-definition-overriding: true
  profiles:
    active: local
  thymeleaf:
    cache: false
    encoding: UTF-8
    mode: LEGACYHTML5
    prefix: classpath:/templates/
    suffix: .html
  servlet:
    multipart:
      enabled: true
      max-request-size: 5120MB
      max-file-size: 5120MB
      file-size-threshold: 0

filters:
  # 熔断降级配置
  - name: Hystrix
    args:
      name: default
      fallbackUri: 'forward:/fallback'


# hystrix 信号量隔离，3秒后自动超时
hystrix:
  command:
    default:
      execution:
        isolation:
          strategy: SEMAPHORE
          thread:
            timeoutInMilliseconds: 60000
  shareSecurityContext: true

management:
  endpoints:
    web:
      exposure:
        # 开放部分端点和自定义端点
        include: health,info,open

aimo:
  # 开放api
  api:
    # 参数签名验证
    check-sign: false
    # 访问权限控制
    access-control: true
    # swagger调试,生产环境设为false
    # 始终放行
    permit-all:
      - /*/login/**
      - /*/logout/**
      - /*/oauth/**
      - /actuator/**
      - /*/workflow/**
    # 忽略权限鉴定
    authority-ignores:
      - /*/authority/granted/me
      - /*/authority/granted/me/menu
      - /*/user/currentUser
      - /*/current/user/**
      - /*/menu/getAll
    # 签名忽略
    sign-ignores:
      - /**/login/**
      - /**/logout/**

#mybatis plus 设置
mybatis-plus:
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.aimo.gateway.spring.server.**.entity
  mapper-locations: classpath:mapper/*.xml
