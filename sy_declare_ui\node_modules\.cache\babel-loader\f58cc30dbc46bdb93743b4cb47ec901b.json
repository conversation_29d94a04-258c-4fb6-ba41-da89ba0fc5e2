{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\directive\\changeImj.js", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\directive\\changeImj.js", "mtime": 1752737748498}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\.babelrc", "mtime": 1752737748316}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMucmVnZXhwLmNvbnN0cnVjdG9yLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMucmVnZXhwLmRvdC1hbGwuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5yZWdleHAuZXhlYy5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLnJlZ2V4cC5zdGlja3kuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5yZWdleHAudG8tc3RyaW5nLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMucmVnZXhwLnRlc3QuanMiOwpleHBvcnQgZnVuY3Rpb24gY2hhbmdlSW1qKGVsLCBiaW5kaW5nKSB7CiAgdmFyIGltZ1VSTCA9IGJpbmRpbmcudmFsdWU7IC8v6I635Y+W5Zu+54mH5Zyw5Z2ACiAgaWYgKGltZ1VSTCkgewogICAgdmFyIHJlZyA9IFJlZ0V4cCgnaHR0cCcpOwogICAgaWYgKHJlZy50ZXN0KGltZ1VSTCkpIHsKICAgICAgZWwuc2V0QXR0cmlidXRlKCdzcmMnLCBpbWdVUkwpOwogICAgfSBlbHNlIHsKICAgICAgZWwuc2V0QXR0cmlidXRlKCdzcmMnLCAnaHR0cHM6Ly9vYS5haW1vdGVjaC5jbi9pbWFnZXMtc2VydmVyJyArIGltZ1VSTCk7CiAgICB9CiAgfSBlbHNlIHsKICAgIGVsLnNldEF0dHJpYnV0ZSgnc3JjJywgcmVxdWlyZSgnQC9hc3NldHMvaW1hZ2VzL25vdEltZy5wbmcnKSk7CiAgfQp9"}, {"version": 3, "names": ["changeImj", "el", "binding", "imgURL", "value", "reg", "RegExp", "test", "setAttribute", "require"], "sources": ["D:/Users/<USER>/Desktop/dev/sy_declare_ui/src/directive/changeImj.js"], "sourcesContent": ["export function changeImj(el, binding){\r\n    let imgURL = binding.value;//获取图片地址\r\n    if (imgURL) {\r\n        let reg = RegExp('http');\r\n        if(reg.test(imgURL)){\r\n            el.setAttribute('src', imgURL);\r\n        }else{\r\n            el.setAttribute('src', 'https://oa.aimotech.cn/images-server'+imgURL);\r\n        }\r\n     }else{\r\n        el.setAttribute('src', require('@/assets/images/notImg.png') );\r\n     }\r\n}\r\n"], "mappings": ";;;;;;AAAA,OAAO,SAASA,SAASA,CAACC,EAAE,EAAEC,OAAO,EAAC;EAClC,IAAIC,MAAM,GAAGD,OAAO,CAACE,KAAK,CAAC;EAC3B,IAAID,MAAM,EAAE;IACR,IAAIE,GAAG,GAAGC,MAAM,CAAC,MAAM,CAAC;IACxB,IAAGD,GAAG,CAACE,IAAI,CAACJ,MAAM,CAAC,EAAC;MAChBF,EAAE,CAACO,YAAY,CAAC,KAAK,EAAEL,MAAM,CAAC;IAClC,CAAC,MAAI;MACDF,EAAE,CAACO,YAAY,CAAC,KAAK,EAAE,sCAAsC,GAACL,MAAM,CAAC;IACzE;EACH,CAAC,MAAI;IACFF,EAAE,CAACO,YAAY,CAAC,KAAK,EAAEC,OAAO,CAAC,4BAA4B,CAAE,CAAC;EACjE;AACL"}]}