{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\custom\\base\\whAddress\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\custom\\base\\whAddress\\index.vue", "mtime": 1754360258641}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAiFA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/view/module/custom/base/whAddress", "sourcesContent": ["<!--\r\n@create date 2020-12-14\r\n<AUTHOR>\r\n@desc 报关清关地址维护表\r\n-->\r\n<template>\r\n  <Card>\r\n    <Form ref=\"searchForm\" :model=\"searchForm\" inline>\r\n      <FormItem prop=\"whCode\">\r\n        <Input v-model=\"searchForm.whCode\" placeholder=\"请输入仓库代码\"/>\r\n      </FormItem>\r\n      <FormItem prop=\"country\">\r\n        <Select v-model=\"searchForm.country\" filterable clearable placeholder=\"请选择国家\" style=\"width:150px\">\r\n          <Option v-for=\"(item,index) in countryList\" :value=\"item['two_code']\" :key=\"index\">{{ item['name_cn'] }}\r\n          </Option>\r\n        </Select>\r\n      </FormItem>\r\n      <FormItem prop=\"address\">\r\n        <Input v-model=\"searchForm.address\" placeholder=\"请输入地址\"/>\r\n      </FormItem>\r\n      <FormItem>\r\n        <Button type=\"primary\" @click=\"handleSearch()\">查询</Button>\r\n        <Button @click=\"handleReset()\" style=\"margin-left:10px\">重置</Button>\r\n      </FormItem>\r\n    </Form>\r\n    <div style=\"margin-bottom:10px\">\r\n      <div style=\"float:left\">\r\n        <Upload ref=\"uploadFileRef\" name=\"importFile\" :action=\"importURl\" :max-size=\"10240\"\r\n                :on-success=\"handleImportSuccess\" :format=\"['xls', 'xlsx']\" :show-upload-list=\"false\"\r\n                :on-format-error=\"handleImportFormatError\"\r\n                :on-error=\"handleImportError\" :headers=\"loginInfo\" :on-exceeded-size=\"handleMaxSize\">\r\n          <Button class=\"search-btn\" type=\"primary\">导入</Button>\r\n        </Upload>\r\n      </div>\r\n      <Button class=\"search-btn\" style=\"margin-left:10px;\" @click=\"addWhAddress\">新增</Button>\r\n      <Button class=\"search-btn\" style=\"margin-left:10px;\" @click=\"whAddressExport\">导出</Button>\r\n    </div>\r\n    <Table :border=\"true\" :columns=\"column\" :data=\"data\" :loading=\"loading\">\r\n      <template v-slot:country=\"{row}\">\r\n        <span v-for=\"item in countryList\" v-if=\"item['two_code'] === row.country\">{{ item['name_cn'] }}</span>\r\n      </template>\r\n      <template v-slot:action=\"{row}\">\r\n        <Button size=\"small\" type=\"info\" @click=\"editWhAddress(row)\" style=\"margin:0 2px\">编辑</Button>\r\n        <Button size=\"small\" type=\"info\" @click=\"delWhAddress(row)\" style=\"margin:0 2px\">删除</Button>\r\n        <Button size=\"small\" type=\"info\" @click=\"lookLog(row)\" style=\"margin:0 2px\">日志</Button>\r\n      </template>\r\n    </Table>\r\n    <Page :total=\"pageInfo.total \" :current=\"pageInfo.page\" :page-size=\"pageInfo.limit\" :show-elevator=\"true\"\r\n          :show-sizer=\"true\"\r\n          :show-total=\"true\" @on-change=\"handlePage\" @on-page-size-change='handlePageSize' :transfer=\"true\"></Page>\r\n    <Modal :width=\"530\" v-model=\"modal\" :title=\"title\" @on-cancel=\"cancelForm\">\r\n      <Spin :fix=\"true\" v-if=\"spinShow\">加载中...</Spin>\r\n      <Form ref=\"form\" :model=\"form\" inline label-position=\"right\" :label-width=\"110\">\r\n        <FormItem label=\"仓库代码\" prop=\"whCode\" :rules=\"{required: true, message: '不能为空', trigger: 'blur'}\">\r\n          <Input v-model.trim=\"form.whCode\" placeholder=\"请输入仓库代码\" class=\"widthClass\"/>\r\n        </FormItem>\r\n        <FormItem label=\"国家\" prop=\"country\" :rules=\"{required: true, message: '不能为空', trigger: 'blur'}\">\r\n          <Select v-model=\"form.country\" filterable clearable placeholder=\"请选择所在国家\" class=\"widthClass\">\r\n            <Option v-for=\"(item,index) in countryList\" :value=\"item['two_code']\" :key=\"index\">{{ item['name_cn'] }}\r\n            </Option>\r\n          </Select>\r\n        </FormItem>\r\n        <FormItem label=\"州\" prop=\"province\" :rules=\"{required: true, message: '不能为空', trigger: 'blur'}\">\r\n          <Input v-model.trim=\"form.province\" placeholder=\"请输入\" class=\"widthClass\"/>\r\n        </FormItem>\r\n        <FormItem label=\"邮编\" prop=\"postCode\">\r\n          <Input v-model.trim=\"form.postCode\" placeholder=\"请输入\" class=\"widthClass\"/>\r\n        </FormItem>\r\n        <FormItem label=\"地址\" prop=\"address\">\r\n          <Input v-model.trim=\"form.address\" placeholder=\"请输入\" class=\"widthClass\"/>\r\n        </FormItem>\r\n      </Form>\r\n      <div slot=\"footer\">\r\n        <Button type=\"primary\" :disabled=\"disabled\" :loading=\"saving\" @click=\"saveWhAddress\">保存</Button>\r\n        <Button @click=\"cancelForm\">取消</Button>\r\n      </div>\r\n    </Modal>\r\n    <LogModel ref=\"logModelRef\" :logVisible=\"logVisible\" :onCancel=\"()=>logVisible=false\"/>\r\n  </Card>\r\n</template>\r\n<script>\r\nimport CommonApi from \"@/api/base/commonApi\";\r\nimport WhAddress from \"@/api/custom/whAddress\";\r\nimport LogModel from \"@/view/module/base/bussinessLog/logModel.vue\";\r\nimport {getToken, getUrl} from \"@/libs/util\";\r\n\r\nexport default {\r\n  components: {LogModel},\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      saving: false,\r\n      modal: false,\r\n      spinShow: false,\r\n      disabled: false,\r\n      logVisible: false,\r\n      refType: null,\r\n      title: '',\r\n      searchForm: {whCode: '', country: '', address: ''},\r\n      pageInfo: {total: 0, page: 1, limit: 10},\r\n      loginInfo: {\r\n        Accept: 'application/json,text/plain, */*, */*',\r\n        mode: 'cors',\r\n        Authorization: 'Bearer ' + getToken()\r\n      },\r\n      importURl: getUrl() + \"/base/whAddress/importFile\",\r\n      form: {whCode: null, country: null, province: null, postCode: null, address: null},\r\n      data: [],\r\n      column: [\r\n        {title: '仓库编码', key: 'whCode', minWidth: 100, align: 'center'},\r\n        {title: '所在国家', key: 'country', minWidth: 100, align: 'center', slot: 'country'},\r\n        {title: '州', key: 'province', minWidth: 100, align: 'center'},\r\n        {title: '邮编', key: 'postCode', minWidth: 150, align: 'center'},\r\n        {title: '地址', key: 'address', minWidth: 300, align: 'center'},\r\n        {title: '操作', key: 'action', width: 200, align: 'center', slot: 'action'}],\r\n      countryList: [],\r\n    }\r\n  },\r\n  mounted() {\r\n    this.handleSearch();\r\n    this.getCountryList();\r\n    this.getLogRefType();\r\n  },\r\n  methods: {\r\n    handleSearch() {\r\n      this.loading = true;\r\n      let params = {...this.searchForm, ...this.pageInfo}\r\n      WhAddress.listPage(params).then(res => {\r\n        if (res['code'] === 0) {\r\n          this.data = res.data.records;\r\n          this.pageInfo.total = Number(res.data.total)\r\n        }\r\n      }).finally(() => {\r\n        this.loading = false;\r\n      })\r\n    },\r\n    handleReset() {\r\n      this.$refs['searchForm'].resetFields();\r\n    },\r\n    handleImportSuccess(res) {\r\n      this.$refs['uploadFileRef'].clearFiles();\r\n      if (res['code'] === 0) {\r\n        this.$Message.success('导入成功');\r\n        this.handleSearch();\r\n      } else {\r\n        this.$Message.warning(res['message']);\r\n      }\r\n    },\r\n    handleImportFormatError(file) {\r\n      //格式验证失败的钩子\r\n      this.$Modal.error({\r\n        title: '文件格式不正确',\r\n        content: '文件 ' + file.name + '格式不正确，支持上传的文件类型：xls,xlsx',\r\n        okText: '确认'\r\n      });\r\n    },\r\n    handleImportError(err, file) {\r\n      this.$Message.warning(file.message);\r\n    },\r\n    handleMaxSize() {\r\n      this.$Message.warning('图片大小不能超过2M.');\r\n    },\r\n    whAddressExport() {\r\n      let params = {...this.searchForm};\r\n      params['fileName'] = \"仓库地址\" + new Date().getExportFormat() + \".xls\";\r\n      this.loading = true;\r\n      WhAddress.download(params, () => {\r\n        this.loading = false\r\n      })\r\n    },\r\n    delWhAddress(row) {\r\n      this.$Modal.confirm({\r\n        title: '提示',\r\n        content: '您确认要删除这条数据吗？',\r\n        onOk: () => {\r\n          WhAddress.remove({id: row.id}).then(res => {\r\n            if (res['code'] === 0) {\r\n              this.$Message.success('删除成功!');\r\n              this.handleSearch();\r\n            }\r\n          })\r\n        },\r\n      })\r\n    },\r\n    editWhAddress(row) {\r\n      this.title = \"修改\";\r\n      this.modal = true;\r\n      this.disabled = false;\r\n      this.resetForm();\r\n      this.form = Object.assign({}, row);\r\n    },\r\n    addWhAddress() {\r\n      this.title = \"添加\";\r\n      this.modal = true;\r\n      this.disabled = false;\r\n      this.resetForm();\r\n    },\r\n    saveWhAddress() {\r\n      this.$refs['form'].validate((valid) => {\r\n        if (valid) {\r\n          this.saving = true;\r\n          WhAddress.saveWhAddress(this.form).then(res => {\r\n            if (res['code'] === 0) {\r\n              this.saving = false;\r\n              this.$Message.success('保存成功!');\r\n              this.resetForm();\r\n              this.modal = false;\r\n              this.handleSearch();\r\n            }\r\n          }).finally(() => {\r\n            this.saving = false;\r\n          })\r\n        }\r\n      })\r\n    },\r\n    //获取目的国家\r\n    getCountryList() {\r\n      CommonApi.ListDictionaryValueBy(\"base_country\").then(res => {\r\n        if (res && res['code'] === 0) {\r\n          let data = res['data']\r\n          if (data) {\r\n            this.countryList = data.map(item => JSON.parse(item.value));\r\n          }\r\n        }\r\n      })\r\n    },\r\n    handlePage(page) {\r\n      this.pageInfo.page = page;\r\n      this.handleSearch();\r\n    },\r\n    handlePageSize(size) {\r\n      this.pageInfo.page = 1;\r\n      this.pageInfo.limit = size;\r\n      this.handleSearch();\r\n    },\r\n    cancelForm() {\r\n      this.modal = false;\r\n      this.resetForm();\r\n    },\r\n    resetForm() {\r\n      this.$refs['form'].resetFields();\r\n      this.form = {};\r\n    },\r\n    //日志\r\n    lookLog(row) {\r\n      const {logModelRef} = this.$refs;\r\n      if (logModelRef) {\r\n        logModelRef.setDefault(row.id, this.refType);\r\n      }\r\n      this.logVisible = true;\r\n    },\r\n    getLogRefType() {\r\n      WhAddress.getLogRefType().then(res => {\r\n        if (res['code'] === 0) {\r\n          this.refType = res.data;\r\n        }\r\n      })\r\n    },\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\">\r\n.widthClass {\r\n  width: 350px\r\n}\r\n</style>\r\n"]}]}