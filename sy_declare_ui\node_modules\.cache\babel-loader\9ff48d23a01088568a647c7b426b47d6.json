{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\api\\system\\data.js", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\api\\system\\data.js", "mtime": 1752737748420}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\.babelrc", "mtime": 1752737748316}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSAiRDovVXNlcnMvYWRtaW5pL0Rlc2t0b3AvZGV2L3N5X2RlY2xhcmVfdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL29iamVjdFNwcmVhZDIuanMiOwppbXBvcnQgcmVxdWVzdCBmcm9tICdAL2xpYnMvcmVxdWVzdCc7CgovKioNCiAqIOS/neWtmOmUmeivr+aXpeW/lw0KICogQHBhcmFtIGluZm8NCiAqLwpleHBvcnQgdmFyIHNhdmVFcnJvckxvZ2dlciA9IGZ1bmN0aW9uIHNhdmVFcnJvckxvZ2dlcihpbmZvKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnc2F2ZV9lcnJvcl9sb2dnZXInLAogICAgZGF0YTogaW5mbywKICAgIG1ldGhvZDogJ3Bvc3QnCiAgfSk7Cn07CmV4cG9ydCB2YXIgcHVibGljQXBpID0gewogIGdldEFsbFNpdGVzOiBmdW5jdGlvbiBnZXRBbGxTaXRlcygpIHsKICAgIHZhciBwYXJhbXMgPSBhcmd1bWVudHMubGVuZ3RoID4gMCAmJiBhcmd1bWVudHNbMF0gIT09IHVuZGVmaW5lZCA/IGFyZ3VtZW50c1swXSA6IHt9OwogICAgLy8g6I635Y+W5omA5pyJ56uZ54K56YCJ6aG5LUFtYXpvbuWSjGViYXkKICAgIHJldHVybiByZXF1ZXN0KHsKICAgICAgdXJsOiAnL2Jhc2UvYmFzaWMvc2l0ZS9saXN0QWxsJywKICAgICAgcGFyYW1zOiBfb2JqZWN0U3ByZWFkKHt9LCBwYXJhbXMpLAogICAgICBtZXRob2Q6ICdnZXQnCiAgICB9KTsKICB9Cn07"}, {"version": 3, "names": ["request", "save<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "info", "url", "data", "method", "publicApi", "getAllSites", "params", "arguments", "length", "undefined", "_objectSpread"], "sources": ["D:/Users/<USER>/Desktop/dev/sy_declare_ui/src/api/system/data.js"], "sourcesContent": ["import request from '@/libs/request'\r\n\r\n/**\r\n * 保存错误日志\r\n * @param info\r\n */\r\nexport const saveErrorLogger = info => {\r\n  return request({\r\n    url: 'save_error_logger',\r\n    data: info,\r\n    method: 'post'\r\n  })\r\n}\r\nexport const publicApi = {\r\n  getAllSites: (params = {}) => { // 获取所有站点选项-Amazon和ebay\r\n    return request({\r\n      url: '/base/basic/site/listAll',\r\n      params: { ...params },\r\n      method: 'get'\r\n    })\r\n  }\r\n}\r\n"], "mappings": ";AAAA,OAAOA,OAAO,MAAM,gBAAgB;;AAEpC;AACA;AACA;AACA;AACA,OAAO,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAGC,IAAI,EAAI;EACrC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,mBAAmB;IACxBC,IAAI,EAAEF,IAAI;IACVG,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;AACD,OAAO,IAAMC,SAAS,GAAG;EACvBC,WAAW,EAAE,SAAAA,YAAA,EAAiB;IAAA,IAAhBC,MAAM,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IAAO;IAC9B,OAAOT,OAAO,CAAC;MACbG,GAAG,EAAE,0BAA0B;MAC/BK,MAAM,EAAAI,aAAA,KAAOJ,MAAM,CAAE;MACrBH,MAAM,EAAE;IACV,CAAC,CAAC;EACJ;AACF,CAAC"}]}