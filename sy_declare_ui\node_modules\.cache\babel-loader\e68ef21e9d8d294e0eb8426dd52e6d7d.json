{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\api\\lx\\fbaShipment.js", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\api\\lx\\fbaShipment.js", "mtime": 1752737748409}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\.babelrc", "mtime": 1752737748316}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC9saWJzL3JlcXVlc3QnOwppbXBvcnQgeyBOZXNwb3N0UmVxdWVzdCB9IGZyb20gJ0AvbGlicy9heGlvcy5qcyc7CnZhciBmYmFTaGlwbWVudFBhdGggPSAiL2x4L2ZiYVNoaXBtZW50IjsKLyoqDQogKiDojrflj5bliIbpobXmlbDmja4NCiAqLwp2YXIgbGlzdFBhZ2UgPSBmdW5jdGlvbiBsaXN0UGFnZShwYXJhbXMpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6IGZiYVNoaXBtZW50UGF0aCArICcvbGlzdFBhZ2UnLAogICAgcGFyYW1zOiBwYXJhbXMsCiAgICBtZXRob2Q6ICdnZXQnCiAgfSk7Cn07Ci8qKg0KICog5ZCM5q2l6LSn5Lu2DQogKi8KdmFyIHN5bmNGYmFTaGlwbWVudCA9IGZ1bmN0aW9uIHN5bmNGYmFTaGlwbWVudChkYXRhKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiBmYmFTaGlwbWVudFBhdGggKyAnL3N5bmMnLAogICAgZGF0YTogZGF0YSwKICAgIG1ldGhvZDogJ3Bvc3QnCiAgfSk7Cn07CmV4cG9ydCBkZWZhdWx0IHsKICBsaXN0UGFnZTogbGlzdFBhZ2UsCiAgc3luY0ZiYVNoaXBtZW50OiBzeW5jRmJhU2hpcG1lbnQKfTs="}, {"version": 3, "names": ["request", "NespostRequest", "fbaShipmentPath", "listPage", "params", "url", "method", "syncFbaShipment", "data"], "sources": ["D:/Users/<USER>/Desktop/dev/sy_declare_ui/src/api/lx/fbaShipment.js"], "sourcesContent": ["import request from '@/libs/request'\r\nimport { NespostRequest } from '@/libs/axios.js';\r\nconst fbaShipmentPath = \"/lx/fbaShipment\";\r\n/**\r\n * 获取分页数据\r\n */\r\nconst listPage = (params) => {\r\n  return request({\r\n    url: fbaShipmentPath + '/listPage',\r\n    params,\r\n    method: 'get'\r\n  })\r\n}\r\n/**\r\n * 同步货件\r\n */\r\nconst syncFbaShipment = (data) => {\r\n  return request({\r\n    url: fbaShipmentPath + '/sync',\r\n    data,\r\n    method: 'post'\r\n  })\r\n}\r\nexport default {\r\n  listPage,\r\n  syncFbaShipment\r\n}\r\n\r\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,gBAAgB;AACpC,SAASC,cAAc,QAAQ,iBAAiB;AAChD,IAAMC,eAAe,GAAG,iBAAiB;AACzC;AACA;AACA;AACA,IAAMC,QAAQ,GAAG,SAAXA,QAAQA,CAAIC,MAAM,EAAK;EAC3B,OAAOJ,OAAO,CAAC;IACbK,GAAG,EAAEH,eAAe,GAAG,WAAW;IAClCE,MAAM,EAANA,MAAM;IACNE,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;AACD;AACA;AACA;AACA,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAIC,IAAI,EAAK;EAChC,OAAOR,OAAO,CAAC;IACbK,GAAG,EAAEH,eAAe,GAAG,OAAO;IAC9BM,IAAI,EAAJA,IAAI;IACJF,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;AACD,eAAe;EACbH,QAAQ,EAARA,QAAQ;EACRI,eAAe,EAAfA;AACF,CAAC"}]}