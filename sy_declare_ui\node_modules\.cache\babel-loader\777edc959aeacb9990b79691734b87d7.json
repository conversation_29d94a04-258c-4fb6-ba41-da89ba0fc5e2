{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\api\\logistics\\logisticsFee.js", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\api\\logistics\\logisticsFee.js", "mtime": 1752737748408}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\.babelrc", "mtime": 1752737748316}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC9saWJzL3JlcXVlc3QnOwppbXBvcnQgZXhwb3J0UmVxdWVzdCBmcm9tICJAL2xpYnMvZXhwb3J0UmVxdWVzdCI7CnZhciBwYXRoID0gIi9iYXNlL2xvZ2lzdGljc0ZlZSI7Ci8qKg0KICog6I635Y+W5YiG6aG15pWw5o2uDQogKi8KdmFyIGxpc3RQYWdlID0gZnVuY3Rpb24gbGlzdFBhZ2UocGFyYW1zKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiBwYXRoICsgJy9saXN0UGFnZScsCiAgICBwYXJhbXM6IHBhcmFtcywKICAgIG1ldGhvZDogJ2dldCcKICB9KTsKfTsKLyoqDQogKiDliKDpmaTmlbDmja4NCiAqLwp2YXIgcmVtb3ZlTG9naXN0aWNzRmVlID0gZnVuY3Rpb24gcmVtb3ZlTG9naXN0aWNzRmVlKHBhcmFtcykgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogcGF0aCArICcvcmVtb3ZlTG9naXN0aWNzRmVlJywKICAgIHBhcmFtczogcGFyYW1zLAogICAgbWV0aG9kOiAncG9zdCcKICB9KTsKfTsKLyoqDQogKiDojrflj5bml6Xlv5cNCiAqLwp2YXIgZ2V0TG9nUmVmVHlwZSA9IGZ1bmN0aW9uIGdldExvZ1JlZlR5cGUoKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiBwYXRoICsgJy9nZXRMb2dSZWZUeXBlJywKICAgIG1ldGhvZDogJ2dldCcKICB9KTsKfTsKdmFyIGV4cG9ydEZpbGUgPSBmdW5jdGlvbiBleHBvcnRGaWxlKHBhcmFtcywgY2FsbGJhY2spIHsKICB2YXIgY29uZmlnID0gewogICAgcGFyYW1zOiBwYXJhbXMsCiAgICBtZXRob2Q6ICdwb3N0JywKICAgIGZpbGVOYW1lOiBwYXJhbXNbJ2ZpbGVOYW1lJ10KICB9OwogIHJldHVybiBleHBvcnRSZXF1ZXN0KHBhdGggKyAnL2V4cG9ydEZpbGUnLCBjb25maWcsIGNhbGxiYWNrKTsKfTsKZXhwb3J0IGRlZmF1bHQgewogIHBhdGg6IHBhdGgsCiAgbGlzdFBhZ2U6IGxpc3RQYWdlLAogIHJlbW92ZUxvZ2lzdGljc0ZlZTogcmVtb3ZlTG9naXN0aWNzRmVlLAogIGdldExvZ1JlZlR5cGU6IGdldExvZ1JlZlR5cGUsCiAgZXhwb3J0RmlsZTogZXhwb3J0RmlsZQp9Ow=="}, {"version": 3, "names": ["request", "exportRequest", "path", "listPage", "params", "url", "method", "removeLogisticsFee", "getLogRefType", "exportFile", "callback", "config", "fileName"], "sources": ["D:/Users/<USER>/Desktop/dev/sy_declare_ui/src/api/logistics/logisticsFee.js"], "sourcesContent": ["import request from '@/libs/request'\r\nimport exportRequest from \"@/libs/exportRequest\";\r\n\r\nconst path = \"/base/logisticsFee\";\r\n/**\r\n * 获取分页数据\r\n */\r\nconst listPage = (params) => {\r\n  return request({\r\n    url: path + '/listPage',\r\n    params,\r\n    method: 'get'\r\n  })\r\n}\r\n/**\r\n * 删除数据\r\n */\r\nconst removeLogisticsFee = (params) => {\r\n  return request({\r\n    url: path + '/removeLogisticsFee',\r\n    params,\r\n    method: 'post'\r\n  })\r\n}\r\n/**\r\n * 获取日志\r\n */\r\nconst getLogRefType = () => {\r\n  return request({\r\n    url: path + '/getLogRefType',\r\n    method: 'get'\r\n  })\r\n}\r\nconst exportFile = (params,callback)=>{\r\n  const config = {\r\n    params:params,\r\n    method: 'post',\r\n    fileName:params['fileName']\r\n  }\r\n  return exportRequest(path + '/exportFile',config,callback);\r\n}\r\n\r\nexport default {\r\n  path,\r\n  listPage,\r\n  removeLogisticsFee,\r\n  getLogRefType,\r\n  exportFile ,\r\n}\r\n\r\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,gBAAgB;AACpC,OAAOC,aAAa,MAAM,sBAAsB;AAEhD,IAAMC,IAAI,GAAG,oBAAoB;AACjC;AACA;AACA;AACA,IAAMC,QAAQ,GAAG,SAAXA,QAAQA,CAAIC,MAAM,EAAK;EAC3B,OAAOJ,OAAO,CAAC;IACbK,GAAG,EAAEH,IAAI,GAAG,WAAW;IACvBE,MAAM,EAANA,MAAM;IACNE,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;AACD;AACA;AACA;AACA,IAAMC,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAIH,MAAM,EAAK;EACrC,OAAOJ,OAAO,CAAC;IACbK,GAAG,EAAEH,IAAI,GAAG,qBAAqB;IACjCE,MAAM,EAANA,MAAM;IACNE,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;AACD;AACA;AACA;AACA,IAAME,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS;EAC1B,OAAOR,OAAO,CAAC;IACbK,GAAG,EAAEH,IAAI,GAAG,gBAAgB;IAC5BI,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;AACD,IAAMG,UAAU,GAAG,SAAbA,UAAUA,CAAIL,MAAM,EAACM,QAAQ,EAAG;EACpC,IAAMC,MAAM,GAAG;IACbP,MAAM,EAACA,MAAM;IACbE,MAAM,EAAE,MAAM;IACdM,QAAQ,EAACR,MAAM,CAAC,UAAU;EAC5B,CAAC;EACD,OAAOH,aAAa,CAACC,IAAI,GAAG,aAAa,EAACS,MAAM,EAACD,QAAQ,CAAC;AAC5D,CAAC;AAED,eAAe;EACbR,IAAI,EAAJA,IAAI;EACJC,QAAQ,EAARA,QAAQ;EACRI,kBAAkB,EAAlBA,kBAAkB;EAClBC,aAAa,EAAbA,aAAa;EACbC,UAAU,EAAVA;AACF,CAAC"}]}