{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\basf\\site\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\basf\\site\\index.vue", "mtime": 1752737748517}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAyIA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/view/module/basf/site", "sourcesContent": ["<template>\r\n  <div>\r\n    <Card :shadow=\"true\">\r\n      <Form ref=\"searchForm\" class=\"searchForm\" :model=\"pageInfo\" inline @submit.native.prevent>\r\n        <FormItem prop=\"platformId\">\r\n          <Select type=\"text\" v-model=\"pageInfo.platformId\" placeholder=\"平台名称\" style=\"width:160px\">\r\n            <Option v-for=\"(item,index) in platformList\" :value=\"item.id\" :key=\"index\">{{ item.name }}</Option>\r\n          </Select>\r\n        </FormItem>\r\n        <FormItem prop=\"code\">\r\n          <Input type=\"text\" v-model=\"pageInfo.code\" placeholder=\"站点编码\"/>\r\n        </FormItem>\r\n        <FormItem prop=\"name\">\r\n          <Input type=\"text\" v-model=\"pageInfo.name\" placeholder=\"站点名称\"/>\r\n        </FormItem>\r\n        <FormItem prop=\"status\">\r\n          <Select v-model=\"pageInfo.status\" style=\"width: 100px\" placeholder=\"请选择状态\" :clearable=\"false\"\r\n                  transfer=\"true\">\r\n            <Option v-for=\"v in statusOps\" :value=\"v.key\" :key=\"v.key\">{{v.name }}</Option>\r\n          </Select>\r\n        </FormItem>\r\n        <FormItem>\r\n          <Button type=\"primary\" @click=\"handleSearch(1)\">查询</Button>&nbsp;\r\n          <Button @click=\"handleResetForm('searchForm')\">重置</Button>\r\n        </FormItem>\r\n      </Form>\r\n      <div class=\"search-con search-con-top\">\r\n        <ButtonGroup>\r\n          <Button :disabled=\"!hasAuthority('siteAdd')\" class=\"search-btn\" type=\"primary\" @click=\"handleModal()\">\r\n            <span>添加</span>\r\n          </Button>\r\n        </ButtonGroup>\r\n      </div>\r\n      <Table border=\"true\" ref=\"autoTableRef\" :max-height=\"autoTableHeight($refs.autoTableRef)\" :columns=\"columns\"\r\n             :data=\"data\" :loading=\"loading\">\r\n        <template v-slot:status=\"{ row }\">\r\n          <Badge v-for=\"v in statusOps\" :text=\"v.name\" v-if=\"v.key === row.status\"\r\n                 :status=\"v.key === 0?'success':'warning'\" v-bind:key=\"v.key\"></Badge>\r\n        </template>\r\n        <template v-slot:action=\"{ row }\">\r\n          <a v-if=\"hasAuthority('siteEdit')\" @click=\"handleModal(row)\">编辑</a>&nbsp;\r\n          <a @click=\"handleClick('view',row)\">查看</a>&nbsp;\r\n          <a v-if=\"hasAuthority('siteEdit')\" @click=\"handleClick('remove',row)\">删除</a>\r\n        </template>\r\n      </Table>\r\n      <Page :total=\"pageInfo.total\" size=\"small\" :current=\"pageInfo.page\" :page-size=\"pageInfo.limit\" show-elevator=\"true\"\r\n            show-sizer=\"true\" show-total=\"true\" @on-change=\"handlePage\" @on-page-size-change='handlePageSize'></Page>\r\n    </Card>\r\n    <Modal v-model=\"modalVisible\" :title=\"modalTitle\" @on-cancel=\"handleReset\">\r\n      <div style=\"width:400px\">\r\n        <Form ref=\"form\" :model=\"formItem\" :rules=\"formItemRules\" :label-width=\"100\">\r\n          <FormItem label=\"平台名称\" prop=\"platformId\">\r\n            <Select v-model=\"formItem.platformId\" filterable>\r\n              <Option v-for=\"item in platformList\" :value=\"item.id\" :key=\"item.name\">{{ item.name }}</Option>\r\n            </Select>\r\n          </FormItem>\r\n          <FormItem label=\"站点编码\" prop=\"code\">\r\n            <Input v-model=\"formItem.code\" placeholder=\"请输入内容\"></Input>\r\n          </FormItem>\r\n          <FormItem label=\"站点名称\" prop=\"name\">\r\n            <Input v-model=\"formItem.name\" placeholder=\"请输入内容\"></Input>\r\n          </FormItem>\r\n          <FormItem label=\"所属货币\" prop=\"currencyId\">\r\n            <Select v-model=\"formItem.currencyId\" filterable>\r\n              <Option v-for=\"item in currencyList\" :value=\"item.id\" :key=\"item.name\">{{ item.name }}({{\r\n                  item.code\r\n                }})\r\n              </Option>\r\n            </Select>\r\n          </FormItem>\r\n          <FormItem label=\"站点URL\" prop=\"siteUrl\">\r\n            <Input v-model=\"formItem.siteUrl\" placeholder=\"请输入内容\"></Input>\r\n          </FormItem>\r\n          <FormItem label=\"备注\" prop=\"remark\">\r\n            <Input v-model=\"formItem.remark\" type=\"textarea\" placeholder=\"请输入内容\"></Input>\r\n          </FormItem>\r\n          <FormItem label=\"状态\">\r\n            <RadioGroup v-model=\"formItem.status\" type=\"button\">\r\n              <Radio v-for=\"v in statusOps\" :label=\"v.key\" v-if=\"v.key !== -1\" v-bind:key=\"v.key\">{{ v.name }}</Radio>\r\n            </RadioGroup>\r\n          </FormItem>\r\n        </Form>\r\n        <div class=\"drawer-footer\">\r\n          <Button type=\"default\" @click=\"handleReset\">取消</Button>&nbsp;\r\n          <Button type=\"primary\" @click=\"handleSubmit\" :loading=\"saving\">保存</Button>\r\n        </div>\r\n      </div>\r\n    </Modal>\r\n    <Modal v-model=\"modalViewVisible\" :title=\"modalTitle\" width=\"50\" @on-cancel=\"handleReset\">\r\n      <div>\r\n        <Form ref=\"viewForm\" :model=\"formItem\" inline :label-width=\"100\">\r\n          <FormItem label=\"平台名称\" prop=\"platformName\">\r\n            <Input v-model=\"formItem.platformName\" readonly=\"true\" style=\"width:165px\"></Input>\r\n          </FormItem>\r\n          <FormItem label=\"站点编码\" prop=\"code\">\r\n            <Input v-model=\"formItem.name\" readonly=\"true\"></Input>\r\n          </FormItem>\r\n          <FormItem label=\"站点名称\" prop=\"name\">\r\n            <Input v-model=\"formItem.name\" readonly=\"true\"></Input>\r\n          </FormItem>\r\n          <FormItem label=\"所属货币\" prop=\"currencyName\">\r\n            <Input v-model=\"formItem.currencyName\" readonly=\"true\"></Input>\r\n          </FormItem>\r\n          <FormItem label=\"站点URL\" prop=\"siteUrl\">\r\n            <Input v-model=\"formItem.siteUrl\" readonly=\"true\"></Input>\r\n          </FormItem>\r\n          <FormItem label=\"店铺数量\" prop=\"siteCount\">\r\n            <Input v-model=\"formItem.shopCount\" readonly=\"true\"></Input>\r\n          </FormItem>\r\n          <FormItem label=\"备注\" prop=\"remark\" style=\"width:100%;\">\r\n            <Input v-model=\"formItem.remark\" type=\"textarea\" readonly=\"true\"></Input>\r\n          </FormItem>\r\n          <FormItem label=\"状态\">\r\n            <RadioGroup v-model=\"formItem.status\" type=\"button\" readonly=\"true\">\r\n              <Radio v-for=\"v in statusOps\" :label=\"v.key\" v-if=\"v.key !== -1\" v-bind:key=\"v.key\">{{ v.name }}</Radio>\r\n            </RadioGroup>\r\n          </FormItem>\r\n          <FormItem label=\"店铺列表\" prop=\"baseShopList\" style=\"width:100%;\">\r\n            <Table border=\"true\" :columns=\"shopColumns\" :data=\"formItem.baseShopList\" :loading=\"loading\">\r\n              <template v-slot:status=\"{ row }\">\r\n                <Badge v-for=\"v in statusOps\" :text=\"v.name\" v-if=\"v.key === row.status\"\r\n                       :status=\"v.key ===0?'success':'warning'\" v-bind:key=\"v.key\"></Badge>\r\n              </template>\r\n            </Table>\r\n          </FormItem>\r\n        </Form>\r\n      </div>\r\n      <div slot=\"footer\"></div>\r\n    </Modal>\r\n  </div>\r\n</template>\r\n<style>\r\n.drawer-footer {\r\n  border: 0;\r\n}\r\n</style>\r\n<script>\r\nimport Site from '@/api/basf/site'\r\nimport Platform from '@/api/basf/platform'\r\nimport Currency from '@/api/basf/currency'\r\nimport {autoTableHeight} from '@/libs/tools.js'\r\nimport Common from '@/api/basic/common'\r\n\r\nexport default {\r\n  name: 'siteList',\r\n  data() {\r\n    return {\r\n      autoTableHeight,\r\n      loading: false,\r\n      saving: false,\r\n      modalVisible: false,\r\n      modalViewVisible: false,\r\n      modalTitle: '',\r\n      platformList: [],\r\n      currencyList: [],\r\n      statusOps: Common.statusOps,\r\n      pageInfo: {\r\n        status:-1,\r\n        total: 0,\r\n        page: 1,\r\n        limit: 10\r\n      },\r\n      formItemRules: {\r\n        code: [\r\n          {required: true, message: '站点名称不能为空', trigger: 'blur'}\r\n        ],\r\n        name: [\r\n          {required: true, message: '站点简称不能为空', trigger: 'blur'}\r\n        ],\r\n        platformId: [\r\n          {required: true, message: '平台名称不能为空', trigger: 'blur'}\r\n        ],\r\n        currencyId: [\r\n          {required: true, message: '所属货币不能为空', trigger: 'blur'}\r\n        ]\r\n      },\r\n      formItem: {\r\n        id: '',\r\n        code: '',\r\n        name: '',\r\n        platformId: '',\r\n        platformName: '',\r\n        remark: '',\r\n        currencyId: '',\r\n        currencyCode: '',\r\n        currencyName: '',\r\n        siteUrl: '',\r\n        baseShopList: []\r\n      },\r\n      columns: [{\r\n        type: 'selection',\r\n        width: 60,\r\n      },\r\n        {\r\n          title: '平台名称',\r\n          key: 'platformName',\r\n          width: 200\r\n        },\r\n        {\r\n          title: '站点编码',\r\n          key: 'code',\r\n          minWidth: 100\r\n        },\r\n        {\r\n          title: '站点名称',\r\n          key: 'name',\r\n          minWidth: 100\r\n        },\r\n        {\r\n          title: '货币',\r\n          key: 'currencyName',\r\n          width: 200\r\n        },\r\n        {\r\n          title: '站点URL',\r\n          key: 'siteUrl',\r\n          width: 200\r\n        },\r\n        {\r\n          title: '备注',\r\n          key: 'remark',\r\n          width: 200\r\n        },\r\n        {\r\n          title: '状态',\r\n          key: 'status',\r\n          slot: 'status',\r\n          width: 200\r\n        },\r\n        {\r\n          title: '店铺数量',\r\n          key: 'shopCount',\r\n          width: 200\r\n        },\r\n        {\r\n          title: '操作',\r\n          slot: 'action',\r\n          fixed: 'right',\r\n          width: 150\r\n        }\r\n      ],\r\n      shopColumns: [\r\n        {\r\n          title: '店铺名称',\r\n          key: 'name',\r\n          minWidth: 100\r\n        },\r\n        {\r\n          title: '店铺别名',\r\n          key: 'aliaName',\r\n          minWidth: 100\r\n        },\r\n        {\r\n          title: '状态',\r\n          key: 'status',\r\n          slot: 'status',\r\n          width: 100,\r\n        },\r\n      ],\r\n      data: []\r\n    }\r\n  },\r\n  methods: {\r\n    handleModal(data) {\r\n      if (data) {\r\n        this.modalTitle = '编辑站点'\r\n        this.formItem = Object.assign({}, this.formItem, data)\r\n      } else {\r\n        this.modalTitle = '添加站点';\r\n        this.formItem.status = 0;\r\n      }\r\n      this.modalVisible = true\r\n    },\r\n    handleView(data) {\r\n      Site.getById(data).then(res => {\r\n        this.formItem = res.data;\r\n      });\r\n      if (data) {\r\n        this.modalTitle = '查看站点'\r\n        this.formItem = Object.assign({}, this.formItem, data)\r\n      }\r\n      this.modalViewVisible = true\r\n    },\r\n    handleReset() {\r\n      this.formItem = {\r\n        id: '',\r\n        name: '',\r\n        nickName: '',\r\n        platformId: '',\r\n        platformName: '',\r\n        remark: '',\r\n        currencyId: '',\r\n        status: 0,\r\n        updateTime: '',\r\n      }\r\n      //重置验证\r\n      let form = this.$refs['form']\r\n      form.resetFields()\r\n      this.modalVisible = false\r\n      this.saving = false\r\n    },\r\n    handleSubmit() {\r\n      let form = this.$refs['form']\r\n      form.validate((valid) => {\r\n        if (valid) {\r\n          this.saving = true\r\n          if (this.formItem.id) {\r\n            Site.edit(this.formItem).then(res => {\r\n              if (res['code'] === 0) {\r\n                this.$Message.success('保存成功')\r\n                this.handleReset()\r\n              }\r\n              this.handleSearch()\r\n            }).finally(() => {\r\n              this.saving = false\r\n            })\r\n          } else {\r\n            Site.add(this.formItem).then(res => {\r\n              this.handleReset()\r\n              this.handleSearch()\r\n              if (res['code'] === 0) {\r\n                this.$Message.success('保存成功')\r\n              }\r\n            }).finally(() => {\r\n              this.saving = false\r\n            })\r\n          }\r\n        }\r\n      })\r\n    },\r\n    handleResetForm(form) {\r\n      this.$refs[form].resetFields();\r\n    },\r\n    handleSearch(page) {\r\n      if (page) {\r\n        this.pageInfo.page = page\r\n      }\r\n      this.loading = true\r\n      Site.listPage(this.pageInfo)\r\n        .then(res => {\r\n          if (res['code'] === 0) {\r\n            this.data = res.data.records;\r\n            this.pageInfo.total = parseInt(res.data.total);\r\n          }\r\n        }).finally(() => {\r\n        this.loading = false\r\n      })\r\n\r\n    },\r\n    handlePage(current) {\r\n      this.pageInfo.page = current\r\n      this.handleSearch()\r\n    },\r\n    handlePageSize(size) {\r\n      this.pageInfo.limit = size\r\n      this.handleSearch()\r\n    },\r\n    handleRemove(data) {\r\n      let modal = this.$Modal\r\n      modal.confirm({\r\n        title: '确定删除吗？',\r\n        onOk: () => {\r\n          Site.remove(data.id).then(res => {\r\n            if (res['code'] === 0) {\r\n              this.pageInfo.page = 1;\r\n              this.$Message.success('删除成功');\r\n            }\r\n            this.handleSearch();\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleClick(name, row) {\r\n      switch (name) {\r\n        case 'remove':\r\n          this.handleRemove(row)\r\n          break\r\n        case 'view':\r\n          this.handleView(row.id)\r\n          break\r\n      }\r\n    },\r\n    handlePlatform() {\r\n      Platform.getAll().then(res => {\r\n        this.platformList = res.data;\r\n      });\r\n    },\r\n    handleCurrency() {\r\n      Currency.getAll().then(res => {\r\n        this.currencyList = res.data;\r\n      });\r\n    }\r\n  },\r\n\r\n  mounted: function () {\r\n    this.handleSearch();\r\n    this.handlePlatform();\r\n    this.handleCurrency();\r\n  }\r\n}\r\n\r\n</script>\r\n\r\n"]}]}