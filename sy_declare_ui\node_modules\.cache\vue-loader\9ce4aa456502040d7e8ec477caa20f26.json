{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\custom\\InsidePurchase\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\custom\\InsidePurchase\\index.vue", "mtime": 1752737748519}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQovLyBpbXBvcnQgSW5zaWRlUHVyY2hhc2UgZnJvbSAnLi9pbnNpZGVQdXJjaGFzZS52dWUnOyAvL+WGhemDqOmHh+i0reeUn+aIkA0KaW1wb3J0IEluc2lkZVB1ckNoYXNlUHJpY2UgZnJvbSAnLi9pbnNpZGVQdXJjaGFzZVByaWNlLnZ1ZSc7IC8v5YaF6YOo6YeH6LSt5oql5Lu3DQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdJbnNpZGVQdXJjaGFzZScsDQogIGRhdGEgKCkgew0KICAgIHJldHVybiB7fTsNCiAgfSwNCiAgY29tcG9uZW50czogew0KICAgIC8vIEluc2lkZVB1cmNoYXNlLA0KICAgICBJbnNpZGVQdXJDaGFzZVByaWNlDQogIH0sDQogIG1vdW50ZWQgKCkgew0KICB9LA0KICBtZXRob2RzOiB7DQogICAgY2xpY2tUYWIgKCkgew0KICAgICAgY29uc3Qgew0KICAgICAgICBJbnNpZGVQdXJjaGFzZVJlZiwNCiAgICAgICAgSW5zaWRlUHVyQ2hhc2VQcmljZVJlZiB9ID0gdGhpcy4kcmVmczsNCiAgICAgIGlmIChJbnNpZGVQdXJjaGFzZVJlZiAmJiBJbnNpZGVQdXJjaGFzZVJlZi5jbG9zZURyb3Bkb3duKSB7DQogICAgICAgIEluc2lkZVB1cmNoYXNlUmVmLmNsb3NlRHJvcGRvd24oKTsNCiAgICAgIH0NCiAgICAgIGlmIChJbnNpZGVQdXJDaGFzZVByaWNlUmVmICYmIEluc2lkZVB1ckNoYXNlUHJpY2VSZWYuY2xvc2VEcm9wZG93bikgew0KICAgICAgICBJbnNpZGVQdXJDaGFzZVByaWNlUmVmLmNsb3NlRHJvcGRvd24oKTsNCiAgICAgIH0NCiAgICB9LA0KICB9DQp9Ow0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAiBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/view/module/custom/InsidePurchase", "sourcesContent": ["<!--\r\n@create date 2024-01-09\r\n@desc 内部采购合同\r\n-->\r\n<template>\r\n  <div class=\"financialTransaction\">\r\n<!--    <Tabs type=\"card\" @on-click=\"clickTab\">-->\r\n<!--      <TabPane label=\"内部采购合同\"  name=\"insidePurchaseFile\">-->\r\n<!--        <InsidePurchase ref=\"InsidePurchaseRef\" />-->\r\n<!--      </TabPane>-->\r\n<!--      <TabPane label=\"内部采购价格\"  name=\"insidePurchasePrice\">-->\r\n        <InsidePurChasePrice ref=\"InsidePurChasePriceRef\"/>\r\n<!--      </TabPane>-->\r\n<!--    </Tabs>-->\r\n  </div>\r\n</template>\r\n<script>\r\n// import InsidePurchase from './insidePurchase.vue'; //内部采购生成\r\nimport InsidePurChasePrice from './insidePurchasePrice.vue'; //内部采购报价\r\nexport default {\r\n  name: 'InsidePurchase',\r\n  data () {\r\n    return {};\r\n  },\r\n  components: {\r\n    // InsidePurchase,\r\n     InsidePurChasePrice\r\n  },\r\n  mounted () {\r\n  },\r\n  methods: {\r\n    clickTab () {\r\n      const {\r\n        InsidePurchaseRef,\r\n        InsidePurChasePriceRef } = this.$refs;\r\n      if (InsidePurchaseRef && InsidePurchaseRef.closeDropdown) {\r\n        InsidePurchaseRef.closeDropdown();\r\n      }\r\n      if (InsidePurChasePriceRef && InsidePurChasePriceRef.closeDropdown) {\r\n        InsidePurChasePriceRef.closeDropdown();\r\n      }\r\n    },\r\n  }\r\n};\r\n</script>\r\n"]}]}