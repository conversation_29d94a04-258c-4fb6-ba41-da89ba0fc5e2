{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\directive\\drag.js", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\directive\\drag.js", "mtime": 1752737748499}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\.babelrc", "mtime": 1752737748316}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["bind", "el", "_ref", "vnode", "modifiers", "value", "style", "position", "x", "y", "addEventListener", "down", "oncontextmenu", "disX", "disY", "e", "which", "offsetX", "offsetY", "document", "move", "up", "l", "clientX", "t", "clientY", "left", "top", "removeEventListener", "split", "window", "innerWidth", "innerHeight", "update", "binding", "unbind"], "sources": ["D:/Users/<USER>/Desktop/dev/sy_declare_ui/src/directive/drag.js"], "sourcesContent": ["/**\r\n * <AUTHOR>\r\n * @create 2021-6-21\r\n * @info 用于实现元素自定义拖拽v-drag\r\n * @example <div v-drag=\"true\">{{ value }}</div>\r\n */\r\nexport default {\r\n  bind: (el, { modifiers, value }, vnode) => {\r\n    el.style.position = \"fixed\";\r\n    let { x, y } = modifiers;\r\n\r\n    el.addEventListener(\"mousedown\", down);\r\n    el.oncontextmenu = function() {\r\n      //禁用鼠标右键默认事件\r\n      return false;\r\n    };\r\n    var disX, disY;\r\n    function down(e) {\r\n      if (e.which == 3) {\r\n        //仅右键能拖动元素\r\n        disX = e.offsetX;\r\n        disY = e.offsetY;\r\n        document.addEventListener(\"mousemove\", move);\r\n        document.addEventListener(\"mouseup\", up);\r\n      }\r\n    }\r\n\r\n    function move(e) {\r\n      var l = e.clientX - disX;\r\n      var t = e.clientY - disY;\r\n      if (value) {\r\n        if (x) {\r\n          el.style.left = l + \"px\";\r\n        }\r\n        if (y) {\r\n          el.style.top = t + \"px\";\r\n        }\r\n\r\n        if ((x && y) || (!x && !y)) {\r\n          el.style.left = l + \"px\";\r\n          el.style.top = t + \"px\";\r\n        }\r\n      }\r\n    }\r\n\r\n    function up(e) {\r\n      // console.log('up');\r\n      document.removeEventListener(\"mousemove\", move);\r\n      document.removeEventListener(\"mouseup\", up);\r\n      const left = el.style.left ? +el.style.left.split(\"px\")[0] : 0;\r\n      const top = el.style.top ? +el.style.top.split(\"px\")[0] : 0;\r\n      //防止边界溢出\r\n      if (left < 0) el.style.left = \"0px\";\r\n      if ((left + 40) > window.innerWidth) el.style.left = window.innerWidth - 40  + \"px\";\r\n      if (top < 0) el.style.top = \"0px\";\r\n      if ((top + 40) > window.innerHeight) el.style.top = window.innerHeight - 40 + \"px\";\r\n    }\r\n  },\r\n  update: (el, binding, vnode) => {},\r\n  unbind: (el, binding) => {}\r\n};\r\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;EACbA,IAAI,EAAE,SAAAA,KAACC,EAAE,EAAAC,IAAA,EAAwBC,KAAK,EAAK;IAAA,IAA9BC,SAAS,GAAAF,IAAA,CAATE,SAAS;MAAEC,KAAK,GAAAH,IAAA,CAALG,KAAK;IAC3BJ,EAAE,CAACK,KAAK,CAACC,QAAQ,GAAG,OAAO;IAC3B,IAAMC,CAAC,GAAQJ,SAAS,CAAlBI,CAAC;MAAEC,CAAC,GAAKL,SAAS,CAAfK,CAAC;IAEVR,EAAE,CAACS,gBAAgB,CAAC,WAAW,EAAEC,IAAI,CAAC;IACtCV,EAAE,CAACW,aAAa,GAAG,YAAW;MAC5B;MACA,OAAO,KAAK;IACd,CAAC;IACD,IAAIC,IAAI,EAAEC,IAAI;IACd,SAASH,IAAIA,CAACI,CAAC,EAAE;MACf,IAAIA,CAAC,CAACC,KAAK,IAAI,CAAC,EAAE;QAChB;QACAH,IAAI,GAAGE,CAAC,CAACE,OAAO;QAChBH,IAAI,GAAGC,CAAC,CAACG,OAAO;QAChBC,QAAQ,CAACT,gBAAgB,CAAC,WAAW,EAAEU,IAAI,CAAC;QAC5CD,QAAQ,CAACT,gBAAgB,CAAC,SAAS,EAAEW,EAAE,CAAC;MAC1C;IACF;IAEA,SAASD,IAAIA,CAACL,CAAC,EAAE;MACf,IAAIO,CAAC,GAAGP,CAAC,CAACQ,OAAO,GAAGV,IAAI;MACxB,IAAIW,CAAC,GAAGT,CAAC,CAACU,OAAO,GAAGX,IAAI;MACxB,IAAIT,KAAK,EAAE;QACT,IAAIG,CAAC,EAAE;UACLP,EAAE,CAACK,KAAK,CAACoB,IAAI,GAAGJ,CAAC,GAAG,IAAI;QAC1B;QACA,IAAIb,CAAC,EAAE;UACLR,EAAE,CAACK,KAAK,CAACqB,GAAG,GAAGH,CAAC,GAAG,IAAI;QACzB;QAEA,IAAKhB,CAAC,IAAIC,CAAC,IAAM,CAACD,CAAC,IAAI,CAACC,CAAE,EAAE;UAC1BR,EAAE,CAACK,KAAK,CAACoB,IAAI,GAAGJ,CAAC,GAAG,IAAI;UACxBrB,EAAE,CAACK,KAAK,CAACqB,GAAG,GAAGH,CAAC,GAAG,IAAI;QACzB;MACF;IACF;IAEA,SAASH,EAAEA,CAACN,CAAC,EAAE;MACb;MACAI,QAAQ,CAACS,mBAAmB,CAAC,WAAW,EAAER,IAAI,CAAC;MAC/CD,QAAQ,CAACS,mBAAmB,CAAC,SAAS,EAAEP,EAAE,CAAC;MAC3C,IAAMK,IAAI,GAAGzB,EAAE,CAACK,KAAK,CAACoB,IAAI,GAAG,CAACzB,EAAE,CAACK,KAAK,CAACoB,IAAI,CAACG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;MAC9D,IAAMF,GAAG,GAAG1B,EAAE,CAACK,KAAK,CAACqB,GAAG,GAAG,CAAC1B,EAAE,CAACK,KAAK,CAACqB,GAAG,CAACE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;MAC3D;MACA,IAAIH,IAAI,GAAG,CAAC,EAAEzB,EAAE,CAACK,KAAK,CAACoB,IAAI,GAAG,KAAK;MACnC,IAAKA,IAAI,GAAG,EAAE,GAAII,MAAM,CAACC,UAAU,EAAE9B,EAAE,CAACK,KAAK,CAACoB,IAAI,GAAGI,MAAM,CAACC,UAAU,GAAG,EAAE,GAAI,IAAI;MACnF,IAAIJ,GAAG,GAAG,CAAC,EAAE1B,EAAE,CAACK,KAAK,CAACqB,GAAG,GAAG,KAAK;MACjC,IAAKA,GAAG,GAAG,EAAE,GAAIG,MAAM,CAACE,WAAW,EAAE/B,EAAE,CAACK,KAAK,CAACqB,GAAG,GAAGG,MAAM,CAACE,WAAW,GAAG,EAAE,GAAG,IAAI;IACpF;EACF,CAAC;EACDC,MAAM,EAAE,SAAAA,OAAChC,EAAE,EAAEiC,OAAO,EAAE/B,KAAK,EAAK,CAAC,CAAC;EAClCgC,MAAM,EAAE,SAAAA,OAAClC,EAAE,EAAEiC,OAAO,EAAK,CAAC;AAC5B,CAAC"}]}