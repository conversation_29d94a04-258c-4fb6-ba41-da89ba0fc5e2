{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\login\\login.vue?vue&type=style&index=0&id=77c14c32&lang=less&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\login\\login.vue", "mtime": 1752737748508}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1752744819993}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpAaW1wb3J0ICIuL2xvZ2luLmxlc3MiOw0K"}, {"version": 3, "sources": ["login.vue"], "names": [], "mappings": ";AACA", "file": "login.vue", "sourceRoot": "src/view/login", "sourcesContent": ["<style lang=\"less\">\r\n@import \"./login.less\";\r\n</style>\r\n\r\n<template>\r\n  <div class=\"login\">\r\n    <div class=\"login-con\">\r\n      <div class=\"login-layout-logo\">\r\n        <img class=\"login-logo\" :src=\"loginLogo\" key=\"login-logo\"  alt=\"珠海穗元服饰有限公司\"/>\r\n      </div>\r\n      <div class=\"login-area\">\r\n        <div class=\"backgroundText\">\r\n          <span>穗元服饰，不是一个人的小情怀</span>\r\n          <br />\r\n          <span>而是一群人的光荣与梦想</span>\r\n          <br />\r\n          <span>一个行业的机遇和使命</span>\r\n        </div>\r\n        <div class=\"login-area-center\">\r\n          <div id=\"wx_icon\"></div>\r\n          <div class=\"right-login\">\r\n            <div class=\"login-title\">穗元服饰账号登录</div>\r\n            <div v-show=\"!checkStatus\" class=\"form-con\">\r\n              <Form ref=\"loginForm\" :model=\"form\" :rules=\"rules\" @keydown.enter.native=\"handleSubmit\" style=\"position: relative\">\r\n                <FormItem prop=\"username\">\r\n                  <div class=\"input-box\">\r\n                    <div class=\"title\">账号</div>\r\n                    <input type=\"text\" v-model=\"form.username\" />\r\n                  </div>\r\n                </FormItem>\r\n                <FormItem prop=\"password\">\r\n                  <div class=\"input-box\">\r\n                    <div class=\"title\">密码</div>\r\n                    <input :type=\"passwordType\" class=\"password\" v-model=\"form.password\"/>\r\n                    <div class=\"eye\">\r\n                      <img :src=\"eyeImg\" @click=\"changeEyeStatus\"  alt=\"查看密码\"/>\r\n                    </div>\r\n                  </div>\r\n                  <Checkbox class=\"autoLogin\" v-model=\"form.auto\">自动登录</Checkbox>\r\n                </FormItem>\r\n                <FormItem>\r\n                  <Button @click=\"handleSubmit\" :size=\"buttonSize\" :loading=\"loading\" :long=\"true\" style=\"background:#1C6BBA;color:white\">登录</Button>\r\n                </FormItem>\r\n              </Form>\r\n            </div>\r\n            <div class=\"popTip\" v-show=\"checkStatus\">\r\n              <div style=\"margin-bottom: 5px\">\r\n                <a @click=\"closeCheck\">\r\n                  <Icon size=\"12\" type=\"md-arrow-back\" />返回\r\n                </a>\r\n              </div>\r\n              <div>\r\n                <slide-verify ref=\"slideBlock\" v-show=\"needCheck\" class=\"slide-box\" style=\"width: 280px\" :r=\"8\" :l=\"32\" :w=\"280\" :h=\"120\" :imgs=\"loginImg\"\r\n                  @success=\"handleCheckSuccess\" @fail=\"handleCheckFail\" slider-text=\"向右滑动完成验证\"></slide-verify>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"footer-area\">\r\n        <div class=\"login-footer-copyright\">\r\n          Copyright ©️ SuiYun.cn. All rights reserved\r\n        </div>\r\n        <div class=\"login-footer-copyright\">\r\n          珠海穗元服饰有限公司-中国服饰行业领跑者 <br/>联系方式：400-660-2205\r\n          email：<EMAIL>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapActions } from \"vuex\";\r\nimport loginLogo from \"@/assets/images/login-logo.png\";\r\nimport { setToken } from \"@/libs/util\";\r\nimport { getRequest } from \"@/libs/axios.js\";\r\nimport check1 from \"@/assets/images/loginImg/check1.jpg\";\r\nimport check2 from \"@/assets/images/loginImg/check2.jpg\";\r\nimport check3 from \"@/assets/images/loginImg/check3.jpg\";\r\nimport check4 from \"@/assets/images/loginImg/check4.jpg\";\r\nimport check5 from \"@/assets/images/loginImg/check5.jpg\";\r\nimport check6 from \"@/assets/images/loginImg/check6.jpg\";\r\n\r\nexport default {\r\n  name: \"LoginForm\",\r\n  props: {\r\n    usernameRules: {\r\n      type: Array,\r\n      default: () => {\r\n        return [{ required: true, message: \"账号不能为空\", trigger: \"blur\" }];\r\n      }\r\n    },\r\n    passwordRules: {\r\n      type: Array,\r\n      default: () => {\r\n        return [{ required: true, message: \"密码不能为空\", trigger: \"blur\" }];\r\n      }\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      loginImg: [check1, check2, check3, check4, check5, check6],\r\n      loginLogo,\r\n      buttonSize: \"large\",\r\n      passwordType: \"password\",\r\n      eyeStatus: true,\r\n      eyeImg: require(\"@/assets/icons/eye.png\"),\r\n      //新滑动验证\r\n      checkStatus: false,\r\n      needCheck: false,\r\n      errTimes: 0, //输入密码错误的次数\r\n      checkResult: false, //滑动验证的结果\r\n      //新滑动验证结束\r\n      form: {\r\n        username: \"\",\r\n        password: \"\",\r\n        auto: false\r\n      },\r\n      config: {}\r\n    };\r\n  },\r\n  computed: {\r\n    rules() {\r\n      return {\r\n        username: this.usernameRules,\r\n        password: this.passwordRules\r\n      };\r\n    }\r\n  },\r\n  watch: {\r\n    eyeStatus(val) {\r\n      if (val) {\r\n        this.eyeImg = require(\"@/assets/icons/eye.png\");\r\n        this.passwordType = \"password\";\r\n      } else {\r\n        this.eyeImg = require(\"@/assets/icons/eye-active.png\");\r\n        this.passwordType = \"Text\";\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    ...mapActions([\"handleLogin\", \"getUserInfo\"]),\r\n    //返回输入登录信息\r\n    closeCheck() {\r\n      this.checkStatus = false;\r\n    },\r\n    //验证通过\r\n    handleCheckSuccess() {\r\n      this.checkResult = true;\r\n      this.$refs.loginForm.validate(valid => {\r\n        if (valid) {\r\n          this.loading = true;\r\n          let username = this.form.username;\r\n          let password = this.form.password;\r\n          let auto = this.form.auto;\r\n          this.handleLogin({ username, password, auto })\r\n            .then(res => {\r\n              if (res && res['code'] === 0) {\r\n                this.$router.push({ name: this.$config.homeName });\r\n                localStorage.setItem(\"needCheck\", \"false\");\r\n                this.needCheck = false;\r\n                this.checkStatus = false;\r\n                this.errTimes = 0;\r\n              }}).catch(err => {\r\n                if (err) {\r\n                this.errTimes++;\r\n                this.checkStatus = false;\r\n                this.$refs.slideBlock.reset();\r\n                this.checkResult = false;\r\n                if (this.errTimes > 2) {\r\n                  localStorage.setItem(\"needCheck\", \"true\");\r\n                  this.needCheck = true;\r\n                }\r\n              }}).finally(() => {this.loading = false;});\r\n        }\r\n      });\r\n    },\r\n    //验证失败\r\n    handleCheckFail() {\r\n      this.checkResult = false;\r\n    },\r\n    //密码是否可见\r\n    changeEyeStatus() {\r\n      this.eyeStatus = !this.eyeStatus;\r\n    },\r\n    //点击登录\r\n    handleSubmit() {\r\n      if (this.needCheck === true) {\r\n        this.$refs.loginForm.validate(valid => {\r\n          if (valid) {\r\n            this.checkStatus = true;\r\n          }\r\n        });\r\n      } else {\r\n        this.$refs.loginForm.validate(valid => {\r\n          if (valid) {\r\n            this.loading = true;\r\n            let username = this.form.username;\r\n            let password = this.form.password;\r\n            let auto = this.form.auto;\r\n            this.handleLogin({ username, password, auto })\r\n              .then(res => {\r\n\r\n                if (res && res['code'] === 0) {\r\n                  this.$router.push({ name: this.$config.homeName });\r\n                  localStorage.setItem(\"needCheck\", \"false\");\r\n                  this.needCheck = false;\r\n                  this.errTimes = 0;\r\n                }}).catch(err => {\r\n                  if (err) {\r\n                    this.errTimes++;\r\n                    this.checkStatus = false;\r\n                    this.$refs.slideBlock.reset();\r\n                    this.checkResult = false;\r\n                    if (this.errTimes > 2) {\r\n                      localStorage.setItem(\"needCheck\", \"true\");\r\n                      this.needCheck = true;\r\n                    }\r\n                }})\r\n              .finally(() => {this.loading = false;});\r\n          }});\r\n      }\r\n    },\r\n    handleAuthLogin() {\r\n      let token = this.$route.query.token;\r\n      let UserId = this.$route.query.UserId;\r\n      if (typeof token === \"undefined\") {\r\n      } else {\r\n        getRequest(\"/base/login/checkThirdIp\", { UserId: UserId })\r\n          .then(res => {\r\n            if (res[\"code\"] === \"0\") {\r\n              setToken(token);\r\n              this.$router.push({ name: this.$config.homeName });\r\n            }\r\n          })\r\n          .finally(() => {\r\n            this.loading = false;\r\n          });\r\n      }\r\n    }\r\n  },\r\n  mounted: function() {\r\n    this.handleAuthLogin();\r\n    this.needCheck = localStorage.getItem(\"needCheck\")\r\n      ? localStorage.getItem(\"needCheck\") === \"true\" : false;\r\n  }\r\n};\r\nwindow.onload = function() {\r\n  const redirect_uri =\r\n    process.env.NODE_ENV === \"production\"\r\n      ? encodeURIComponent(\"https://amz.i-suiyuan.com/api-v1.0/base/oauth/qiyewechat/callback\")\r\n      : undefined;\r\n  window.WwLogin({\r\n    id: \"wx_icon\",\r\n    appid: \"ww41fa91e90d30fdd8\",\r\n    agentid: \"1000002\",\r\n    redirect_uri: redirect_uri,\r\n    state: \"2241241241\",\r\n    href: location.origin + \"/QR_code.css\"\r\n  });\r\n};\r\n</script>\r\n<style></style>\r\n"]}]}