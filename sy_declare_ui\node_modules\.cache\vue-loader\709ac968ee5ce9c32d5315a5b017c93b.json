{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\base\\gateway\\route\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\base\\gateway\\route\\index.vue", "mtime": 1752737748511}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AA2FA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/view/module/base/gateway/route", "sourcesContent": ["<template>\r\n  <div>\r\n    <Card :shadow=\"true\">\r\n      <div class=\"search-con search-con-top\">\r\n        <ButtonGroup>\r\n          <Button :disabled=\"!hasAuthority('RouteAdd')\" class=\"search-btn\" type=\"primary\"\r\n                  @click=\"handleModal()\">\r\n            <span>添加</span>\r\n          </Button>\r\n        </ButtonGroup>\r\n      </div>\r\n      <Alert :show-icon=\"true\">谨慎添加或修改路由,如果修改不当,将影响正常访问！&nbsp;<a @click=\"handleRefreshGateway\">手动刷新网关</a></Alert>\r\n      <Table :border=\"true\" :columns=\"columns\" :max-height=\"autoTableHeight($refs.autoTableRef)\" ref=\"autoTableRef\" :data=\"data\" :loading=\"loading\">\r\n        <template v-slot:status=\"{ row }\">\r\n          <Badge v-if=\"row.status===0\" status=\"success\" text=\"启用\"/>\r\n          <Badge v-else status=\"error\" text=\"禁用\"/>\r\n        </template>\r\n        <template v-slot:routeType=\"{ row }\">\r\n          <span v-if=\"!!row.serviceId\"><Tag color=\"green\">负载均衡</Tag>{{row.serviceId}}</span>\r\n          <span v-else-if=\"!!row.url\"><Tag color=\"blue\">反向代理</Tag>{{row.url}}</span>\r\n        </template>\r\n\r\n        <template v-slot:action=\"{ row }\">\r\n          <a v-if=\"hasAuthority('RouteEdit')\" @click=\"handleModal(row)\">\r\n            编辑</a>&nbsp;\r\n          <a v-if=\"hasAuthority('RouteDel')\" @click=\"handleRemove(row)\">\r\n            删除</a>&nbsp;\r\n        </template>\r\n      </Table>\r\n      <Page :total=\"pageInfo.total\" size=\"small\" :current=\"pageInfo.page\" :page-size=\"pageInfo.limit\"\r\n            :show-elevator=\"true\" :show-sizer=\"true\" :show-total=\"true\"\r\n            @on-change=\"handlePage\" @on-page-size-change='handlePageSize'></Page>\r\n    </Card>\r\n    <Modal v-model=\"modalVisible\"\r\n           :title=\"modalTitle\"\r\n           width=\"40\"\r\n           @on-cancel=\"handleReset\">\r\n      <div>\r\n        <Form ref=\"routeForm\" :model=\"formItem\" :rules=\"formItemRules\" :label-width=\"100\">\r\n          <FormItem label=\"路由名称\" prop=\"routeDesc\">\r\n            <Input v-model=\"formItem.routeDesc\" placeholder=\"请输入内容\"></Input>\r\n          </FormItem>\r\n          <FormItem label=\"路由标识\" prop=\"routeName\">\r\n            <Input v-model=\"formItem.routeName\" placeholder=\"默认使用服务名称{application.name}\"></Input>\r\n          </FormItem>\r\n          <FormItem label=\"路由前缀\" prop=\"path\">\r\n            <Input v-model=\"formItem.path\" placeholder=\"/{path}/**\"></Input>\r\n          </FormItem>\r\n          <FormItem label=\"路由方式\">\r\n            <Select v-model=\"selectType\">\r\n              <Option value=\"service\" label=\"负载均衡(serviceId)\"></Option>\r\n              <Option value=\"url\" label=\"反向代理(url)\"></Option>\r\n            </Select>\r\n          </FormItem>\r\n          <FormItem v-if=\"selectType==='service'\" label=\"负载均衡\" prop=\"serviceId\"\r\n                    :rules=\"{required: true, message: '服务名称不能为空', trigger: 'blur'}\">\r\n            <Input v-model=\"formItem.serviceId\" placeholder=\"服务名称application.name\"></Input>\r\n          </FormItem>\r\n          <FormItem v-if=\"selectType==='url'\" label=\"反向代理\" prop=\"url\"\r\n                    :rules=\"[{required: true, message: '服务地址不能为空', trigger: 'blur'},{type: 'url', message: '请输入有效网址', trigger: 'blur'}]\">\r\n            <Input v-model=\"formItem.url\" placeholder=\"http://localhost:8080\"></Input>\r\n          </FormItem>\r\n          <FormItem label=\"忽略前缀\">\r\n            <RadioGroup v-model=\"formItem.stripPrefix\" type=\"button\">\r\n              <Radio label=\"0\">否</Radio>\r\n              <Radio label=\"1\">是</Radio>\r\n            </RadioGroup>\r\n          </FormItem>\r\n          <FormItem label=\"失败重试\">\r\n            <RadioGroup v-model=\"formItem.retryable\" type=\"button\">\r\n              <Radio label=\"0\">否</Radio>\r\n              <Radio label=\"1\">是</Radio>\r\n            </RadioGroup>\r\n          </FormItem>\r\n          <FormItem label=\"状态\">\r\n            <RadioGroup v-model=\"formItem.status\" type=\"button\">\r\n              <Radio label=\"0\">启用</Radio>\r\n              <Radio label=\"1\">禁用</Radio>\r\n            </RadioGroup>\r\n          </FormItem>\r\n        </Form>\r\n        <div class=\"drawer-footer\">\r\n          <Button type=\"default\" @click=\"handleReset\">取消</Button>&nbsp;\r\n          <Button type=\"primary\" @click=\"handleSubmit\" :loading=\"saving\"  v-if=\"hasAuthority('RouteEdit') ||hasAuthority('RouteAdd')\">保存</Button>\r\n        </div>\r\n      </div>\r\n    </Modal>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport route from '@/api/base/gateway/route'\r\nimport gatewayLog from '@/api/base/gateway/gatewayLog'\r\nimport {autoTableHeight} from '@/libs/tools.js'\r\n\r\nexport default {\r\n    name: 'gatewayRoute',\r\n    data () {\r\n      return {\r\n        autoTableHeight,\r\n        loading: false,\r\n        saving: false,\r\n        modalVisible: false,\r\n        modalTitle: '',\r\n        pageInfo: {\r\n          total: 0,\r\n          page: 1,\r\n          limit: 10\r\n        },\r\n        selectType: 'service',\r\n        selectServiceList: [],\r\n        formItemRules: {\r\n          routeDesc: [\r\n            {required: true, message: '路由名称不能为空', trigger: 'blur'}\r\n          ],\r\n          routeName: [\r\n            {required: true, message: '路由标识不能为空', trigger: 'blur'}\r\n          ],\r\n          path: [\r\n            {required: true, message: '路由前缀不能为空', trigger: 'blur'}\r\n          ]\r\n        },\r\n        formItem: {\r\n          id: '',\r\n          path: '',\r\n          serviceId: '',\r\n          url: '',\r\n          stripPrefix: 0,\r\n          retryable: 0,\r\n          status: 0,\r\n          routeName: '',\r\n          routeDesc: ''\r\n        },\r\n        columns: [\r\n          {\r\n            title: '路由名称',\r\n            key: 'routeDesc',\r\n            width: 300\r\n          },\r\n          {\r\n            title: '路由标识',\r\n            key: 'routeName',\r\n            width: 300\r\n\r\n          },\r\n          {\r\n            title: '路由前缀',\r\n            key: 'path',\r\n            width: 200\r\n          },\r\n          {\r\n            title: '路由方式',\r\n            slot: 'routeType',\r\n            width: 300\r\n          },\r\n          {\r\n            title: '忽略前缀',\r\n            key: 'stripPrefix'\r\n          },\r\n          {\r\n            title: '失败重试',\r\n            key: 'retryable'\r\n          },\r\n          {\r\n            title: '状态',\r\n            key: 'status',\r\n            slot: 'status'\r\n          },\r\n          {\r\n            title: '操作',\r\n            slot: 'action',\r\n            fixed: 'right',\r\n            width: 120\r\n          }\r\n        ],\r\n        data: []\r\n      }\r\n    },\r\n    methods: {\r\n      handleModal (data) {\r\n        if (data) {\r\n          this.modalTitle = '编辑路由'\r\n          this.formItem = Object.assign({}, this.formItem, data)\r\n        } else {\r\n          this.modalTitle = '添加路由'\r\n        }\r\n        this.formItem.status = this.formItem.status + ''\r\n        this.formItem.stripPrefix = this.formItem.stripPrefix + ''\r\n        this.formItem.retryable = this.formItem.retryable + ''\r\n        this.formItem.url = this.formItem.service ? '' : this.formItem.url\r\n        this.formItem.service = this.formItem.url ? '' : this.formItem.service\r\n        this.selectType = this.formItem.url ? 'url' : 'service'\r\n        this.modalVisible = true\r\n      },\r\n      handleReset () {\r\n        this.formItem = {\r\n          id: '',\r\n          path: '',\r\n          serviceId: '',\r\n          url: '',\r\n          stripPrefix: 0,\r\n          retryable: 0,\r\n          status: 0,\r\n          routeName: '',\r\n          routeDesc: ''\r\n        }\r\n        //重置验证\r\n        this.$refs['routeForm'].resetFields()\r\n        this.modalVisible = false\r\n        this.saving = false\r\n      },\r\n      handleSubmit () {\r\n        this.$refs['routeForm'].validate((valid) => {\r\n          if (valid) {\r\n            this.saving = true\r\n            if (this.formItem.id) {\r\n              route.updateRoute(this.formItem).then(res => {\r\n                if (res['code'] === 0) {\r\n                  this.$Message.success('保存成功')\r\n                  this.handleReset()\r\n                }\r\n                this.handleSearch()\r\n              }).finally(() => {\r\n                this.saving = false\r\n              })\r\n            } else {\r\n              route.addRoute(this.formItem).then(res => {\r\n                this.handleReset()\r\n                this.handleSearch()\r\n                if (res['code'] === 0) {\r\n                  this.$Message.success('保存成功')\r\n                }\r\n              }).finally(() => {\r\n                this.saving = false\r\n              })\r\n            }\r\n          }\r\n        })\r\n      },\r\n      handleSearch (page) {\r\n        if (page) {\r\n          this.pageInfo.page = page\r\n        }\r\n        this.loading = true\r\n        route.listPage({page: this.pageInfo.page, limit: this.pageInfo.limit}).then(res => {\r\n          this.data = res.data.records\r\n          this.pageInfo.total = parseInt(res.data.total)\r\n        }).finally(() => {\r\n          this.loading = false\r\n        })\r\n      },\r\n      handlePage (current) {\r\n        this.pageInfo.page = current\r\n        this.handleSearch()\r\n      },\r\n      handlePageSize (size) {\r\n        this.pageInfo.limit = size\r\n        this.handleSearch()\r\n      },\r\n      handleRemove (data) {\r\n        this.$Modal.confirm({\r\n          title: '确定删除吗？',\r\n          onOk: () => {\r\n            route.removeRoute(data.id).then(res => {\r\n              if (res['code'] === 0) {\r\n                this.pageInfo.page = 1\r\n                this.$Message.success('删除成功')\r\n              }\r\n              this.handleSearch()\r\n            })\r\n          }\r\n        })\r\n      },\r\n      handleRefreshGateway () {\r\n        this.$Modal.confirm({\r\n          title: '提示',\r\n          content: '将重新加载所有网关实例包括（访问权限、流量限制、IP访问限制、路由缓存），是否继续？',\r\n          onOk: () => {\r\n            gatewayLog.refreshGateway().then(res => {\r\n              if (res['code'] === 0) {\r\n                this.$Message.success('刷新成功')\r\n              }\r\n            })\r\n          }\r\n        })\r\n      }\r\n    },\r\n    mounted: function () {\r\n      this.handleSearch()\r\n    }\r\n  }\r\n</script>\r\n"]}]}