{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\basf\\shop\\index.vue?vue&type=style&index=0&id=77c54dd8&lang=less&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\basf\\shop\\index.vue", "mtime": 1752737748516}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1752744819993}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouc2hvcE1hbmFnZSB7DQogIC5zZWFyY2hGb3JtIHsNCiAgICBwb3NpdGlvbjogcmVsYXRpdmU7DQogICAgcGFkZGluZy1yaWdodDogMjAwcHg7DQogICAgLnJpZ2h0QnRuIHsNCiAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCiAgICAgIHRvcDogMDsNCiAgICAgIHJpZ2h0OiAwOw0KICAgIH0NCiAgfQ0KICAuaXZ1LXRhYmxlLWNlbGwgew0KICAgIHBhZGRpbmctbGVmdDogOHB4Ow0KICAgIHBhZGRpbmctcmlnaHQ6IDhweDsNCiAgfQ0KfQ0KLnNob3BNYW5hZ2VFZGl0TW9kYWwgew0KICAuaXZ1LW1vZGFsIHsNCiAgICB0b3A6IDMwcHg7DQogICAgLml2dS1tb2RhbC1ib2R5IHsNCiAgICAgIG1heC1oZWlnaHQ6IDcyMHB4Ow0KICAgICAgb3ZlcmZsb3c6IGF1dG87DQogICAgfQ0KICB9DQogIC5zZWxsZXJTZWxlY3RJdGVtIHsNCiAgICBwb3NpdGlvbjogcmVsYXRpdmU7DQogICAgLml2dS1zZWxlY3QtbXVsdGlwbGUgLml2dS10YWcgc3Bhbjpub3QoLml2dS1zZWxlY3QtbWF4LXRhZykgew0KICAgICAgbWF4LXdpZHRoOiA0NXB4Ow0KICAgIH0NCiAgICAuY2xvc2VJY29uIHsNCiAgICAgIGNvbG9yOiAjY2NjOw0KICAgICAgd2lkdGg6IDE2cHg7DQogICAgICBwb3NpdGlvbjogYWJzb2x1dGU7DQogICAgICB0b3A6IDA7DQogICAgICByaWdodDogODBweDsNCiAgICAgIGZvbnQtc2l6ZTogOXB4Ow0KICAgICAgJjpob3ZlciB7DQogICAgICAgIGN1cnNvcjogcG9pbnRlcjsNCiAgICAgICAgY29sb3I6ICNlNTM5MzU7DQogICAgICB9DQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AA4kBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/view/module/basf/shop", "sourcesContent": ["<template>\r\n  <div>\r\n    <Card class=\"shopManage\" :shadow=\"true\">\r\n      <Form ref=\"searchForm\" class=\"searchForm\" :model=\"pageInfo\" inline @submit.native.prevent>\r\n        <FormItem prop=\"platformId\">\r\n          <Select type=\"text\" v-model=\"pageInfo.platformId\" placeholder=\"平台名称\" @on-change=\"platformChange\" style=\"width:160px\" >\r\n            <Option v-for=\"(item, index) in platformList\" :value=\"item.id\" :key=\"index\" >{{ item.name }}</Option >\r\n          </Select>\r\n        </FormItem>\r\n        <FormItem prop=\"siteId\">\r\n          <Select v-model=\"pageInfo.siteId\" placeholder=\"站点名称\" style=\"width:160px\" >\r\n            <Option v-for=\"(item, index) in siteList\" :value=\"item.id\" :key=\"index\" >{{ item.name }}</Option >\r\n          </Select>\r\n        </FormItem>\r\n        <FormItem prop=\"name\">\r\n          <Input type=\"text\" placeholder=\"店铺名称\" v-model=\"pageInfo.name\" />\r\n        </FormItem>\r\n        <FormItem prop=\"aliaName\">\r\n          <Input type=\"text\" placeholder=\"店铺编号\" v-model=\"pageInfo.aliaName\"/>\r\n        </FormItem>\r\n        <FormItem prop=\"status\">\r\n          <Select v-model=\"pageInfo.status\" placeholder=\"店铺状态\" style=\"width:160px\">\r\n            <Option v-for=\"v in statusOps\" :value=\"v.key\" :key=\"v.key\">{{v.name }}</Option>\r\n          </Select>\r\n        </FormItem>\r\n        <FormItem prop=\"relate\">\r\n          <Select v-model=\"pageInfo.relate\" placeholder=\"关联领星\" style=\"width:160px\">\r\n            <Option v-for=\"v in relateOps\" :value=\"v.key\" :key=\"v.key\">{{v.name }}</Option>\r\n          </Select>\r\n        </FormItem>\r\n        <FormItem class=\"rightBtn\" :label-width=\"20\">\r\n          <Button type=\"primary\" @click=\"handleSearch(1)\">查询</Button>&nbsp;\r\n          <Button @click=\"handleResetForm('searchForm')\">重置</Button>\r\n        </FormItem>\r\n      </Form>\r\n      <div class=\"search-con search-con-top\" style=\"padding-bottom: 10px\">\r\n        <Button type=\"primary\" @click=\"handleModal()\" v-if=\"hasAuthority('shopAdd')\">添加</Button>\r\n        <Button @click=\"syncShop()\" style=\"margin-left: 15px\" :loading=\"loading\" v-if=\"hasAuthority('shopSync')\">同步</Button>\r\n      </div>\r\n      <Table :border=\"true\" ref=\"autoTableRef\" :max-height=\"autoTableHeight($refs.autoTableRef)\" :columns=\"columns\"\r\n             :data=\"data\" :loading=\"loading\">\r\n        <template v-slot:status=\"{ row }\">\r\n          <Badge v-for=\"v in statusOps\" :text=\"v.name\" v-if=\"v.key === row.status\"\r\n                 :status=\"v.key === 0?'success':'warning'\" v-bind:key=\"v.key\"></Badge>\r\n        </template>\r\n        <template v-slot:action=\"{ row }\">\r\n          <a v-if=\"hasAuthority('shopEdit')\" @click=\"handleModal(row)\">编辑</a>&nbsp;\r\n          <a @click=\"handleClick('view',row)\">查看</a>&nbsp;\r\n          <a v-if=\"hasAuthority('shopEdit')\" @click=\"handleClick('remove',row)\">{{row.status === 0?\"停用\":(row.status === 1?\"启用\":\"解锁\")}}</a>\r\n          <!--<a @click=\"handleAuth(row)\">AWM授权</a>&nbsp;-->\r\n        </template>\r\n      </Table>\r\n      <Page :total=\"pageInfo.total\" size=\"small\" :current=\"pageInfo.page\" :page-size=\"pageInfo.limit\" :show-elevator=\"true\"\r\n            :show-sizer=\"true\" :show-total=\"true\" @on-change=\"handlePage\" @on-page-size-change='handlePageSize'></Page>\r\n    </Card>\r\n    <Modal v-model=\"modalVisible\" :title=\"modalTitle\" @on-cancel=\"handleReset\" width=\"765px\" class-name=\"shopManageEditModal\">\r\n      <Form ref=\"form\" :model=\"formItem\" :rules=\"formItemRules\" :label-width=\"100\">\r\n        <FormItem label=\"平台名称\" prop=\"platformId\">\r\n          <Select v-model=\"formItem.platformId\" @on-change=\"change\" :disabled=\"actionType==='view'\">\r\n            <Option v-for=\"(item, index) in platformList\" :value=\"item.id\" :key=\"index\" >{{ item.name }}</Option >\r\n          </Select>\r\n        </FormItem>\r\n        <FormItem label=\"站点名称\" prop=\"siteId\" :rules=\"siteRule\">\r\n          <Select v-model=\"formItem.siteId\" :disabled=\"actionType==='view'\">\r\n            <Option v-for=\"(item, index) in siteList\" :value=\"item.id\" :key=\"index\" >{{ item.name }}</Option >\r\n          </Select>\r\n        </FormItem>\r\n        <FormItem label=\"店铺名称\" prop=\"name\">\r\n          <Input v-model=\"formItem.name\" placeholder=\"请输入内容\"></Input>\r\n        </FormItem>\r\n        <FormItem label=\"店铺编号\" prop=\"aliaName\">\r\n          <Input v-model=\"formItem.aliaName\" placeholder=\"请输入内容\"></Input>\r\n        </FormItem>\r\n        <FormItem label=\"客户编号\" prop=\"erpCustNo\">\r\n          <Input v-model=\"formItem.erpCustNo\" placeholder=\"请输入内容\"></Input>\r\n        </FormItem>\r\n        <FormItem prop=\"managed_user\" label=\"店铺负责人\" class=\"sellerSelectItem\">\r\n          <Select type=\"text\" v-model=\"formItem.managedUser\" :filterable=\"true\" style=\"width: 555px\" placeholder=\"请选择\" :transfer=\"true\" >\r\n            <Option v-for=\"item in usersOptions\" :value=\"item.userId\" :key=\"item.userId\" >{{ item.nickName }}</Option >\r\n          </Select>\r\n          <Button type=\"dashed\" @click=\"openPerson\" style=\"margin-left: 3px\" size=\"default\" >选择</Button >\r\n          <div class=\"closeIcon\" v-show=\"!!formItem.managedUser\" @click=\"() => (formItem.managedUser = undefined)\" >\r\n            <Icon type=\"md-close\" size=\"14\" />\r\n          </div>\r\n          <person-select :visible=\"personVisible\" :onCancel=\"() => (personVisible = false)\"\r\n                         @setPerson=\"arr => (formItem.managedUser = arr.map(v => v.id)[0])\"\r\n                         @setSelectInfo=\"setSelectInfo\" ref=\"personSelectRef\" groupName=\"shopmanage_operateuser_config\" :isQuery=\"true\" />\r\n        </FormItem>\r\n        <FormItem label=\"手机号码\" prop=\"phone\">\r\n          <Input v-model=\"formItem.phone\" maxlength=\"11\" width=\"100%\" />\r\n        </FormItem>\r\n        <FormItem label=\"状态\" prop=\"remark\">\r\n          <RadioGroup v-model=\"formItem.status\" type=\"button\">\r\n            <Radio v-for=\"v in statusOps\" :label=\"v.key\" v-if=\"v.key !== -1\" v-bind:key=\"v.key\">{{ v.name }}</Radio>\r\n          </RadioGroup>\r\n        </FormItem>\r\n        <FormItem label=\"备注\" prop=\"remark\">\r\n          <Input v-model=\"formItem.remark\" type=\"textarea\" placeholder=\"请输入内容\"></Input>\r\n        </FormItem>\r\n      </Form>\r\n      <div class=\"drawer-footer\">\r\n        <Button type=\"default\" @click=\"handleReset\">取消</Button>&nbsp;\r\n        <Button type=\"primary\" @click=\"handleSubmit\" :loading=\"saving\" >保存</Button >\r\n      </div>\r\n    </Modal>\r\n    <Modal v-model=\"modalViewVisible\" :title=\"modalTitle\" @on-cancel=\"handleReset\" width=\"765px\">\r\n      <Form ref=\"viewForm\" :model=\"formItem\" :label-width=\"100\">\r\n        <FormItem label=\"平台名称\" prop=\"name\">\r\n          <Input v-model=\"formItem.platform\" placeholder=\"\" :readonly=\"true\"></Input>\r\n        </FormItem>\r\n        <FormItem label=\"站点名称\" prop=\"siteName\">\r\n          <Input v-model=\"formItem.site\" placeholder=\"\" :readonly=\"true\"></Input>\r\n        </FormItem>\r\n        <FormItem label=\"店铺名称\" prop=\"name\">\r\n          <Input v-model=\"formItem.name\" placeholder=\"\" :readonly=\"true\"></Input>\r\n        </FormItem>\r\n        <FormItem label=\"店铺编号\" prop=\"aliaName\">\r\n          <Input v-model=\"formItem.aliaName\" placeholder=\"请输入内容\" :readonly=\"true\"></Input>\r\n        </FormItem>\r\n        <FormItem label=\"客户编号\" prop=\"erpCustNo\">\r\n          <Input v-model=\"formItem.erpCustNo\" placeholder=\"请输入内容\" :readonly=\"true\"></Input>\r\n        </FormItem>\r\n        <FormItem label=\"店铺负责人\" prop=\"managedUserName\">\r\n          <Input v-model=\"formItem.managedUserName\" placeholder=\"请输入内容\" :readonly=\"true\"></Input>\r\n        </FormItem>\r\n        <FormItem label=\"手机号码\" prop=\"phone\">\r\n          <Input v-model=\"formItem.phone\" placeholder=\"\" :readonly=\"true\" />\r\n        </FormItem>\r\n        <FormItem label=\"状态\">\r\n          <RadioGroup v-model=\"formItem.status\" type=\"button\" :readonly=\"true\">\r\n            <Radio v-for=\"v in statusOps\" :label=\"v.key\" v-if=\"v.key !== -1\" v-bind:key=\"v.key\">{{ v.name }}</Radio>\r\n          </RadioGroup>\r\n        </FormItem>\r\n        <FormItem label=\"备注\" prop=\"remark\">\r\n          <Input v-model=\"formItem.remark\" type=\"textarea\" :readonly=\"true\"></Input>\r\n        </FormItem>\r\n      </Form>\r\n      <div slot=\"footer\"></div>\r\n    </Modal>\r\n  </div>\r\n</template>\r\n<script>\r\n\r\nimport personSelect from \"@/components/person-select-radio/index.vue\";\r\nimport Shop from \"@/api/basf/shop\";\r\nimport Platform from \"@/api/basf/platform\";\r\nimport Site from \"@/api/basf/site\";\r\nimport {autoTableHeight} from \"@/libs/tools.js\";\r\nimport Common from '@/api/basic/common'\r\n\r\nexport default {\r\n  name: \"shopList\",\r\n  components: {\r\n    personSelect\r\n  },\r\n  data() {\r\n    return {\r\n      autoTableHeight,\r\n      statusOps: Common.userStatusOps,\r\n      relateOps: Common.relateOps,\r\n      actionType:'view',\r\n      personVisible: false,\r\n      loading: false,\r\n      saving: false,\r\n      modalVisible: false,\r\n      modalViewVisible: false,\r\n      shopNameShow: false,\r\n      siteRule: { required: false },\r\n      readName: false,\r\n      modalTitle: \"\",\r\n      platformList: [],\r\n      selectTreeData: [],\r\n      siteList: [],\r\n      selectPersons: [],\r\n      pageInfo: {\r\n        total: 0,\r\n        page: 1,\r\n        limit: 10,\r\n        status: -1,\r\n        relate: -1,\r\n        platformId:-1,\r\n        siteId:-1,\r\n        name: \"\",\r\n        aliaName: \"\"\r\n      },\r\n      formItemRules: {\r\n        name: [\r\n          { required: true, message: \"店铺名称不能为空\", trigger: \"blur\" }\r\n        ],\r\n        platformId: [\r\n          { required: true, message: \"所属平台不能为空\", trigger: \"blur\" }\r\n        ],\r\n        aliaName: [\r\n          { required: true, message: \"店铺编号不能为空\", trigger: \"blur\" }\r\n        ],\r\n        departmentId: [{ required: true, message: \"部门不能为空\" }]\r\n      },\r\n      formItem: {\r\n        id: \"\",\r\n        platformId: \"\",\r\n        platform: \"\",\r\n        siteId: \"\",\r\n        site: \"\",\r\n        name: \"\",\r\n        aliaName: \"\",\r\n        erpCustNo: \"\",\r\n        managedUserName:\"\",\r\n        phone:\"\",\r\n        status: 0,\r\n        remark: \"\"\r\n      },\r\n      columns: [\r\n        {\r\n          type: \"selection\",\r\n          maxWidth: 40\r\n        },\r\n        {\r\n          title: \"平台名称\",\r\n          key: \"platform\",\r\n          minWidth: 120,\r\n          align: \"center\"\r\n        },\r\n        {\r\n          title: \"站点名称\",\r\n          key: \"site\",\r\n          minWidth: 100,\r\n          align: \"center\"\r\n        },\r\n        {\r\n          title: \"店铺名称\",\r\n          key: \"name\",\r\n          minWidth: 150,\r\n          align: \"center\"\r\n        },\r\n        {\r\n          title: \"店铺编号\",\r\n          key: \"aliaName\",\r\n          minWidth: 200,\r\n          align: \"center\"\r\n        },\r\n        {\r\n          title: \"客户编号\",\r\n          key: \"erpCustNo\",\r\n          minWidth: 150,\r\n          align: \"center\"\r\n        },\r\n        {\r\n          title: \"店铺负责人\",\r\n          key: \"managedUserName\",\r\n          width: 110,\r\n          align: \"center\",\r\n          render: (_, { row }) => (\r\n              <div v-copytext={row.managedUserName}>{row.managedUserName}</div>\r\n          )\r\n        },\r\n        {\r\n          title: \"手机号码\",\r\n          key: \"phone\",\r\n          minWidth: 160,\r\n          align: \"center\"\r\n        },\r\n        {\r\n          title: \"状态\",\r\n          key: \"status\",\r\n          slot: \"status\",\r\n          minWidth: 100,\r\n          align: \"center\"\r\n        },\r\n        {\r\n          title: \"关联领星\",\r\n          key: \"sid\",\r\n          minWidth: 160,\r\n          align: \"center\",\r\n          render:(_, { row }) => (\r\n              <span>{row.sid>0?\"已关联\":\"未关联\"}</span>\r\n          )\r\n        },\r\n        {\r\n          title: \"新增修改时间\",\r\n          key: \"updateTime\",\r\n          sortable: \"custom\",\r\n          width: 140,\r\n          align: \"center\",\r\n          render: (_, { row }) => (\r\n              <div>\r\n                <p title=\"新增时间\">{row.createTime}</p>\r\n                <p title=\"修改时间\">{row.updateTime}</p>\r\n              </div>\r\n          )\r\n        },\r\n        {\r\n          title: \"操作\",\r\n          slot: \"action\",\r\n          fixed: \"right\",\r\n          minWidth: 150,\r\n          align: \"center\"\r\n        }\r\n      ],\r\n      data: [],\r\n      usersOptions: [],\r\n      allDepartments: []\r\n    };\r\n  },\r\n  methods: {\r\n    treeSelectNormalizer(node) {\r\n      return {\r\n        id: node.id,\r\n        label: node.departmentName,\r\n        children: node.children\r\n      };\r\n    },\r\n    onPersonCancel() {\r\n      this.personVisible = false;\r\n    },\r\n    // 把选中的人员设置在父组件中\r\n    setPerson(personArr = []) {\r\n      let personIds = [];\r\n      personArr.map(item => {\r\n        personIds.push(item.id);\r\n      });\r\n      if (!this.ArrayIsEqual(this.selectPersons, personIds)) {\r\n        Shop.addShopUsers({\r\n          id: this.id,\r\n          userIds: personIds.join(\",\")\r\n        }).then(res => {\r\n          if (res['code'] === 0) {\r\n            this.$Message.success(\"授权成功\");\r\n          } else {\r\n            this.$Message.error(\"授权失败\");\r\n          }\r\n        });\r\n      }\r\n    },\r\n    ArrayIsEqual(arr1, arr2) {\r\n      //判断2个数组是否相等\r\n      if (arr1 === arr2) {\r\n        //如果2个数组对应的指针相同，那么肯定相等，同时也对比一下类型\r\n        return true;\r\n      } else {\r\n        if (arr1.length !== arr2.length) {\r\n          return false;\r\n        } else {\r\n          //长度相同\r\n          for (let i in arr1) {\r\n            //循环遍历对比每个位置的元素\r\n            if (arr1[i] !== arr2[i]) {\r\n              //只要出现一次不相等，那么2个数组就不相等\r\n              return false;\r\n            }\r\n          } //for循环完成，没有出现不相等的情况，那么2个数组相等\r\n          return true;\r\n        }\r\n      }\r\n    },\r\n    syncShop(){\r\n      this.loading = true;\r\n      Shop.syncShop().then((res)=>{\r\n        if(res['code'] ===0){\r\n          this.$Message.success(\"同步成功\");\r\n          this.handleSearch();\r\n        }\r\n      }).finally(()=>{this.loading = false})\r\n    },\r\n    handleModal(data) {\r\n      if (data) {\r\n        this.readName = true;\r\n        this.shopNameShow = true;\r\n        this.modalTitle = \"编辑\";\r\n        this.actionType = \"edit\";\r\n        this.formItem = Object.assign({}, this.formItem, data);\r\n      } else {\r\n        this.modalTitle = \"添加\";\r\n        this.actionType = \"add\";\r\n      }\r\n      this.modalVisible = true;\r\n    },\r\n    handleView(data) {\r\n      this.formItem = data;\r\n      this.modalTitle = \"查看店铺\";\r\n      this.actionType = \"view\";\r\n      this.formItem = Object.assign({}, this.formItem, data);\r\n      this.modalViewVisible = true;\r\n    },\r\n    handleReset() {\r\n      this.formItem = {\r\n        id: \"\",\r\n        platformId: \"\",\r\n        platform: \"\",\r\n        siteId: \"\",\r\n        site: \"\",\r\n        name: \"\",\r\n        aliaName: \"\",\r\n        erpCustNo: \"\",\r\n        managedUserName:\"\",\r\n        phone:\"\",\r\n        status: 0,\r\n        remark: \"\"\r\n      };\r\n      let form = this.$refs[\"form\"];\r\n      form.resetFields();\r\n      this.modalVisible = false;\r\n      this.shopNameShow = false;\r\n      this.readName = false;\r\n      this.siteRule = { required: false };\r\n      this.saving = false;\r\n    },\r\n\r\n    getDepartNames() {\r\n      const { allDepartments, formItem } = this;\r\n      const departmentIds = formItem.departmentId || [];\r\n      const results = [];\r\n      departmentIds.forEach(id => {\r\n        const departmentName = (allDepartments.find(v => v.id === id) || {})\r\n            .departmentName;\r\n        if (departmentName) results.push(departmentName);\r\n      });\r\n      return results;\r\n    },\r\n    handleSubmit() {\r\n      let form = this.$refs[\"form\"];\r\n      const { formItem, usersOptions } = this;\r\n      form.validate(valid => {\r\n        const params = {\r\n          ...this.formItem,\r\n          departmentId: (this.formItem.departmentId || []).toString(),\r\n          departmentName: this.getDepartNames().toString(),\r\n          name: formItem.name ? formItem.name.trim() : formItem.name,\r\n          operateUser: (\r\n              usersOptions.find(v => v.userId === formItem.managedUser) || {}\r\n          ).nickName\r\n        };\r\n        if (valid) {\r\n          this.saving = true;\r\n          if (this.formItem.id) {\r\n            Shop.edit(params).then(res => {\r\n              if (res['code'] === 0) {\r\n                this.$Message.success(\"保存成功\");\r\n                this.handleReset()\r\n                this.handleSearch();\r\n              }}).finally(() => {this.saving = false;});\r\n          } else {\r\n            Shop.add(params).then(res => {\r\n              if (res['code'] === 0) {\r\n                this.handleReset();\r\n                this.handleSearch();\r\n                this.$Message.success(\"保存成功\");\r\n              }}).finally(() => {this.saving = false;});\r\n          }\r\n        }\r\n      });\r\n    },\r\n    handleResetForm(form) {\r\n      if (this.$refs[form]) this.$refs[form].resetFields();\r\n      this.pageInfo.deptId = undefined;\r\n    },\r\n    handleSearch(page) {\r\n      const { pageInfo } = this;\r\n      if (page) {\r\n        pageInfo.page = page;\r\n      }\r\n      this.loading = true;\r\n      const params = {\r\n        ...pageInfo,\r\n        total: undefined,\r\n        deptId: pageInfo.deptId\r\n            ? pageInfo.deptId.toString() || undefined\r\n            : undefined\r\n      };\r\n      Shop.listPage(params)\r\n          .then(res => {\r\n            this.data = res.data.records;\r\n            this.pageInfo.total = parseInt(res.data.total);\r\n          })\r\n          .finally(() => {\r\n            this.loading = false;\r\n          });\r\n    },\r\n    handlePage(current) {\r\n      this.pageInfo.page = current;\r\n      this.handleSearch();\r\n    },\r\n    handlePageSize(size) {\r\n      this.pageInfo.limit = size;\r\n      this.handleSearch();\r\n    },\r\n    handleRemove(data) {\r\n      let modal = this.$Modal;\r\n      let title = data.status === 0?\"停用\":(data.status === 1?\"启用\":\"解锁\");\r\n      modal.confirm({\r\n        title: \"确定\"+title+\"吗？\",\r\n        onOk: () => {\r\n          Shop.remove(data.id).then(res => {\r\n            if (res['code'] === 0) {\r\n              this.pageInfo.page = 1;\r\n              this.$Message.success(title+\"成功\");\r\n            }\r\n            this.handleSearch();\r\n          });\r\n        }\r\n      });\r\n    },\r\n    handlePlatform() {\r\n      Platform.getAll().then(res => {\r\n        this.platformList = res.data;\r\n        this.platformList.unshift({ id: -1,  name: \"全部\" });\r\n      });\r\n    },\r\n    handleSite() {\r\n      Site.getAll().then(res => {\r\n        this.siteList = res.data;\r\n        this.siteList.unshift({ id: -1, name: \"全部\" });\r\n      });\r\n    },\r\n    change(val) {\r\n      if (val === \"1\") {\r\n        this.siteRule = {\r\n          required: true,\r\n          message: \"所属站点不能为空\",\r\n          trigger: \"blur\"\r\n        };\r\n      } else {\r\n        this.siteRule = { required: false };\r\n      }\r\n      if (val != null) {\r\n        this.formItem.siteId = \"\";\r\n        Site.getByPlatformId(val).then(res => {\r\n          this.siteList = res.data;\r\n          if (val !== \"1\") {\r\n            this.siteList.unshift({ id: -1, name: \"全部\" });\r\n          }\r\n        });\r\n      }\r\n    },\r\n    //主表格排序查询\r\n    onSortChange({order }) {\r\n      if (order && order !== \"normal\") {\r\n        this.pageInfo.sort = \"c_update_time\";\r\n        this.pageInfo.order = order;\r\n      } else {\r\n        this.pageInfo.sort = null;\r\n        this.pageInfo.order = null;\r\n      }\r\n      this.handleSearch();\r\n    },\r\n    platformChange(pfId) {\r\n      if (pfId != null) {\r\n        Site.getByPlatformId(pfId).then(res => {\r\n          this.siteList = res.data;\r\n          this.siteList.unshift({ id: -1, name: \"全部\" });\r\n          this.pageInfo.siteId = -1;\r\n        });\r\n      }\r\n    },\r\n    handleClick(name, row) {\r\n      switch (name) {\r\n        case \"remove\":\r\n          this.handleRemove(row);\r\n          break;\r\n        case \"view\":\r\n          this.handleView(row);\r\n          break;\r\n      }\r\n    },\r\n    // 人员选择相关\r\n    setSelectInfo(info = {}) {\r\n      this.usersOptions = info.personArr || [];\r\n    },\r\n    openPerson() {\r\n      //打开人员选择\r\n      const { personSelectRef } = this.$refs;\r\n      const { usersOptions, formItem } = this;\r\n      const selectedId = formItem.managedUser || undefined;\r\n      if (personSelectRef)\r\n        personSelectRef.setDefault(usersOptions\r\n            .filter(v => selectedId === v.userId)\r\n            .map(v => ({ name: v.nickName, id: v.userId }))\r\n        ); //给组件设置默认选中\r\n      this.personVisible = true;\r\n    }\r\n  },\r\n  mounted: function() {\r\n    this.handleSearch();\r\n    this.handlePlatform();\r\n    this.handleSite();\r\n  }\r\n};\r\n</script>\r\n<style lang=\"less\">\r\n.shopManage {\r\n  .searchForm {\r\n    position: relative;\r\n    padding-right: 200px;\r\n    .rightBtn {\r\n      position: absolute;\r\n      top: 0;\r\n      right: 0;\r\n    }\r\n  }\r\n  .ivu-table-cell {\r\n    padding-left: 8px;\r\n    padding-right: 8px;\r\n  }\r\n}\r\n.shopManageEditModal {\r\n  .ivu-modal {\r\n    top: 30px;\r\n    .ivu-modal-body {\r\n      max-height: 720px;\r\n      overflow: auto;\r\n    }\r\n  }\r\n  .sellerSelectItem {\r\n    position: relative;\r\n    .ivu-select-multiple .ivu-tag span:not(.ivu-select-max-tag) {\r\n      max-width: 45px;\r\n    }\r\n    .closeIcon {\r\n      color: #ccc;\r\n      width: 16px;\r\n      position: absolute;\r\n      top: 0;\r\n      right: 80px;\r\n      font-size: 9px;\r\n      &:hover {\r\n        cursor: pointer;\r\n        color: #e53935;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}