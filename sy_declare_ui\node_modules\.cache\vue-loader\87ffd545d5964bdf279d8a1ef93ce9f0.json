{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\basf\\product\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\basf\\product\\index.vue", "mtime": 1752737748516}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQppbXBvcnQgUHJvZHVjdEluZm8gZnJvbSAnLi9wcm9kdWN0SW5mby52dWUnOyAvL+S6p+WTgeS/oeaBrw0KaW1wb3J0IFByb2R1Y3RSZWxheCBmcm9tICcuL3Byb2R1Y3RSZWxheC52dWUnOyAvL+S6p+WTgeWFs+iBlA0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAncHJvZHVjdExvb2snLA0KICBkYXRhICgpIHsNCiAgICByZXR1cm4ge307DQogIH0sDQogIGNvbXBvbmVudHM6IHsNCiAgICBQcm9kdWN0SW5mbyxQcm9kdWN0UmVsYXgNCiAgfSwNCiAgbW91bnRlZCAoKSB7DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBjbGlja1RhYiAoKSB7DQogICAgICBjb25zdCB7IFByb2R1Y3RJbmZvLFByb2R1Y3RSZWxheCB9ID0gdGhpcy4kcmVmczsNCiAgICAgIGlmIChQcm9kdWN0SW5mbyAmJiBQcm9kdWN0SW5mby5jbG9zZURyb3Bkb3duKSB7DQogICAgICAgIFByb2R1Y3RJbmZvLmNsb3NlRHJvcGRvd24oKTsNCiAgICAgIH0NCiAgICAgIGlmIChQcm9kdWN0UmVsYXggJiYgUHJvZHVjdFJlbGF4LmNsb3NlRHJvcGRvd24pIHsNCiAgICAgICAgUHJvZHVjdFJlbGF4LmNsb3NlRHJvcGRvd24oKTsNCiAgICAgIH0NCiAgICB9LA0KICB9DQp9Ow0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAiBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/view/module/basf/product", "sourcesContent": ["<!--\r\n@create date 2024-01-09\r\n@desc 产品信息表\r\n-->\r\n<template>\r\n  <div>\r\n    <Tabs type=\"card\" @on-click=\"clickTab\">\r\n      <TabPane label=\"产品信息\" name=\"1\" tab=\"global\">\r\n        <ProductInfo ref=\"productInfo\"/>\r\n      </TabPane>\r\n      <TabPane label=\"产品关联\" name=\"2\" tab=\"global\">\r\n        <ProductRelax ref=\"productRelax\"/>\r\n      </TabPane>\r\n    </Tabs>\r\n  </div>\r\n</template>\r\n<script>\r\nimport ProductInfo from './productInfo.vue'; //产品信息\r\nimport ProductRelax from './productRelax.vue'; //产品关联\r\nexport default {\r\n  name: 'productLook',\r\n  data () {\r\n    return {};\r\n  },\r\n  components: {\r\n    ProductInfo,ProductRelax\r\n  },\r\n  mounted () {\r\n  },\r\n  methods: {\r\n    clickTab () {\r\n      const { ProductInfo,ProductRelax } = this.$refs;\r\n      if (ProductInfo && ProductInfo.closeDropdown) {\r\n        ProductInfo.closeDropdown();\r\n      }\r\n      if (ProductRelax && ProductRelax.closeDropdown) {\r\n        ProductRelax.closeDropdown();\r\n      }\r\n    },\r\n  }\r\n};\r\n</script>\r\n"]}]}