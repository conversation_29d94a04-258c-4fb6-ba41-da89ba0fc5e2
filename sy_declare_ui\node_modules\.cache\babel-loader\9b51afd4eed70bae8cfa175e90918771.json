{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\api\\base\\gateway\\ipLimit.js", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\api\\base\\gateway\\ipLimit.js", "mtime": 1752737748399}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\.babelrc", "mtime": 1752737748316}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "ipLimitPath", "listPage", "params", "url", "method", "addIpLimit", "data", "updateIpLimit", "removeIpLimit", "id", "getApiDetails", "parentId", "addApiDetails", "_ref", "apiIds", "join"], "sources": ["D:/Users/<USER>/Desktop/dev/sy_declare_ui/src/api/base/gateway/ipLimit.js"], "sourcesContent": ["import request from '@/libs/request'\r\nconst ipLimitPath = \"/base/ipLimit\";\r\n\r\n/**\r\n * 获取分页数据\r\n */\r\nconst listPage = (params) => {\r\n  return request({\r\n    url: ipLimitPath + '/listPage',\r\n    params,\r\n    method: 'get'\r\n  })\r\n}\r\n/**\r\n * 添加数据\r\n */\r\nconst addIpLimit = (data) => {\r\n  return request({\r\n    url: ipLimitPath + '/addIpLimit',\r\n    data,\r\n    method: 'post'\r\n  })\r\n}\r\n/**\r\n * 添加数据\r\n */\r\nconst updateIpLimit = (data) => {\r\n  return request({\r\n    url: ipLimitPath + '/updateIpLimit',\r\n    data,\r\n    method: 'post'\r\n  })\r\n}\r\n\r\n/**\r\n * 删除\r\n */\r\nconst removeIpLimit = (id) => {\r\n  const data = {\"id\":id}\r\n  return request({\r\n    url: ipLimitPath + '/removeIpLimit',\r\n    data,\r\n    method: 'post'\r\n  })\r\n}\r\n\r\n/**\r\n * 查询策略已绑定API列表\r\n * @param id\r\n */\r\nexport const getApiDetails = (id) => {\r\n  const params = {\r\n    parentId: id\r\n  }\r\n  return request({\r\n    url: ipLimitPath + '/getApiDetails',\r\n    params,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n/**\r\n * 绑定API\r\n * @param policyId\r\n * @param apiIds\r\n */\r\nexport const addApiDetails = ({ parentId, apiIds }) => {\r\n  const data = {\r\n    parentId: parentId,\r\n    apiIds: apiIds.join(',')\r\n  }\r\n  return request({\r\n    url: ipLimitPath + '/addApiDetails',\r\n    data,\r\n    method: 'post'\r\n  })\r\n}\r\nexport default {\r\n  listPage,\r\n  addIpLimit,\r\n  updateIpLimit,\r\n  removeIpLimit,\r\n  getApiDetails,\r\n  addApiDetails\r\n}\r\n"], "mappings": ";AAAA,OAAOA,OAAO,MAAM,gBAAgB;AACpC,IAAMC,WAAW,GAAG,eAAe;;AAEnC;AACA;AACA;AACA,IAAMC,QAAQ,GAAG,SAAXA,QAAQA,CAAIC,MAAM,EAAK;EAC3B,OAAOH,OAAO,CAAC;IACbI,GAAG,EAAEH,WAAW,GAAG,WAAW;IAC9BE,MAAM,EAANA,MAAM;IACNE,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;AACD;AACA;AACA;AACA,IAAMC,UAAU,GAAG,SAAbA,UAAUA,CAAIC,IAAI,EAAK;EAC3B,OAAOP,OAAO,CAAC;IACbI,GAAG,EAAEH,WAAW,GAAG,aAAa;IAChCM,IAAI,EAAJA,IAAI;IACJF,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;AACD;AACA;AACA;AACA,IAAMG,aAAa,GAAG,SAAhBA,aAAaA,CAAID,IAAI,EAAK;EAC9B,OAAOP,OAAO,CAAC;IACbI,GAAG,EAAEH,WAAW,GAAG,gBAAgB;IACnCM,IAAI,EAAJA,IAAI;IACJF,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA,IAAMI,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,EAAE,EAAK;EAC5B,IAAMH,IAAI,GAAG;IAAC,IAAI,EAACG;EAAE,CAAC;EACtB,OAAOV,OAAO,CAAC;IACbI,GAAG,EAAEH,WAAW,GAAG,gBAAgB;IACnCM,IAAI,EAAJA,IAAI;IACJF,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,IAAMM,aAAa,GAAG,SAAhBA,aAAaA,CAAID,EAAE,EAAK;EACnC,IAAMP,MAAM,GAAG;IACbS,QAAQ,EAAEF;EACZ,CAAC;EACD,OAAOV,OAAO,CAAC;IACbI,GAAG,EAAEH,WAAW,GAAG,gBAAgB;IACnCE,MAAM,EAANA,MAAM;IACNE,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,IAAMQ,aAAa,GAAG,SAAhBA,aAAaA,CAAAC,IAAA,EAA6B;EAAA,IAAvBF,QAAQ,GAAAE,IAAA,CAARF,QAAQ;IAAEG,MAAM,GAAAD,IAAA,CAANC,MAAM;EAC9C,IAAMR,IAAI,GAAG;IACXK,QAAQ,EAAEA,QAAQ;IAClBG,MAAM,EAAEA,MAAM,CAACC,IAAI,CAAC,GAAG;EACzB,CAAC;EACD,OAAOhB,OAAO,CAAC;IACbI,GAAG,EAAEH,WAAW,GAAG,gBAAgB;IACnCM,IAAI,EAAJA,IAAI;IACJF,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;AACD,eAAe;EACbH,QAAQ,EAARA,QAAQ;EACRI,UAAU,EAAVA,UAAU;EACVE,aAAa,EAAbA,aAAa;EACbC,aAAa,EAAbA,aAAa;EACbE,aAAa,EAAbA,aAAa;EACbE,aAAa,EAAbA;AACF,CAAC"}]}