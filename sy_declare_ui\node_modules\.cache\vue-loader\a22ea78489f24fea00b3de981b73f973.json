{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\custom\\base\\customClass\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\custom\\base\\customClass\\index.vue", "mtime": 1754360258640}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQppbXBvcnQgQ3VzdG9tQ2xhc3MgZnJvbSAiQC9hcGkvY3VzdG9tL2N1c3RvbUNsYXNzIjsNCmltcG9ydCB7bGlzdEFsbFNwdX0gZnJvbSAnQC9hcGkvYmFzZi9wcm9kdWN0LmpzJw0KaW1wb3J0IENvbW1vbkFwaSBmcm9tICJAL2FwaS9iYXNlL2NvbW1vbkFwaSI7DQppbXBvcnQgQ3VycmVuY3kgZnJvbSAiQC9hcGkvYmFzZi9jdXJyZW5jeSI7DQppbXBvcnQgQ2F0ZWdvcnlWaWV3IGZyb20gIkAvdmlldy9tb2R1bGUvY3VzdG9tL2Jhc2UvY3VzdG9tQ2xhc3MvaW5kZXhWaWV3LnZ1ZSI7DQppbXBvcnQgTG9nTW9kZWwgZnJvbSAiQC92aWV3L21vZHVsZS9iYXNlL2J1c3NpbmVzc0xvZy9sb2dNb2RlbC52dWUiOw0KaW1wb3J0IHtnZXRUb2tlbiwgZ2V0VXJsfSBmcm9tICJAL2xpYnMvdXRpbCI7DQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdjYXRlZ29yeScsDQogIGNvbXBvbmVudHM6IHtMb2dNb2RlbCwgQ2F0ZWdvcnlWaWV3fSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgc2VhcmNoRm9ybTogew0KICAgICAgICBjbGFzc05hbWU6ICcnLA0KICAgICAgICBjYXRlZ29yeU5hbWU6ICcnLA0KICAgICAgICBoc0NvZGU6ICcnLA0KICAgICAgfSwNCiAgICAgIGN1cnJlbnRJZDogbnVsbCwvL+W9k+WJjeaTjeS9nOeahOaVsOaNrklEDQogICAgICBjb2x1bW46IFsNCiAgICAgICAge3RpdGxlOiAn57G755uu5ZCN56ewJyxrZXk6ICdjbGFzc05hbWUnLCBtaW5XaWR0aDogMTgwLCBhbGlnbjogJ2xlZnQnLHJlc2l6YWJsZTp0cnVlLHJlbmRlcjooXyx7cm93fSk9Pig8c3BhbiB2LWNvcHl0ZXh0PXtyb3cuY2xhc3NOYW1lfT57cm93LmNsYXNzTmFtZX08L3NwYW4+KX0sDQogICAgICAgIHt0aXRsZTogJ+S6p+WTgeWei+WPtycsIGtleTogJ2NhdGVnb3J5TmFtZScsbWluV2lkdGg6IDEyMCwgYWxpZ246ICdjZW50ZXInLHJlc2l6YWJsZTp0cnVlLHJlbmRlcjooXyx7cm93fSk9Pig8c3BhbiB2LWNvcHl0ZXh0PXtyb3cuY2F0ZWdvcnlOYW1lfT57cm93LmNhdGVnb3J5TmFtZX08L3NwYW4+KX0sDQogICAgICAgIHt0aXRsZTogJ+S4reaWh+aKpeWFs+WQjScsIGtleTogJ2N1c3RvbU5hbWVDbicsbWluV2lkdGg6IDEyMCwgYWxpZ246ICdjZW50ZXInLHJlc2l6YWJsZTp0cnVlLHJlbmRlcjooXyx7cm93fSk9Pig8c3BhbiB2LWNvcHl0ZXh0PXtyb3cuY3VzdG9tTmFtZUNufT57cm93LmN1c3RvbU5hbWVDbn08L3NwYW4+KX0sDQogICAgICAgIHt0aXRsZTogJ+iLseaWh+aKpeWFs+WQjScsIGtleTogJ2N1c3RvbU5hbWVFbicsbWluV2lkdGg6IDEyMCwgYWxpZ246ICdjZW50ZXInLHJlc2l6YWJsZTp0cnVlLHJlbmRlcjooXyx7cm93fSk9Pig8c3BhbiB2LWNvcHl0ZXh0PXtyb3cuY3VzdG9tTmFtZUVufT57cm93LmN1c3RvbU5hbWVFbn08L3NwYW4+KX0sDQogICAgICAgIHt0aXRsZTogJ+aKpeWFs+a1t+WFs+e8lueggScsa2V5OiAnaHNDb2RlJyxtaW5XaWR0aDogMTIwLCBhbGlnbjogJ2NlbnRlcicscmVzaXphYmxlOnRydWUscmVuZGVyOihfLHtyb3d9KT0+KDxzcGFuIHYtY29weXRleHQ9e3Jvdy5oc0NvZGV9Pntyb3cuaHNDb2RlfTwvc3Bhbj4pfSwNCiAgICAgICAge3RpdGxlOiAn5p2Q6LSoJyxrZXk6ICdtYXRlcmlhbCcsbWluV2lkdGg6IDEyMCwgYWxpZ246ICdjZW50ZXInLHJlc2l6YWJsZTp0cnVlLHJlbmRlcjooXyx7cm93fSk9Pig8c3BhbiB2LWNvcHl0ZXh0PXtyb3cubWF0ZXJpYWx9Pntyb3cubWF0ZXJpYWx9PC9zcGFuPil9LA0KICAgICAgICB7dGl0bGU6ICfnlKjpgJQnLGtleTogJ3B1cnBvc2UnLG1pbldpZHRoOiAxMjAsIGFsaWduOiAnY2VudGVyJyxyZXNpemFibGU6dHJ1ZSxyZW5kZXI6KF8se3Jvd30pPT4oPHNwYW4gdi1jb3B5dGV4dD17cm93LnB1cnBvc2V9Pntyb3cucHVycG9zZX08L3NwYW4+KX0sDQogICAgICAgIHt0aXRsZTogJ+aKpeWFs+WNleS9jScsa2V5OiAndW5pdCcsbWluV2lkdGg6IDEyMCwgYWxpZ246ICdjZW50ZXInLHJlc2l6YWJsZTp0cnVlLHJlbmRlcjooXyx7cm93fSk9Pig8c3BhbiB2LWNvcHl0ZXh0PXtyb3cudW5pdH0+e3Jvdy51bml0fTwvc3Bhbj4pfSwNCiAgICAgICAge3RpdGxlOiAn5riF5YWz5pWw5o2uJyxrZXk6ICdjbGVhcmFuY2VFbGVtZW50Jyx3aWR0aDogMTIwLCBhbGlnbjogJ2NlbnRlcicscmVzaXphYmxlOnRydWUsdHlwZTogJ3RlbXBsYXRlJyx0ZW1wbGF0ZTogJ2NsZWFyYW5jZUVsZW1lbnQnfSwNCiAgICAgICAge3RpdGxlOiAn5pON5L2cJyx3aWR0aDogMjAwLCB0eXBlOiAndGVtcGxhdGUnLHRlbXBsYXRlOiAnYWN0aW9uJ30NCiAgICAgIF0sDQogICAgICBkYXRhOiBbXSwNCiAgICAgIGFsbERhdGE6IFtdLC8v5YyF5ZCr5piO57uG57G755uu55qEbGlzdOaVsOaNruagvOW8jw0KICAgICAgbG9hZGluZzogZmFsc2UsDQogICAgICBzYXZpbmc6IGZhbHNlLA0KICAgICAgY2xpY2tOb2RlOiB7fSwNCiAgICAgIC8vIOaWsOWinue8lui+kQ0KICAgICAgY2xhc3NNb2RlbFZpc2libGU6IGZhbHNlLA0KICAgICAgZm9ybToge3BhcmVudE5hbWU6IG51bGwsIHBhcmVudElkOiBudWxsLCBjbGFzc05hbWU6IG51bGwsIGN1c3RvbU5hbWVDbjogbnVsbCwgY3VzdG9tTmFtZUVuOiBudWxsLCBtYXRlcmlhbDogbnVsbCwgcHVycG9zZTogbnVsbCwgdW5pdDogbnVsbCwgaHNDb2RlOiBudWxsLH0sDQogICAgICBkaXNhYmxlZDpmYWxzZSwNCiAgICAgIHNwaW5TaG93OiBmYWxzZSwNCiAgICAgIGNsYXNzVGl0bGU6ICcnLA0KICAgICAgdHJlZURhdGE6W10sDQogICAgICBydWxlVmFsaWRhdGU6IHsNCiAgICAgICAgY2xhc3NOYW1lOiBbDQogICAgICAgICAge3JlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl57G755uu5ZCN56ewJywgdHJpZ2dlcjogJ2JsdXInfQ0KICAgICAgICBdLA0KICAgICAgfSwNCiAgICAgIC8v5p+l55yL5piO57uGDQogICAgICBjbGFzc1ZpZXdWaXNpYmxlOmZhbHNlLA0KICAgICAgLy/nlLPmiqXopoHntKANCiAgICAgIGRlY2xhcmVNb2RlbFZpc2libGU6ZmFsc2UsDQogICAgICBkZWNsYXJlQ29sdW1uOlt7dGl0bGU6ICfnsbvlnosnLGtleTogJ2RlY0tleScsIG1pbldpZHRoOiAxMjAsIGFsaWduOiAnY2VudGVyJyxzbG90OidkZWNLZXknfSwNCiAgICAgICAge3RpdGxlOiAn5YaF5a65Jywga2V5OiAnY29udGVudCcsbWluV2lkdGg6IDEyMCwgYWxpZ246ICdjZW50ZXInLHNsb3Q6J2RlY0NvbnRlbnQnfSwNCiAgICAgICAge3RpdGxlOiAn5pON5L2cJyxrZXk6ICdhY3Rpb24nLHdpZHRoOiAxMDAsIGFsaWduOiAnY2VudGVyJyxzbG90OidkZWNBY3Rpb24nfV0sDQogICAgICBkZWNsYXJlRGF0YTpbXSwNCiAgICAgIGRlY2xhcmVUaXRsZTonJywNCg0KICAgICAgLy/muIXlhbPotYTmlpkNCiAgICAgIGNsZWFyYW5jZU1vZGVsVmlzaWJsZTpmYWxzZSwNCiAgICAgIGNsZWFyYW5jZUNvbHVtbjpbe3RpdGxlOiAn5Zu95a62JyxrZXk6ICdjb3VudHJ5JywgbWluV2lkdGg6IDEyMCwgYWxpZ246ICdjZW50ZXInLHNsb3Q6J2NvdW50cnknfSwNCiAgICAgICAge3RpdGxlOiAn5riF5YWz57yW56CBJywga2V5OiAnaHNDb2RlJyxtaW5XaWR0aDogMTIwLCBhbGlnbjogJ2NlbnRlcicsc2xvdDonaHNDb2RlJ30sDQogICAgICAgIHt0aXRsZTogJ+a4heWFs+S7t+agvCcsIGtleTogJ3ByaWNlJyxtaW5XaWR0aDogMTIwLCBhbGlnbjogJ2NlbnRlcicsc2xvdDoncHJpY2UnfSwNCiAgICAgICAge3RpdGxlOiAn5riF5YWz5biB56eNJywga2V5OiAnY3VycmVuY3knLG1pbldpZHRoOiAxMjAsIGFsaWduOiAnY2VudGVyJyxzbG90OidjdXJyZW5jeSd9LA0KICAgICAgICB7dGl0bGU6ICfmk43kvZwnLGtleTogJ2FjdGlvbicsd2lkdGg6IDEwMCwgYWxpZ246ICdjZW50ZXInLHNsb3Q6J2NsZWFyYW5jZUFjdGlvbid9XSwNCiAgICAgIGNsZWFyYW5jZURhdGE6W10sDQogICAgICBjbGVhcmFuY2VUaXRsZTonJywNCg0KICAgICAgbG9nVmlzaWJsZTpmYWxzZSwNCiAgICAgIC8v5a+85YWl57G755uuDQogICAgICBpbXBvcnRDbGFzc1VSbDogZ2V0VXJsKCkgKyAiL2Jhc2UvY3VzdG9tQ2xhc3MvaW1wb3J0Q2xhc3NGaWxlIiwNCiAgICAgIC8v5a+85YWl5riF5YWz6LWE5paZDQogICAgICBpbXBvcnRDbGVhcmFuY2VVUmw6IGdldFVybCgpICsgIi9iYXNlL2N1c3RvbUNsYXNzL2ltcG9ydENsZWFyYW5jZUZpbGUiLA0KICAgICAgbG9naW5JbmZvOiB7DQogICAgICAgIEFjY2VwdDogJ2FwcGxpY2F0aW9uL2pzb24sdGV4dC9wbGFpbiwgKi8qLCAqLyonLA0KICAgICAgICBtb2RlOiAnY29ycycsDQogICAgICAgIEF1dGhvcml6YXRpb246ICdCZWFyZXIgJyArIGdldFRva2VuKCkNCiAgICAgIH0sDQogICAgICAvL+Wei+WPt+WIl+ihqA0KICAgICAgc3B1TGlzdDogW10sDQogICAgICAvL+eUs+aKpeimgee0oOexu+Weiw0KICAgICAgZGVjbGFyZVR5cGVMaXN0OltdLA0KICAgICAgLy/luIHnp40NCiAgICAgIGN1cnJlbmN5TGlzdDpbXSwNCiAgICAgIC8v5Zu95a62DQogICAgICBjb3VudHJ5TGlzdDpbXSwNCiAgICAgIC8v5pel5b+X57G75Z6LDQogICAgICByZWZUeXBlOm51bGwsDQogICAgfQ0KICB9LA0KICBtb3VudGVkKCkgew0KICAgIHRoaXMubGlzdEFsbFNwdSgpOy8v6I635Y+W5Lqn5ZOB57G755uuTGlzdA0KICAgIHRoaXMubGlzdERlY2xhcmVUeXBlTGlzdCgpOy8v6I635Y+W5Lqn5ZOB57G755uuTGlzdA0KICAgIHRoaXMuaGFuZGxlQ3VycmVuY3koKTsvL+iOt+WPluaJgOacieW4geenjQ0KICAgIHRoaXMuZ2V0Q291bnRyeUxpc3QoKTsvL+iOt+WPluaJgOacieWbveWutg0KICAgIHRoaXMuZ2V0TG9nUmVmVHlwZSgpOy8v6I635Y+W5pel5b+X57G75Z6LDQogICAgdGhpcy5oYW5kbGVTZWFyY2goKTsvL+iOt+WPluaJgOacieaVsOaNrg0KICB9LA0KICBtZXRob2RzOiB7DQogICAgLy/mn6Xor6LvvIzph43nva4NCiAgICBoYW5kbGVTZWFyY2goKSB7DQogICAgICBDdXN0b21DbGFzcy5saXN0VHJlZSh0aGlzLnNlYXJjaEZvcm0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgaWYgKHJlc1snY29kZSddID09PSAwKSB7DQogICAgICAgICAgdGhpcy5kYXRhID0gcmVzLmRhdGE7DQogICAgICAgICAgdGhpcy50cmVlRGF0YSA9IHJlcy5kYXRhOy8v5aaC5p6c5pyJ5paw5aKe55qE5pWw5o2uLOmcgOimgemHjeaWsOaJk+W8gOmhteetvuaJjeiDveWKoOi9veWIsA0KICAgICAgICAgIHRoaXMuYWxsRGF0YSA9IFtdOw0KICAgICAgICAgIHRoaXMuc2V0VGl0bGUodGhpcy50cmVlRGF0YSkNCiAgICAgICAgICBsZXQgZGVjbGFyZVR5cGVPYmogPSB7fTsNCiAgICAgICAgICBsZXQgaW5kZXggPSAwOw0KICAgICAgICAgIHRoaXMuZGVjbGFyZVR5cGVMaXN0LmZvckVhY2goaXRlbT0+ZGVjbGFyZVR5cGVPYmpbaXRlbVsndmFsdWUnXV0gPSAnZGVjJysgaW5kZXgrKykNCiAgICAgICAgICB0aGlzLnNldERlY2xhcmF0aW9uRWxlbWVudCh0aGlzLmRhdGEsZGVjbGFyZVR5cGVPYmopDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICBzZXRUaXRsZShkYXRhKXsNCiAgICAgIGlmKGRhdGEgJiYgZGF0YS5sZW5ndGg+MCl7DQogICAgICAgIGRhdGEuZm9yRWFjaChpdGVtID0+IHsNCiAgICAgICAgICB0aGlzLmFsbERhdGEucHVzaChpdGVtKTsNCiAgICAgICAgICBpdGVtWyd0aXRsZSddID0gaXRlbVsnY2xhc3NOYW1lJ107DQogICAgICAgICAgdGhpcy5zZXRUaXRsZShpdGVtWydjaGlsZHJlbiddKTsNCiAgICAgICAgfSkNCiAgICAgIH0NCiAgICB9LA0KICAgIHNldERlY2xhcmF0aW9uRWxlbWVudChkYXRhTGlzdCxkZWNsYXJlVHlwZU9iail7DQogICAgICBpZighZGF0YUxpc3QgfHwgZGF0YUxpc3QubGVuZ3RoPD0wKXsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KICAgICAgZGF0YUxpc3QuZm9yRWFjaChpdGVtPT57DQogICAgICAgIGlmKGl0ZW1bJ2RlY2xhcmF0aW9uRWxlbWVudExpc3QnXSAhPW51bGwgJiYgaXRlbVsnZGVjbGFyYXRpb25FbGVtZW50TGlzdCddLmxlbmd0aCAhPT0wKXsNCiAgICAgICAgICBpdGVtWydkZWNsYXJhdGlvbkVsZW1lbnRMaXN0J10uZm9yRWFjaChkZWNsYXJhdGlvbkVsZW1lbnQ9PnsNCiAgICAgICAgICAgIGxldCBkZWNLZXkgPSBkZWNsYXJlVHlwZU9ialtkZWNsYXJhdGlvbkVsZW1lbnRbJ2RlY0tleSddXTsNCiAgICAgICAgICAgIGlmKGRlY0tleSAhPW51bGwpew0KICAgICAgICAgICAgICBpdGVtW2RlY0tleV0gPSBkZWNsYXJhdGlvbkVsZW1lbnRbJ2NvbnRlbnQnXTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICAgIHRoaXMuc2V0RGVjbGFyYXRpb25FbGVtZW50KGl0ZW1bJ2NoaWxkcmVuJ10sZGVjbGFyZVR5cGVPYmopOw0KICAgICAgfSkNCiAgICB9LA0KICAgIGhhbmRsZVJlc2V0KCkgew0KICAgICAgdGhpcy4kcmVmc1snc2VhcmNoRm9ybSddLnJlc2V0RmllbGRzKCk7DQogICAgfSwNCg0KICAgIGxvYWRDbGFzc0NoaWxkKHBhcmFtcykgew0KICAgICAgdGhpcy5jbGlja05vZGUgPSBwYXJhbXMucm93IHx8IHt9Ow0KICAgICAgdGhpcy5jdXJyZW50SWQgPSBwYXJhbXMucm93LmlkOw0KICAgIH0sDQogICAgcm93Q2xhc3NOYW1lKHJvdykgew0KICAgICAgY29uc3QgeyBjbGlja05vZGUgfSA9IHRoaXM7DQogICAgICBpZihjbGlja05vZGUuaWQgPT09IHJvdy5pZCkgcmV0dXJuICdzcGVjaWFsQmFja2dyb3VuZCc7DQogICAgfSwNCiAgICAvL+WFs+iBlOexu+ebrg0KICAgIHNlbGVjdFBhcmVudChyb3cpIHsNCiAgICAgIHRoaXMuZm9ybS5wYXJlbnROYW1lID0gcm93WzBdLmNsYXNzTmFtZTsNCiAgICAgIHRoaXMuZm9ybS5sZXZlbCA9IHJvd1swXS5sZXZlbCArIDE7DQogICAgICB0aGlzLmZvcm0ucGFyZW50SWQgPSByb3dbMF0uaWQNCiAgICB9LA0KDQogICAgLy/lr7zlhaXnsbvnm67mlbDmja7lkozmuIXlhbPotYTmlpnmlbDmja4NCiAgICBoYW5kbGVJbXBvcnRDbGFzc1N1Y2Nlc3MocmVzKXsNCiAgICAgIHRoaXMuJHJlZnNbJ3VwbG9hZENsYXNzRmlsZVJlZiddLmNsZWFyRmlsZXMoKTsNCiAgICAgIHRoaXMuaGFuZGxlSW1wb3J0U3VjY2VzcyhyZXMpOw0KICAgIH0sDQogICAgaGFuZGxlSW1wb3J0Q2xlYXJhbmNlU3VjY2VzcyhyZXMpew0KICAgICAgdGhpcy4kcmVmc1sndXBsb2FkQ2xlYXJhbmNlRmlsZVJlZiddLmNsZWFyRmlsZXMoKTsNCiAgICAgIHRoaXMuaGFuZGxlSW1wb3J0U3VjY2VzcyhyZXMpOw0KICAgIH0sDQogICAgaGFuZGxlSW1wb3J0U3VjY2VzcyhyZXMpIHsNCiAgICAgIGlmIChyZXNbJ2NvZGUnXSA9PT0gMCkgew0KICAgICAgICB0aGlzLiRNZXNzYWdlLnN1Y2Nlc3MoJ+WvvOWFpeaIkOWKnycpOw0KICAgICAgICB0aGlzLmhhbmRsZVNlYXJjaCgpOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy4kTWVzc2FnZS53YXJuaW5nKHJlc1snbWVzc2FnZSddKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIGhhbmRsZUltcG9ydEZvcm1hdEVycm9yKGZpbGUpIHsNCiAgICAgIC8v5qC85byP6aqM6K+B5aSx6LSl55qE6ZKp5a2QDQogICAgICB0aGlzLiRNb2RhbC5lcnJvcih7DQogICAgICAgIHRpdGxlOiAn5paH5Lu25qC85byP5LiN5q2j56GuJywNCiAgICAgICAgY29udGVudDogJ+aWh+S7tiAnICsgZmlsZS5uYW1lICsgJ+agvOW8j+S4jeato+ehru+8jOaUr+aMgeS4iuS8oOeahOaWh+S7tuexu+Wei++8mnhscyx4bHN4JywNCiAgICAgICAgb2tUZXh0OiAn56Gu6K6kJw0KICAgICAgfSk7DQogICAgfSwNCiAgICBoYW5kbGVJbXBvcnRFcnJvcihlcnIsIGZpbGUpIHsNCiAgICAgIHRoaXMuJE1lc3NhZ2Uud2FybmluZyhmaWxlLm1lc3NhZ2UpOw0KICAgIH0sDQogICAgaGFuZGxlTWF4U2l6ZSgpIHsNCiAgICAgIHRoaXMuJE1lc3NhZ2Uud2FybmluZygn5Zu+54mH5aSn5bCP5LiN6IO96LaF6L+HMk0uJyk7DQogICAgfSwNCiAgICBjbGFzc0V4cG9ydCgpew0KICAgICAgbGV0IHBhcmFtcyA9IHsuLi50aGlzLnNlYXJjaEZvcm19Ow0KICAgICAgcGFyYW1zWydmaWxlTmFtZSddPSLmiqXlhbPnsbvnm65fIituZXcgRGF0ZSgpLmdldEV4cG9ydEZvcm1hdCgpKyIueGxzIjsNCiAgICAgIHRoaXMubG9hZGluZz10cnVlOw0KICAgICAgQ3VzdG9tQ2xhc3MuZG93bmxvYWQocGFyYW1zLCgpPT57dGhpcy5sb2FkaW5nPWZhbHNlfSkNCiAgICB9LA0KICAgIGNsZWFyYW5jZUV4cG9ydCgpew0KICAgICAgbGV0IHBhcmFtcyA9IHsuLi50aGlzLnNlYXJjaEZvcm19Ow0KICAgICAgcGFyYW1zWydmaWxlTmFtZSddPSLmiqXlhbPnsbvnm65fIituZXcgRGF0ZSgpLmdldEV4cG9ydEZvcm1hdCgpKyIueGxzIjsNCiAgICAgIHRoaXMubG9hZGluZz10cnVlOw0KICAgICAgQ3VzdG9tQ2xhc3MuZG93bmxvYWRDbGVhcmFuY2UocGFyYW1zLCgpPT57dGhpcy5sb2FkaW5nPWZhbHNlfSkNCiAgICB9LA0KDQogICAgLy8g5paw5aKe5L+u5pS557G755uuDQogICAgY2xhc3NBZGQoKSB7DQogICAgICB0aGlzLmNsYXNzTW9kZWxWaXNpYmxlID0gdHJ1ZTsNCiAgICAgIHRoaXMuY2xhc3NUaXRsZSA9ICLmlrDlop7nsbvnm64iOw0KICAgICAgdGhpcy5oYW5kbGVSZXNldEZvcm0oKTsNCiAgICB9LA0KICAgIGNsYXNzRWRpdChzY29wZSkgew0KICAgICAgdGhpcy5jbGFzc01vZGVsVmlzaWJsZSA9IHRydWU7DQogICAgICB0aGlzLmNsYXNzVGl0bGUgPSAi57yW6L6R57G755uuIjsNCiAgICAgIHRoaXMuaGFuZGxlUmVzZXRGb3JtKCk7DQogICAgICB0aGlzLmxvYWRNb2RlbChzY29wZS5yb3cuaWQsZmFsc2UpOw0KICAgIH0sDQogICAgbG9hZE1vZGVsKGlkKXsNCiAgICAgIHRoaXMuc3BpblNob3cgPSB0cnVlOw0KICAgICAgQ3VzdG9tQ2xhc3MuZ2V0QnkoeyJpZCI6IGlkfSkudGhlbihyZXMgPT4gew0KICAgICAgICBpZiAocmVzWydjb2RlJ10gPT09IDApIHsNCiAgICAgICAgICB0aGlzLmZvcm0gPSByZXMuZGF0YTsNCiAgICAgICAgICB0aGlzLmFsbERhdGEuZm9yRWFjaChpdGVtID0+IHsNCiAgICAgICAgICAgIGlmIChpdGVtWydpZCddID09PSB0aGlzLmZvcm0ucGFyZW50SWQpIHsNCiAgICAgICAgICAgICAgdGhpcy5mb3JtLnBhcmVudE5hbWUgPSBpdGVtWydjbGFzc05hbWUnXTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KQ0KICAgICAgICAgIGlmKCF0aGlzLmZvcm0uY2F0ZWdvcnlOYW1lKXsNCiAgICAgICAgICAgIHRoaXMuZm9ybS5jYXRlZ29yeU5hbWU9bnVsbDsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pLmZpbmFsbHkoKCkgPT4gew0KICAgICAgICB0aGlzLnNwaW5TaG93ID0gZmFsc2U7DQogICAgICB9KQ0KICAgIH0sDQogICAgY2FuY2VsRm9ybSgpIHsNCiAgICAgIHRoaXMuY2xhc3NNb2RlbFZpc2libGUgPSBmYWxzZTsNCiAgICAgIHRoaXMuaGFuZGxlUmVzZXRGb3JtKCk7DQogICAgfSwNCiAgICBhZGREZWNsYXJlKCl7DQogICAgICB0aGlzLmRlY2xhcmVEYXRhLnB1c2goe2RlY0tleTonJyxjb250ZW50OicnfSkNCiAgICB9LA0KICAgIGRlbERlY2xhcmUoaW5kZXgpew0KICAgICAgdGhpcy5kZWNsYXJlRGF0YS5zcGxpY2UoaW5kZXgsMSk7DQogICAgfSwNCiAgICBoYW5kbGVSZXNldEZvcm0oKSB7DQogICAgICB0aGlzLiRyZWZzWyJmb3JtIl0ucmVzZXRGaWVsZHMoKTsNCiAgICAgIHRoaXMuZm9ybVsncGFyZW50SWQnXSA9IG51bGw7DQogICAgICB0aGlzLmZvcm1bJ2lkJ10gPSBudWxsOw0KICAgICAgdGhpcy5mb3JtWydsZXZlbCddID0gbnVsbDsNCiAgICB9LA0KICAgIGhhbmRsZVN1Ym1pdCgpIHsNCiAgICAgIHRoaXMuJHJlZnNbJ2Zvcm0nXS52YWxpZGF0ZSgodmFsaWQpID0+IHsNCiAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgdGhpcy5zYXZpbmcgPSB0cnVlOw0KICAgICAgICAgIEN1c3RvbUNsYXNzLnNhdmVDdXN0b21DbGFzcyh0aGlzLmZvcm0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgICAgIGlmIChyZXNbJ2NvZGUnXSA9PT0gMCkgew0KICAgICAgICAgICAgICB0aGlzLmhhbmRsZVNlYXJjaCgpOw0KICAgICAgICAgICAgICB0aGlzLmNhbmNlbEZvcm0oKTsNCiAgICAgICAgICAgICAgdGhpcy5jdXJyZW50SWQgPSBudWxsOw0KICAgICAgICAgICAgICB0aGlzLiRNZXNzYWdlLnN1Y2Nlc3MoJ+S/neWtmOaIkOWKnycpOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pLmZpbmFsbHkoKCkgPT4gew0KICAgICAgICAgICAgdGhpcy5zYXZpbmcgPSBmYWxzZTsNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgLy/liKDpmaTmiqXlhbPnsbvnm64NCiAgICBjbGFzc1JlbW92ZShzY29wZSkgew0KICAgICAgdGhpcy4kTW9kYWwuY29uZmlybSh7DQogICAgICAgIHRpdGxlOiAn5o+Q56S677yBJywNCiAgICAgICAgY29udGVudDogJ+aCqOehruWumuWIoOmZpOi/meadoeaVsOaNruWQl++8nycsDQogICAgICAgIG9uT2s6ICgpID0+IHsNCiAgICAgICAgICB0aGlzLnNhdmluZyA9IHRydWU7DQogICAgICAgICAgQ3VzdG9tQ2xhc3MucmVtb3ZlKHtpZDogc2NvcGUucm93LmlkfSkudGhlbihyZXMgPT4gew0KICAgICAgICAgICAgaWYgKHJlc1snY29kZSddID09PSAwKSB7DQogICAgICAgICAgICAgIHRoaXMuY3VycmVudElkID0gbnVsbDsNCiAgICAgICAgICAgICAgdGhpcy4kTWVzc2FnZS5zdWNjZXNzKCfliKDpmaTmiJDlip8nKTsNCiAgICAgICAgICAgICAgdGhpcy5oYW5kbGVTZWFyY2goKTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KS5maW5hbGx5KCgpID0+IHsNCiAgICAgICAgICAgIHRoaXMuc2F2aW5nID0gZmFsc2UNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8v5p+l55yL5oql5YWz57G755uuDQogICAgY2xhc3NMb29rKHNjb3BlKXsNCiAgICAgIGNvbnN0IHsgY2F0ZWdvcnlWaWV3UmVmfSA9IHRoaXMuJHJlZnM7DQogICAgICBpZiAoY2F0ZWdvcnlWaWV3UmVmKSB7DQogICAgICAgIGNhdGVnb3J5Vmlld1JlZi5zZXREZWZhdWx0KHNjb3BlLnJvdy5pZCk7DQogICAgICB9DQogICAgICB0aGlzLmNsYXNzVmlld1Zpc2libGUgPSB0cnVlOw0KICAgIH0sDQogICAgLy/mlrDlop4v5L+u5pS5L+WIoOmZpOeUs+aKpeimgee0oA0KICAgIGRlY2xhcmVFZGl0KHNjb3BlKXsNCiAgICAgIHRoaXMuZGVjbGFyZVRpdGxlID0gIue7tOaKpOeUs+aKpeimgee0oOexu+ebrjoiK3Njb3BlLnJvdy5jbGFzc05hbWU7DQogICAgICB0aGlzLmRpc2FibGVkID0gZmFsc2U7DQogICAgICBpZih0aGlzLmRlY2xhcmVDb2x1bW4ubGVuZ3RoIDw9Mil7DQogICAgICAgIHRoaXMuZGVjbGFyZUNvbHVtbi5wdXNoKHt0aXRsZTogJ+aTjeS9nCcsa2V5OiAnYWN0aW9uJyxtaW5XaWR0aDogMTIwLCBhbGlnbjogJ2NlbnRlcicsc2xvdDonZGVjQWN0aW9uJ30pOw0KICAgICAgfQ0KICAgICAgdGhpcy5jdXJyZW50SWQgPSBzY29wZS5yb3cuaWQ7DQogICAgICB0aGlzLmRlY2xhcmVNb2RlbFZpc2libGU9IHRydWU7DQogICAgICB0aGlzLmxvYWRpbmc9dHJ1ZTsNCiAgICAgIEN1c3RvbUNsYXNzLmdldERlY2xhcmVFbGVtZW50KHsiaWQiOnRoaXMuY3VycmVudElkfSkudGhlbihyZXM9PnsNCiAgICAgICAgdGhpcy5kZWNsYXJlRGF0YSA9IHJlc1snZGF0YSddOw0KICAgICAgfSkuZmluYWxseSgoKT0+e3RoaXMubG9hZGluZz1mYWxzZTt9KQ0KICAgIH0sDQogICAgY2FuY2VsRGVjbGFyZSgpew0KICAgICAgdGhpcy5kZWNsYXJlTW9kZWxWaXNpYmxlID0gZmFsc2U7DQogICAgICB0aGlzLmN1cnJlbnRJZCA9IG51bGw7DQogICAgfSwNCiAgICBzYXZlRGVjbGFyZSgpew0KICAgICAgdGhpcy5kZWNsYXJlRGF0YS5mb3JFYWNoKGl0ZW09Pml0ZW1bJ3BhcmVudElkJ109dGhpcy5jdXJyZW50SWQpOw0KICAgICAgdGhpcy5zYXZpbmc9dHJ1ZTsNCiAgICAgICh0aGlzLmRlY2xhcmVEYXRhLmxlbmd0aD4wP0N1c3RvbUNsYXNzLnNhdmVEZWNsYXJlRWxlbWVudCh0aGlzLmRlY2xhcmVEYXRhKTpDdXN0b21DbGFzcy5kZWxEZWNsYXJlRWxlbWVudCh7ImlkIjp0aGlzLmN1cnJlbnRJZH0pKQ0KICAgICAgICAudGhlbihyZXM9PnsNCiAgICAgICAgICBpZiAocmVzWydjb2RlJ10gPT09IDApIHsNCiAgICAgICAgICAgIHRoaXMuaGFuZGxlU2VhcmNoKCk7DQogICAgICAgICAgICB0aGlzLmNhbmNlbERlY2xhcmUoKTsNCiAgICAgICAgICAgIHRoaXMuJE1lc3NhZ2Uuc3VjY2Vzcygn5aSE55CG5oiQ5YqfJyk7DQogICAgICAgICAgfQ0KICAgICAgICB9KS5maW5hbGx5KCgpPT57dGhpcy5zYXZpbmc9ZmFsc2U7fSkNCiAgICB9LA0KDQogICAgLy/mlrDlop4v5L+u5pS5L+WIoOmZpOa4heWFs+i1hOaWmQ0KICAgIGNsZWFyYW5jZUVkaXQoc2NvcGUpew0KICAgICAgdGhpcy5jbGVhcmFuY2VUaXRsZSA9ICLnu7TmiqTmuIXlhbPkv6Hmga86IitzY29wZS5yb3cuY2xhc3NOYW1lOw0KICAgICAgdGhpcy5kaXNhYmxlZCA9IGZhbHNlOw0KICAgICAgaWYodGhpcy5jbGVhcmFuY2VDb2x1bW4ubGVuZ3RoIDw9NCl7DQogICAgICAgIHRoaXMuY2xlYXJhbmNlQ29sdW1uLnB1c2goe3RpdGxlOiAn5pON5L2cJyxrZXk6ICdhY3Rpb24nLG1pbldpZHRoOiAxMjAsIGFsaWduOiAnY2VudGVyJyxzbG90OidjbGVhcmFuY2VBY3Rpb24nfSk7DQogICAgICB9DQogICAgICB0aGlzLmxvYWRDbGVhcmFuY2VFbGVtZW50KHNjb3BlKTsNCiAgICB9LA0KICAgIGdldENsZWFyYW5jZUVsZW1lbnQoc2NvcGUpew0KICAgICAgdGhpcy5jbGVhcmFuY2VUaXRsZSA9ICLmn6XnnIvmuIXlhbPkv6Hmga86IitzY29wZS5yb3cuY2xhc3NOYW1lOw0KICAgICAgdGhpcy5kaXNhYmxlZCA9IHRydWU7DQogICAgICBpZih0aGlzLmNsZWFyYW5jZUNvbHVtbi5sZW5ndGggPj01KXsNCiAgICAgICAgdGhpcy5jbGVhcmFuY2VDb2x1bW4uc3BsaWNlKDQsMSk7DQogICAgICB9DQogICAgICB0aGlzLmxvYWRDbGVhcmFuY2VFbGVtZW50KHNjb3BlKTsNCiAgICB9LA0KICAgIGxvYWRDbGVhcmFuY2VFbGVtZW50KHNjb3BlKXsNCiAgICAgIHRoaXMuY3VycmVudElkID0gc2NvcGUucm93LmlkOw0KICAgICAgdGhpcy5jbGVhcmFuY2VNb2RlbFZpc2libGU9IHRydWU7DQogICAgICB0aGlzLmxvYWRpbmc9dHJ1ZTsNCiAgICAgIEN1c3RvbUNsYXNzLmdldENsZWFyYW5jZUVsZW1lbnQoeyJpZCI6dGhpcy5jdXJyZW50SWR9KS50aGVuKHJlcz0+ew0KICAgICAgICB0aGlzLmNsZWFyYW5jZURhdGEgPSByZXNbJ2RhdGEnXTsNCiAgICAgIH0pLmZpbmFsbHkoKCk9Pnt0aGlzLmxvYWRpbmc9ZmFsc2U7fSkNCiAgICB9LA0KICAgIGNhbmNlbENsZWFyYW5jZSgpew0KICAgICAgdGhpcy5jbGVhcmFuY2VNb2RlbFZpc2libGUgPSBmYWxzZTsNCiAgICAgIHRoaXMuY3VycmVudElkID0gbnVsbDsNCiAgICB9LA0KICAgIGFkZENsZWFyYW5jZSgpew0KICAgICAgdGhpcy5jbGVhcmFuY2VEYXRhLnB1c2goe30pDQogICAgfSwNCiAgICBkZWxDbGVhcmFuY2UoaW5kZXgpew0KICAgICAgdGhpcy5jbGVhcmFuY2VEYXRhLnNwbGljZShpbmRleCwxKTsNCiAgICB9LA0KICAgIHNhdmVDbGVhcmFuY2UoKXsNCiAgICAgIHRoaXMuY2xlYXJhbmNlRGF0YS5mb3JFYWNoKGl0ZW09Pml0ZW1bJ3BhcmVudElkJ109dGhpcy5jdXJyZW50SWQpOw0KICAgICAgdGhpcy5zYXZpbmc9dHJ1ZTsNCiAgICAgICh0aGlzLmNsZWFyYW5jZURhdGEubGVuZ3RoPjA/Q3VzdG9tQ2xhc3Muc2F2ZUNsZWFyYW5jZUVsZW1lbnQodGhpcy5jbGVhcmFuY2VEYXRhKTpDdXN0b21DbGFzcy5kZWxDbGVhcmFuY2VFbGVtZW50KHsiaWQiOnRoaXMuY3VycmVudElkfSkpDQogICAgICAgIC50aGVuKHJlcz0+ew0KICAgICAgICAgIGlmIChyZXNbJ2NvZGUnXSA9PT0gMCkgew0KICAgICAgICAgICAgdGhpcy5oYW5kbGVTZWFyY2goKTsNCiAgICAgICAgICAgIHRoaXMuY2FuY2VsQ2xlYXJhbmNlKCk7DQogICAgICAgICAgICB0aGlzLiRNZXNzYWdlLnN1Y2Nlc3MoJ+S/neWtmOaIkOWKnycpOw0KICAgICAgICAgIH0NCiAgICAgICAgfSkuZmluYWxseSgoKT0+e3RoaXMuc2F2aW5nPWZhbHNlO30pDQogICAgfSwNCg0KICAgIC8v5pel5b+XDQogICAgbG9va0xvZyhzY29wZSkgew0KICAgICAgY29uc3Qge2xvZ01vZGVsUmVmfSA9IHRoaXMuJHJlZnM7DQogICAgICBpZiAobG9nTW9kZWxSZWYpIHsNCiAgICAgICAgbG9nTW9kZWxSZWYuc2V0RGVmYXVsdChzY29wZS5yb3cuaWQsIHRoaXMucmVmVHlwZSk7DQogICAgICB9DQogICAgICB0aGlzLmxvZ1Zpc2libGUgPSB0cnVlOw0KICAgIH0sDQogICAgZ2V0TG9nUmVmVHlwZSgpIHsNCiAgICAgIEN1c3RvbUNsYXNzLmdldExvZ1JlZlR5cGUoKS50aGVuKHJlcyA9PiB7DQogICAgICAgIGlmIChyZXNbJ2NvZGUnXSA9PT0gMCkgew0KICAgICAgICAgIHRoaXMucmVmVHlwZSA9IHJlcy5kYXRhOw0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgLy/liqDovb3mlbDmja4NCiAgICBsaXN0QWxsU3B1KCkgew0KICAgICAgbGlzdEFsbFNwdSh7fSkudGhlbigocmVzKSA9PiB7DQogICAgICAgIGlmIChyZXNbJ2NvZGUnXSA9PT0gMCkgew0KICAgICAgICAgIHRoaXMuc3B1TGlzdCA9IFt7InNwdSI6IuacjemlsCIsInNwdU5hbWUiOiLmnI3ppbAiLGNoaWxkcmVuOnJlcy5kYXRhLmZpbHRlcihpdGVtPT5pdGVtWydzcHVOYW1lJ10uaW5kZXhPZigiIyIpPDApfV07DQogICAgICAgICAgdGhpcy5kaUd1aVRyZWUodGhpcy5zcHVMaXN0KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgZGlHdWlUcmVlKGl0ZW0pIHsgIC8v6YCS5b2S5L6/5Yip5qCR57uT5p6EDQogICAgICBpdGVtLmZvckVhY2goaXRlbSA9PiB7DQogICAgICAgIGl0ZW0uaWQgPSBpdGVtWydzcHUnXTsNCiAgICAgICAgaXRlbS5sYWJlbCA9IGl0ZW1bJ3NwdU5hbWUnXTsNCiAgICAgICAgIWl0ZW1bJ2NoaWxkcmVuJ10gfHwgaXRlbVsnY2hpbGRyZW4nXS5sZW5ndGggPT09IDAgPyBkZWxldGUgaXRlbS5jaGlsZHJlbiA6IHRoaXMuZGlHdWlUcmVlKGl0ZW0uY2hpbGRyZW4pOw0KICAgICAgfSkNCiAgICB9LA0KICAgIGxpc3REZWNsYXJlVHlwZUxpc3QoKXsNCiAgICAgIENvbW1vbkFwaS5MaXN0RGljdGlvbmFyeVZhbHVlQnkoImRlY2xhcmVfZWxlbWVudCIpLnRoZW4ocmVzPT57DQogICAgICAgIGlmKHJlcyAmJiByZXNbJ2NvZGUnXSA9PT0wKXsNCiAgICAgICAgICB0aGlzLmRlY2xhcmVUeXBlTGlzdCA9IHJlc1snZGF0YSddfHxbXTsNCiAgICAgICAgICBsZXQgaW5kZXggPSAwOw0KICAgICAgICAgIHRoaXMuZGVjbGFyZVR5cGVMaXN0LmZvckVhY2goaXRlbT0+ew0KICAgICAgICAgICAgdGhpcy5jb2x1bW4uc3BsaWNlKC0yLDAsIHt0aXRsZTogaXRlbVsndmFsdWUnXSxrZXk6ICdkZWMnK2luZGV4KyssbWluV2lkdGg6IDEyMCwgYWxpZ246ICdjZW50ZXInLHJlc2l6YWJsZTp0cnVlLHJlbmRlcjpudWxsfSk7DQogICAgICAgICAgfSkNCiAgICAgICAgICB0aGlzLmhhbmRsZVNlYXJjaCgpOw0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgaGFuZGxlQ3VycmVuY3koKSB7DQogICAgICBDdXJyZW5jeS5nZXRBbGwoKS50aGVuKHJlcyA9PiB7DQogICAgICAgIHRoaXMuY3VycmVuY3lMaXN0ID0gcmVzLmRhdGE7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8v6I635Y+W55uu55qE5Zu95a62DQogICAgZ2V0Q291bnRyeUxpc3QoKSB7DQogICAgICBDb21tb25BcGkuTGlzdERpY3Rpb25hcnlWYWx1ZUJ5KCJiYXNlX2NvdW50cnkiKS50aGVuKHJlcz0+ew0KICAgICAgICBpZihyZXMgJiYgcmVzWydjb2RlJ10gPT09MCl7DQogICAgICAgICAgbGV0IGRhdGEgPSByZXNbJ2RhdGEnXQ0KICAgICAgICAgIGlmKGRhdGEpew0KICAgICAgICAgICAgdGhpcy5jb3VudHJ5TGlzdCA9IGRhdGEubWFwKGl0ZW09PkpTT04ucGFyc2UoaXRlbS52YWx1ZSkpOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAs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file": "index.vue", "sourceRoot": "src/view/module/custom/base/customClass", "sourcesContent": ["<!--\r\n@create date 2020-07-09\r\n@desc 报关类目\r\n-->\r\n<template>\r\n  <div class=\"customClass\">\r\n      <Card :shadow=\"true\">\r\n        <Form ref=\"searchForm\" :model=\"searchForm\" inline @keydown.native.enter.prevent=\"handleSearch\">\r\n          <FormItem prop=\"className\">\r\n            <Input type=\"text\" v-model=\"searchForm.className\" placeholder=\"请输入类目名称\"/>\r\n          </FormItem>\r\n          <FormItem prop=\"categoryName\">\r\n            <Input type=\"text\" v-model=\"searchForm.categoryName\" placeholder=\"请输入产品型号\"/>\r\n          </FormItem>\r\n          <FormItem prop=\"hsCode\">\r\n            <Input type=\"text\" v-model=\"searchForm.hsCode\" placeholder=\"请输入报关海关编码\"/>\r\n          </FormItem>\r\n          <FormItem>\r\n            <Button type=\"primary\" @click=\"handleSearch\" :loading=\"loading\">查询</Button>\r\n            <Button style=\"margin-left:10px\" @click=\"handleReset\">重置</Button>\r\n          </FormItem>\r\n        </Form>\r\n        <div style=\"margin-bottom: 10px\">\r\n          <div style=\"float:left\">\r\n            <Upload ref=\"uploadClassFileRef\" name=\"importFile\" :action=\"importClassURl\" :max-size=\"10240\"\r\n                    :on-success=\"handleImportClassSuccess\" :format=\"['xls', 'xlsx']\" :show-upload-list=\"false\" :on-format-error=\"handleImportFormatError\"\r\n                    :on-error=\"handleImportError\" :headers=\"loginInfo\" :on-exceeded-size=\"handleMaxSize\">\r\n              <Button class=\"search-btn\" type=\"primary\">导入类目</Button>\r\n            </Upload></div>\r\n          <Button class=\"search-btn\" style=\"margin-left:10px;\" @click=\"classAdd\">添加类目</Button>\r\n          <Button class=\"search-btn\" style=\"margin-left:10px;\" @click=\"classExport\">导出类目</Button>\r\n          <div style=\"float:left\">\r\n            <Upload ref=\"uploadClearanceFileRef\" name=\"importFile\" :action=\"importClearanceURl\" :max-size=\"10240\"\r\n                    :on-success=\"handleImportClearanceSuccess\" :format=\"['xls', 'xlsx']\" :show-upload-list=\"false\" :on-format-error=\"handleImportFormatError\"\r\n                    :on-error=\"handleImportError\" :headers=\"loginInfo\" :on-exceeded-size=\"handleMaxSize\">\r\n              <Button class=\"search-btn\" style=\"margin-left:10px;\">导入清关资料</Button>\r\n            </Upload></div>\r\n          <Button class=\"search-btn\" style=\"margin-left:10px;\" @click=\"clearanceExport\">导出清关资料</Button>\r\n        </div>\r\n        <tree-table ref=\"treeTableRef\" expand-key=\"className\" :expand-type=\"false\" :selectable=\"false\" :columns=\"column\" :data=\"data\"\r\n                    @radio-click=\"loadClassChild\" @clickRow=\"loadClassChild\" :border=\"true\" :row-class-name=\"rowClassName\">\r\n          <template v-slot:action=\"scope\">\r\n            <Button size=\"small\" type=\"info\" @click=\"classLook(scope)\" style=\"margin:0 2px\">查看</Button>\r\n            <Button size=\"small\" type=\"info\" @click=\"classEdit(scope)\" v-if=\"scope.row.parentId>0\" style=\"margin:0 2px\">编辑</Button>\r\n            <Button size=\"small\" type=\"info\" @click=\"classRemove(scope)\" style=\"margin:0 2px\">删除</Button>\r\n            <Button size=\"small\" type=\"info\" @click=\"declareEdit(scope)\" v-if=\"scope.row.parentId>0\" style=\"margin:0 2px\">申报要素</Button>\r\n            <Button size=\"small\" type=\"info\" @click=\"clearanceEdit(scope)\" style=\"margin:0 2px\">清关资料</Button>\r\n            <Button size=\"small\" type=\"info\" @click=\"lookLog(scope)\" style=\"margin:0 2px\">日志</Button>\r\n          </template>\r\n          <template v-slot:clearanceElement=\"scope\">\r\n            <Button size=\"small\" type=\"info\" @click=\"getClearanceElement(scope)\" style=\"margin:0 2px\">清关信息</Button>\r\n          </template>\r\n        </tree-table>\r\n      </Card>\r\n\r\n    <!-- 报关类目 模块 修改新增model -->\r\n    <Modal :width=\"680\" v-model=\"classModelVisible\" :mask-closable=\"false\" :title=\"classTitle\" @on-cancel=\"cancelForm\">\r\n      <Spin size=\"large\" v-if=\"spinShow\" :fix=\"true\">加载中...</Spin>\r\n      <Form ref=\"form\" :model=\"form\" label-position=\"left\" :rules=\"ruleValidate\" :label-width=\"100\" inline>\r\n        <FormItem label=\"上级目录\" prop=\"parentName\">\r\n          <Poptip placement=\"right-start\" width=\"230\" class=\"superClass\" title=\"上级目录\">\r\n            <Input v-model=\"form.parentName\" :readonly=\"true\" placeholder=\"请选择\" style=\"width:200px;\"></Input>\r\n            <div class=\"treeDiv\" slot=\"content\">\r\n              <Tree :data=\"treeData\" @on-select-change=\"selectParent\"></Tree>\r\n            </div>\r\n          </Poptip>\r\n        </FormItem>\r\n        <FormItem label=\"类目名称\" prop=\"className\">\r\n          <Input v-model=\"form.className\" placeholder=\"请输入报关类目名称\" style=\"width:200px;\"></Input>\r\n        </FormItem>\r\n        <FormItem label=\"产品型号\" prop=\"categoryName\">\r\n          <div class=\"setClass\" style=\"width:200px;\">\r\n            <treeselect v-model=\"form.categoryName\"\r\n                        :options=\"spuList\"\r\n                        :defaultExpandLevel=\"1\"\r\n                        :autoLoadRootOptions=\"true\"\r\n                        noResultsText=\"暂无数据\"\r\n                        placeholder=\"请选择产品型号\"/>\r\n          </div>\r\n        </FormItem>\r\n        <FormItem label=\"中文报关名\" prop=\"customNameCn\">\r\n          <Input v-model=\"form.customNameCn\" placeholder=\"中文报关名\" style=\"width:200px;\"></Input>\r\n        </FormItem>\r\n        <FormItem label=\"英文报关名\" prop=\"customNameEn\">\r\n          <Input v-model=\"form.customNameEn\" placeholder=\"英文报关名\" style=\"width:200px;\"></Input>\r\n        </FormItem>\r\n        <FormItem label=\"报关单位\" prop=\"unit\">\r\n          <Input v-model=\"form.unit\" placeholder=\"请输入报关类目名称\" style=\"width:200px;\"></Input>\r\n        </FormItem>\r\n        <FormItem label=\"报关海关编码\" prop=\"hsCode\">\r\n          <Input v-model=\"form.hsCode\" placeholder=\"请输入报关类目名称\" style=\"width:200px;\"></Input>\r\n        </FormItem>\r\n        <FormItem label=\"材质\" prop=\"material\">\r\n          <Input v-model=\"form.material\" placeholder=\"请输入材质\" style=\"width:200px;\"></Input>\r\n        </FormItem>\r\n        <FormItem prop=\"purpose\" label=\"用途\">\r\n          <Input v-model=\"form.purpose\" type=\"textarea\" :autosize=\"{minRows: 1,maxRows: 3}\"\r\n                 placeholder=\"请输入用途\" style=\"width:200px;\"/>\r\n        </FormItem>\r\n      </Form>\r\n      <div slot=\"footer\">\r\n        <Button type=\"default\" @click=\"cancelForm\">取消</Button>&nbsp;\r\n        <Button type=\"primary\" @click=\"handleSubmit()\" :loading=\"saving\">提交</Button>\r\n      </div>\r\n    </Modal>\r\n\r\n    <!-- 申报要素 -->\r\n    <Modal v-model=\"declareModelVisible\" class=\"modelBox\" :title=\"declareTitle\" width=\"600\" @on-cancel=\"cancelDeclare\">\r\n      <Spin :fix=\"true\" v-if=\"spinShow\">加载中...</Spin>\r\n      <Button class=\"search-btn\" type=\"primary\" size=\"small\" style=\"margin-left:15px\" @click=\"addDeclare()\" v-if=\"!disabled\">添加</Button>\r\n      <Table :border=\"true\" :columns=\"declareColumn\" :data=\"declareData\" :loading=\"loading\">\r\n        <template v-slot:decKey=\"{index}\">\r\n          <Select v-model=\"declareData[index].decKey\" transfer placeholder=\"请选择\" :disabled=\"disabled\">\r\n            <Option v-for=\"(item) in declareTypeList\" :value=\"item['value']\" :key=\"item['value']\">{{item['value']}}</Option>\r\n          </Select>\r\n        </template>\r\n        <template v-slot:decContent=\"{index}\">\r\n          <Input v-model=\"declareData[index].content\" type=\"textarea\" :autosize=\"{minRows: 1,maxRows: 4}\" placeholder=\"请输入\" :disabled=\"disabled\"/>\r\n        </template>\r\n        <template v-slot:decAction=\"{index}\">\r\n          <a href=\"javascript:void(0)\" v-if=\"!disabled\" @click=\"delDeclare(index)\">删除</a>\r\n        </template>\r\n      </Table>\r\n      <div slot=\"footer\">\r\n        <Button  type=\"primary\" :loading=\"saving\" @click=\"saveDeclare()\" v-if=\"!disabled\">保存</Button>\r\n        <Button @click=\"cancelDeclare\">取消</Button>\r\n      </div>\r\n    </Modal>\r\n\r\n    <!-- 清关资料 -->\r\n    <Modal v-model=\"clearanceModelVisible\" class=\"modelBox\" :title=\"clearanceTitle\" width=\"900\" @on-cancel=\"cancelClearance\">\r\n      <Spin :fix=\"true\" v-if=\"spinShow\">加载中...</Spin>\r\n      <Button class=\"search-btn\" type=\"primary\" size=\"small\" style=\"margin-left:15px\" @click=\"addClearance()\" v-if=\"!disabled\">添加</Button>\r\n      <Table :border=\"true\" :columns=\"clearanceColumn\" :data=\"clearanceData\" :loading=\"loading\">\r\n        <template v-slot:country=\"{index}\">\r\n          <Select v-model=\"clearanceData[index].country\" transfer placeholder=\"请选择\" :disabled=\"disabled\">\r\n            <Option v-for=\"(item,index) in countryList\" :value=\"item['two_code']\" :key=\"index\">{{ item['name_cn'] }}</Option>\r\n          </Select>\r\n        </template>\r\n        <template v-slot:hsCode=\"{index}\">\r\n          <Input v-model=\"clearanceData[index].hsCode\" type=\"text\" placeholder=\"请输入\" :disabled=\"disabled\"/>\r\n        </template>\r\n        <template v-slot:price=\"{index}\">\r\n          <Input v-model=\"clearanceData[index].price\" type=\"text\" placeholder=\"请输入\" :disabled=\"disabled\"/>\r\n        </template>\r\n        <template v-slot:currency=\"{index}\">\r\n          <Select v-model=\"clearanceData[index].currency\" transfer placeholder=\"请选择\" :disabled=\"disabled\">\r\n            <Option v-for=\"(item) in currencyList\" :value=\"item['id']\" :key=\"item['id']\">{{item['name']}}</Option>\r\n          </Select>\r\n        </template>\r\n        <template v-slot:clearanceAction=\"{index}\">\r\n          <a href=\"javascript:void(0)\" v-if=\"!disabled\" @click=\"delClearance(index)\">删除</a>\r\n        </template>\r\n      </Table>\r\n      <div slot=\"footer\">\r\n        <Button  type=\"primary\" :loading=\"saving\" @click=\"saveClearance()\" v-if=\"!disabled\">保存</Button>\r\n        <Button @click=\"cancelClearance\">取消</Button>\r\n      </div>\r\n    </Modal>\r\n    <!-- 报关类目明细查看 -->\r\n    <CategoryView ref=\"categoryViewRef\" :modelViewVisible=\"classViewVisible\" :onCancel=\"()=>classViewVisible=false\" :allData=\"this.allData\" :currencyList=\"currencyList\" :countryList=\"countryList\"/>\r\n    <!-- 日志模块 -->\r\n    <LogModel ref=\"logModelRef\" :logVisible=\"logVisible\" :onCancel=\"()=>logVisible=false\"/>\r\n  </div>\r\n</template>\r\n<script>\r\nimport CustomClass from \"@/api/custom/customClass\";\r\nimport {listAllSpu} from '@/api/basf/product.js'\r\nimport CommonApi from \"@/api/base/commonApi\";\r\nimport Currency from \"@/api/basf/currency\";\r\nimport CategoryView from \"@/view/module/custom/base/customClass/indexView.vue\";\r\nimport LogModel from \"@/view/module/base/bussinessLog/logModel.vue\";\r\nimport {getToken, getUrl} from \"@/libs/util\";\r\nexport default {\r\n  name: 'category',\r\n  components: {LogModel, CategoryView},\r\n  data() {\r\n    return {\r\n      searchForm: {\r\n        className: '',\r\n        categoryName: '',\r\n        hsCode: '',\r\n      },\r\n      currentId: null,//当前操作的数据ID\r\n      column: [\r\n        {title: '类目名称',key: 'className', minWidth: 180, align: 'left',resizable:true,render:(_,{row})=>(<span v-copytext={row.className}>{row.className}</span>)},\r\n        {title: '产品型号', key: 'categoryName',minWidth: 120, align: 'center',resizable:true,render:(_,{row})=>(<span v-copytext={row.categoryName}>{row.categoryName}</span>)},\r\n        {title: '中文报关名', key: 'customNameCn',minWidth: 120, align: 'center',resizable:true,render:(_,{row})=>(<span v-copytext={row.customNameCn}>{row.customNameCn}</span>)},\r\n        {title: '英文报关名', key: 'customNameEn',minWidth: 120, align: 'center',resizable:true,render:(_,{row})=>(<span v-copytext={row.customNameEn}>{row.customNameEn}</span>)},\r\n        {title: '报关海关编码',key: 'hsCode',minWidth: 120, align: 'center',resizable:true,render:(_,{row})=>(<span v-copytext={row.hsCode}>{row.hsCode}</span>)},\r\n        {title: '材质',key: 'material',minWidth: 120, align: 'center',resizable:true,render:(_,{row})=>(<span v-copytext={row.material}>{row.material}</span>)},\r\n        {title: '用途',key: 'purpose',minWidth: 120, align: 'center',resizable:true,render:(_,{row})=>(<span v-copytext={row.purpose}>{row.purpose}</span>)},\r\n        {title: '报关单位',key: 'unit',minWidth: 120, align: 'center',resizable:true,render:(_,{row})=>(<span v-copytext={row.unit}>{row.unit}</span>)},\r\n        {title: '清关数据',key: 'clearanceElement',width: 120, align: 'center',resizable:true,type: 'template',template: 'clearanceElement'},\r\n        {title: '操作',width: 200, type: 'template',template: 'action'}\r\n      ],\r\n      data: [],\r\n      allData: [],//包含明细类目的list数据格式\r\n      loading: false,\r\n      saving: false,\r\n      clickNode: {},\r\n      // 新增编辑\r\n      classModelVisible: false,\r\n      form: {parentName: null, parentId: null, className: null, customNameCn: null, customNameEn: null, material: null, purpose: null, unit: null, hsCode: null,},\r\n      disabled:false,\r\n      spinShow: false,\r\n      classTitle: '',\r\n      treeData:[],\r\n      ruleValidate: {\r\n        className: [\r\n          {required: true, message: '请输入类目名称', trigger: 'blur'}\r\n        ],\r\n      },\r\n      //查看明细\r\n      classViewVisible:false,\r\n      //申报要素\r\n      declareModelVisible:false,\r\n      declareColumn:[{title: '类型',key: 'decKey', minWidth: 120, align: 'center',slot:'decKey'},\r\n        {title: '内容', key: 'content',minWidth: 120, align: 'center',slot:'decContent'},\r\n        {title: '操作',key: 'action',width: 100, align: 'center',slot:'decAction'}],\r\n      declareData:[],\r\n      declareTitle:'',\r\n\r\n      //清关资料\r\n      clearanceModelVisible:false,\r\n      clearanceColumn:[{title: '国家',key: 'country', minWidth: 120, align: 'center',slot:'country'},\r\n        {title: '清关编码', key: 'hsCode',minWidth: 120, align: 'center',slot:'hsCode'},\r\n        {title: '清关价格', key: 'price',minWidth: 120, align: 'center',slot:'price'},\r\n        {title: '清关币种', key: 'currency',minWidth: 120, align: 'center',slot:'currency'},\r\n        {title: '操作',key: 'action',width: 100, align: 'center',slot:'clearanceAction'}],\r\n      clearanceData:[],\r\n      clearanceTitle:'',\r\n\r\n      logVisible:false,\r\n      //导入类目\r\n      importClassURl: getUrl() + \"/base/customClass/importClassFile\",\r\n      //导入清关资料\r\n      importClearanceURl: getUrl() + \"/base/customClass/importClearanceFile\",\r\n      loginInfo: {\r\n        Accept: 'application/json,text/plain, */*, */*',\r\n        mode: 'cors',\r\n        Authorization: 'Bearer ' + getToken()\r\n      },\r\n      //型号列表\r\n      spuList: [],\r\n      //申报要素类型\r\n      declareTypeList:[],\r\n      //币种\r\n      currencyList:[],\r\n      //国家\r\n      countryList:[],\r\n      //日志类型\r\n      refType:null,\r\n    }\r\n  },\r\n  mounted() {\r\n    this.listAllSpu();//获取产品类目List\r\n    this.listDeclareTypeList();//获取产品类目List\r\n    this.handleCurrency();//获取所有币种\r\n    this.getCountryList();//获取所有国家\r\n    this.getLogRefType();//获取日志类型\r\n    this.handleSearch();//获取所有数据\r\n  },\r\n  methods: {\r\n    //查询，重置\r\n    handleSearch() {\r\n      CustomClass.listTree(this.searchForm).then(res => {\r\n        if (res['code'] === 0) {\r\n          this.data = res.data;\r\n          this.treeData = res.data;//如果有新增的数据,需要重新打开页签才能加载到\r\n          this.allData = [];\r\n          this.setTitle(this.treeData)\r\n          let declareTypeObj = {};\r\n          let index = 0;\r\n          this.declareTypeList.forEach(item=>declareTypeObj[item['value']] = 'dec'+ index++)\r\n          this.setDeclarationElement(this.data,declareTypeObj)\r\n        }\r\n      })\r\n    },\r\n    setTitle(data){\r\n      if(data && data.length>0){\r\n        data.forEach(item => {\r\n          this.allData.push(item);\r\n          item['title'] = item['className'];\r\n          this.setTitle(item['children']);\r\n        })\r\n      }\r\n    },\r\n    setDeclarationElement(dataList,declareTypeObj){\r\n      if(!dataList || dataList.length<=0){\r\n        return;\r\n      }\r\n      dataList.forEach(item=>{\r\n        if(item['declarationElementList'] !=null && item['declarationElementList'].length !==0){\r\n          item['declarationElementList'].forEach(declarationElement=>{\r\n            let decKey = declareTypeObj[declarationElement['decKey']];\r\n            if(decKey !=null){\r\n              item[decKey] = declarationElement['content'];\r\n            }\r\n          })\r\n        }\r\n        this.setDeclarationElement(item['children'],declareTypeObj);\r\n      })\r\n    },\r\n    handleReset() {\r\n      this.$refs['searchForm'].resetFields();\r\n    },\r\n\r\n    loadClassChild(params) {\r\n      this.clickNode = params.row || {};\r\n      this.currentId = params.row.id;\r\n    },\r\n    rowClassName(row) {\r\n      const { clickNode } = this;\r\n      if(clickNode.id === row.id) return 'specialBackground';\r\n    },\r\n    //关联类目\r\n    selectParent(row) {\r\n      this.form.parentName = row[0].className;\r\n      this.form.level = row[0].level + 1;\r\n      this.form.parentId = row[0].id\r\n    },\r\n\r\n    //导入类目数据和清关资料数据\r\n    handleImportClassSuccess(res){\r\n      this.$refs['uploadClassFileRef'].clearFiles();\r\n      this.handleImportSuccess(res);\r\n    },\r\n    handleImportClearanceSuccess(res){\r\n      this.$refs['uploadClearanceFileRef'].clearFiles();\r\n      this.handleImportSuccess(res);\r\n    },\r\n    handleImportSuccess(res) {\r\n      if (res['code'] === 0) {\r\n        this.$Message.success('导入成功');\r\n        this.handleSearch();\r\n      } else {\r\n        this.$Message.warning(res['message']);\r\n      }\r\n    },\r\n    handleImportFormatError(file) {\r\n      //格式验证失败的钩子\r\n      this.$Modal.error({\r\n        title: '文件格式不正确',\r\n        content: '文件 ' + file.name + '格式不正确，支持上传的文件类型：xls,xlsx',\r\n        okText: '确认'\r\n      });\r\n    },\r\n    handleImportError(err, file) {\r\n      this.$Message.warning(file.message);\r\n    },\r\n    handleMaxSize() {\r\n      this.$Message.warning('图片大小不能超过2M.');\r\n    },\r\n    classExport(){\r\n      let params = {...this.searchForm};\r\n      params['fileName']=\"报关类目_\"+new Date().getExportFormat()+\".xls\";\r\n      this.loading=true;\r\n      CustomClass.download(params,()=>{this.loading=false})\r\n    },\r\n    clearanceExport(){\r\n      let params = {...this.searchForm};\r\n      params['fileName']=\"报关类目_\"+new Date().getExportFormat()+\".xls\";\r\n      this.loading=true;\r\n      CustomClass.downloadClearance(params,()=>{this.loading=false})\r\n    },\r\n\r\n    // 新增修改类目\r\n    classAdd() {\r\n      this.classModelVisible = true;\r\n      this.classTitle = \"新增类目\";\r\n      this.handleResetForm();\r\n    },\r\n    classEdit(scope) {\r\n      this.classModelVisible = true;\r\n      this.classTitle = \"编辑类目\";\r\n      this.handleResetForm();\r\n      this.loadModel(scope.row.id,false);\r\n    },\r\n    loadModel(id){\r\n      this.spinShow = true;\r\n      CustomClass.getBy({\"id\": id}).then(res => {\r\n        if (res['code'] === 0) {\r\n          this.form = res.data;\r\n          this.allData.forEach(item => {\r\n            if (item['id'] === this.form.parentId) {\r\n              this.form.parentName = item['className'];\r\n            }\r\n          })\r\n          if(!this.form.categoryName){\r\n            this.form.categoryName=null;\r\n          }\r\n        }\r\n      }).finally(() => {\r\n        this.spinShow = false;\r\n      })\r\n    },\r\n    cancelForm() {\r\n      this.classModelVisible = false;\r\n      this.handleResetForm();\r\n    },\r\n    addDeclare(){\r\n      this.declareData.push({decKey:'',content:''})\r\n    },\r\n    delDeclare(index){\r\n      this.declareData.splice(index,1);\r\n    },\r\n    handleResetForm() {\r\n      this.$refs[\"form\"].resetFields();\r\n      this.form['parentId'] = null;\r\n      this.form['id'] = null;\r\n      this.form['level'] = null;\r\n    },\r\n    handleSubmit() {\r\n      this.$refs['form'].validate((valid) => {\r\n        if (valid) {\r\n          this.saving = true;\r\n          CustomClass.saveCustomClass(this.form).then(res => {\r\n            if (res['code'] === 0) {\r\n              this.handleSearch();\r\n              this.cancelForm();\r\n              this.currentId = null;\r\n              this.$Message.success('保存成功');\r\n            }\r\n          }).finally(() => {\r\n            this.saving = false;\r\n          })\r\n        }\r\n      })\r\n    },\r\n    //删除报关类目\r\n    classRemove(scope) {\r\n      this.$Modal.confirm({\r\n        title: '提示！',\r\n        content: '您确定删除这条数据吗？',\r\n        onOk: () => {\r\n          this.saving = true;\r\n          CustomClass.remove({id: scope.row.id}).then(res => {\r\n            if (res['code'] === 0) {\r\n              this.currentId = null;\r\n              this.$Message.success('删除成功');\r\n              this.handleSearch();\r\n            }\r\n          }).finally(() => {\r\n            this.saving = false\r\n          })\r\n        }\r\n      });\r\n    },\r\n    //查看报关类目\r\n    classLook(scope){\r\n      const { categoryViewRef} = this.$refs;\r\n      if (categoryViewRef) {\r\n        categoryViewRef.setDefault(scope.row.id);\r\n      }\r\n      this.classViewVisible = true;\r\n    },\r\n    //新增/修改/删除申报要素\r\n    declareEdit(scope){\r\n      this.declareTitle = \"维护申报要素类目:\"+scope.row.className;\r\n      this.disabled = false;\r\n      if(this.declareColumn.length <=2){\r\n        this.declareColumn.push({title: '操作',key: 'action',minWidth: 120, align: 'center',slot:'decAction'});\r\n      }\r\n      this.currentId = scope.row.id;\r\n      this.declareModelVisible= true;\r\n      this.loading=true;\r\n      CustomClass.getDeclareElement({\"id\":this.currentId}).then(res=>{\r\n        this.declareData = res['data'];\r\n      }).finally(()=>{this.loading=false;})\r\n    },\r\n    cancelDeclare(){\r\n      this.declareModelVisible = false;\r\n      this.currentId = null;\r\n    },\r\n    saveDeclare(){\r\n      this.declareData.forEach(item=>item['parentId']=this.currentId);\r\n      this.saving=true;\r\n      (this.declareData.length>0?CustomClass.saveDeclareElement(this.declareData):CustomClass.delDeclareElement({\"id\":this.currentId}))\r\n        .then(res=>{\r\n          if (res['code'] === 0) {\r\n            this.handleSearch();\r\n            this.cancelDeclare();\r\n            this.$Message.success('处理成功');\r\n          }\r\n        }).finally(()=>{this.saving=false;})\r\n    },\r\n\r\n    //新增/修改/删除清关资料\r\n    clearanceEdit(scope){\r\n      this.clearanceTitle = \"维护清关信息:\"+scope.row.className;\r\n      this.disabled = false;\r\n      if(this.clearanceColumn.length <=4){\r\n        this.clearanceColumn.push({title: '操作',key: 'action',minWidth: 120, align: 'center',slot:'clearanceAction'});\r\n      }\r\n      this.loadClearanceElement(scope);\r\n    },\r\n    getClearanceElement(scope){\r\n      this.clearanceTitle = \"查看清关信息:\"+scope.row.className;\r\n      this.disabled = true;\r\n      if(this.clearanceColumn.length >=5){\r\n        this.clearanceColumn.splice(4,1);\r\n      }\r\n      this.loadClearanceElement(scope);\r\n    },\r\n    loadClearanceElement(scope){\r\n      this.currentId = scope.row.id;\r\n      this.clearanceModelVisible= true;\r\n      this.loading=true;\r\n      CustomClass.getClearanceElement({\"id\":this.currentId}).then(res=>{\r\n        this.clearanceData = res['data'];\r\n      }).finally(()=>{this.loading=false;})\r\n    },\r\n    cancelClearance(){\r\n      this.clearanceModelVisible = false;\r\n      this.currentId = null;\r\n    },\r\n    addClearance(){\r\n      this.clearanceData.push({})\r\n    },\r\n    delClearance(index){\r\n      this.clearanceData.splice(index,1);\r\n    },\r\n    saveClearance(){\r\n      this.clearanceData.forEach(item=>item['parentId']=this.currentId);\r\n      this.saving=true;\r\n      (this.clearanceData.length>0?CustomClass.saveClearanceElement(this.clearanceData):CustomClass.delClearanceElement({\"id\":this.currentId}))\r\n        .then(res=>{\r\n          if (res['code'] === 0) {\r\n            this.handleSearch();\r\n            this.cancelClearance();\r\n            this.$Message.success('保存成功');\r\n          }\r\n        }).finally(()=>{this.saving=false;})\r\n    },\r\n\r\n    //日志\r\n    lookLog(scope) {\r\n      const {logModelRef} = this.$refs;\r\n      if (logModelRef) {\r\n        logModelRef.setDefault(scope.row.id, this.refType);\r\n      }\r\n      this.logVisible = true;\r\n    },\r\n    getLogRefType() {\r\n      CustomClass.getLogRefType().then(res => {\r\n        if (res['code'] === 0) {\r\n          this.refType = res.data;\r\n        }\r\n      })\r\n    },\r\n    //加载数据\r\n    listAllSpu() {\r\n      listAllSpu({}).then((res) => {\r\n        if (res['code'] === 0) {\r\n          this.spuList = [{\"spu\":\"服饰\",\"spuName\":\"服饰\",children:res.data.filter(item=>item['spuName'].indexOf(\"#\")<0)}];\r\n          this.diGuiTree(this.spuList)\r\n        }\r\n      })\r\n    },\r\n    diGuiTree(item) {  //递归便利树结构\r\n      item.forEach(item => {\r\n        item.id = item['spu'];\r\n        item.label = item['spuName'];\r\n        !item['children'] || item['children'].length === 0 ? delete item.children : this.diGuiTree(item.children);\r\n      })\r\n    },\r\n    listDeclareTypeList(){\r\n      CommonApi.ListDictionaryValueBy(\"declare_element\").then(res=>{\r\n        if(res && res['code'] ===0){\r\n          this.declareTypeList = res['data']||[];\r\n          let index = 0;\r\n          this.declareTypeList.forEach(item=>{\r\n            this.column.splice(-2,0, {title: item['value'],key: 'dec'+index++,minWidth: 120, align: 'center',resizable:true,render:null});\r\n          })\r\n          this.handleSearch();\r\n        }\r\n      })\r\n    },\r\n    handleCurrency() {\r\n      Currency.getAll().then(res => {\r\n        this.currencyList = res.data;\r\n      });\r\n    },\r\n    //获取目的国家\r\n    getCountryList() {\r\n      CommonApi.ListDictionaryValueBy(\"base_country\").then(res=>{\r\n        if(res && res['code'] ===0){\r\n          let data = res['data']\r\n          if(data){\r\n            this.countryList = data.map(item=>JSON.parse(item.value));\r\n          }\r\n        }\r\n      })\r\n    },\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\">\r\n.treeDiv {\r\n  max-height: 450px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.superClass {\r\n  .ivu-poptip-body {\r\n    padding: 0 0 0 5px;\r\n  }\r\n}\r\n.modelBox{\r\n  .ivu-modal-body{\r\n    padding:0;\r\n  }\r\n}\r\n.customClass {\r\n  .specialBackground {\r\n    background: #E0FFFF;\r\n  }\r\n}\r\n</style>\r\n"]}]}