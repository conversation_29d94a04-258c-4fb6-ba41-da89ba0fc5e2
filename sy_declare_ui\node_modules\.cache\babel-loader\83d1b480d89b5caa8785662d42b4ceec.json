{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\basf\\listing\\components\\ListingSync.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\basf\\listing\\components\\ListingSync.vue", "mtime": 1752737748515}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\.babelrc", "mtime": 1752737748316}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["ShopSelect", "Listing", "name", "components", "props", "onCancel", "type", "Function", "visible", "Boolean", "loading", "data", "syncTitle", "syncLoading", "loadStatus", "syncForm", "shops", "methods", "onVisibleChange", "$refs", "resetFields", "onOk", "_this", "getStr", "value", "Array", "isArray", "join", "undefined", "content", "sellerSku", "trim", "replace", "sellerSkus", "split", "filter", "v", "syncListing", "then", "res", "$Message", "success", "error", "catch", "finally"], "sources": ["src/view/module/lx/listing/components/ListingSync.vue"], "sourcesContent": ["<!--\r\n@create date 2019-12-10\r\n@desc 设置同步选择弹框,由于有shopSelect控件，导致放入统一页面时弹框在模态框后面\r\n-->\r\n<template>\r\n  <Modal :title=\"syncTitle\" width=\"500px\" @on-cancel=\"onCancel\" @on-ok=\"onOk\" :value=\"visible\"  @on-visible-change=\"onVisibleChange\">\r\n    <Form ref=\"syncFormRef\" :model=\"syncForm\" :label-width=\"100\">\r\n      <FormItem prop=\"shops\" label=\"网店\">\r\n        <ShopSelect v-model=\"syncForm.shops\" placeholder=\"店铺\" width=\"205px\" :valueField=\"'sid'\" :appendToBody=\"false\"/>\r\n      </FormItem>\r\n      <FormItem label=\"销售Sku\" prop=\"sellerSku\">\r\n        <Input v-model=\"syncForm.sellerSku\" type=\"textarea\" placeholder=\"请输入内容,多个以逗号隔开,最多支持10个\"></Input>\r\n      </FormItem>\r\n    </Form>\r\n    <div slot=\"footer\"></div>\r\n    <template v-slot:footer=\"{}\">\r\n      <Button @click=\"onCancel\" :disabled=\"syncLoading\">取消</Button>\r\n      <Button type=\"primary\" @click=\"onOk\" :loading=\"syncLoading\" style=\"margin-left: 15px\">确认</Button>\r\n    </template>\r\n  </Modal>\r\n</template>\r\n<script>\r\nimport ShopSelect from \"@/components/shopSelect/index.vue\";\r\nimport Listing from '@/api/basf/listing'\r\nexport default {\r\n  name: \"ListingSync\",\r\n  components: {ShopSelect},\r\n  props: {\r\n    onCancel: {\r\n      type: Function\r\n    },\r\n    visible: {\r\n      //弹窗关闭控制\r\n      type: Boolean\r\n    },\r\n    loading: {\r\n      type: Boolean\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      syncTitle:\"同步Listing\",\r\n      syncLoading:false,\r\n      loadStatus:false,\r\n      syncForm:{shops:[]}\r\n    };\r\n  },\r\n\r\n  methods: {\r\n    onVisibleChange(visible){\r\n      if(!visible){\r\n        this.$refs[\"syncFormRef\"].resetFields();\r\n      }\r\n    },\r\n    onOk() {\r\n      //点击确定\r\n      this.syncLoading = true;\r\n      const getStr = value => value && Array.isArray(value) ? value.join(\",\") : undefined;\r\n      const content = this.syncForm.sellerSku ? this.syncForm.sellerSku.trim().replace(/，/g, \",\") : '';\r\n      let sellerSkus = content.split('\\n').filter(v=>!!v).join(\",\");\r\n      Listing.syncListing({\"shopId\":getStr(this.syncForm.shops),\"sellerSku\":sellerSkus}).then((res)=>{\r\n        if(res && res['code'] ===0){\r\n          this.$Message.success(\"提交同步任务成功,任务正在执行\");\r\n          this.onCancel();\r\n        }else{\r\n          this.$Message.error(res['message'])\r\n        }\r\n      }).catch(()=>{}).finally(()=>{this.syncLoading = false;});\r\n    }\r\n  }\r\n};\r\n</script>\r\n"], "mappings": ";;;;;;AAsBA,OAAAA,UAAA;AACA,OAAAC,OAAA;AACA;EACAC,IAAA;EACAC,UAAA;IAAAH,UAAA,EAAAA;EAAA;EACAI,KAAA;IACAC,QAAA;MACAC,IAAA,EAAAC;IACA;IACAC,OAAA;MACA;MACAF,IAAA,EAAAG;IACA;IACAC,OAAA;MACAJ,IAAA,EAAAG;IACA;EACA;EACAE,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA;MACAC,WAAA;MACAC,UAAA;MACAC,QAAA;QAAAC,KAAA;MAAA;IACA;EACA;EAEAC,OAAA;IACAC,eAAA,WAAAA,gBAAAV,OAAA;MACA,KAAAA,OAAA;QACA,KAAAW,KAAA,gBAAAC,WAAA;MACA;IACA;IACAC,IAAA,WAAAA,KAAA;MAAA,IAAAC,KAAA;MACA;MACA,KAAAT,WAAA;MACA,IAAAU,MAAA,YAAAA,OAAAC,KAAA;QAAA,OAAAA,KAAA,IAAAC,KAAA,CAAAC,OAAA,CAAAF,KAAA,IAAAA,KAAA,CAAAG,IAAA,QAAAC,SAAA;MAAA;MACA,IAAAC,OAAA,QAAAd,QAAA,CAAAe,SAAA,QAAAf,QAAA,CAAAe,SAAA,CAAAC,IAAA,GAAAC,OAAA;MACA,IAAAC,UAAA,GAAAJ,OAAA,CAAAK,KAAA,OAAAC,MAAA,WAAAC,CAAA;QAAA,SAAAA,CAAA;MAAA,GAAAT,IAAA;MACA1B,OAAA,CAAAoC,WAAA;QAAA,UAAAd,MAAA,MAAAR,QAAA,CAAAC,KAAA;QAAA,aAAAiB;MAAA,GAAAK,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,IAAAA,GAAA;UACAjB,KAAA,CAAAkB,QAAA,CAAAC,OAAA;UACAnB,KAAA,CAAAjB,QAAA;QACA;UACAiB,KAAA,CAAAkB,QAAA,CAAAE,KAAA,CAAAH,GAAA;QACA;MACA,GAAAI,KAAA,iBAAAC,OAAA;QAAAtB,KAAA,CAAAT,WAAA;MAAA;IACA;EACA;AACA"}]}