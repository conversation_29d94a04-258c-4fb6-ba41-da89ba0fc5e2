{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\basf\\listing\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\basf\\listing\\index.vue", "mtime": 1752737748515}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQppbXBvcnQgTGlzdGluZyBmcm9tICdAL2FwaS9iYXNmL2xpc3RpbmcnDQppbXBvcnQgRXhwb3J0RmlsZUpzIGZyb20gJ0AvYXBpL2NvbW1vbi9leHBvcnRGaWxlJw0KaW1wb3J0IHthdXRvVGFibGVIZWlnaHR9IGZyb20gJ0AvbGlicy90b29scy5qcycNCmltcG9ydCBTaG9wU2VsZWN0IGZyb20gIkAvY29tcG9uZW50cy9zaG9wU2VsZWN0L2luZGV4LnZ1ZSI7DQppbXBvcnQgTGlzdGluZ1N5bmMgZnJvbSAiLi9jb21wb25lbnRzL0xpc3RpbmdTeW5jLnZ1ZSI7DQppbXBvcnQgSW1wb3J0RmlsZSBmcm9tICIuLi8uLi9jb21tb24vaW1wb3J0RmlsZS52dWUiOw0KaW1wb3J0IEV4cG9ydEZpbGUgZnJvbSAiLi4vLi4vY29tbW9uL2V4cG9ydEZpbGUudnVlIjsNCmltcG9ydCBNdWx0aXBsZSBmcm9tICJAL3ZpZXcvbW9kdWxlL2NvbW1vbi9tdWx0aXBsZUlucHV0LnZ1ZSI7DQppbXBvcnQgcGVyc29uU2VsZWN0IGZyb20gIkAvY29tcG9uZW50cy9wZXJzb24tc2VsZWN0LXJhZGlvL2luZGV4LnZ1ZSI7DQppbXBvcnQgU2l0ZSBmcm9tICJAL2FwaS9iYXNmL3NpdGUiOw0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAnbGlzdGluZycsDQogIGNvbXBvbmVudHM6IHsNCiAgICBwZXJzb25TZWxlY3QsDQogICAgU2hvcFNlbGVjdCwNCiAgICBMaXN0aW5nU3luYywNCiAgICBJbXBvcnRGaWxlLA0KICAgIEV4cG9ydEZpbGUsDQogICAgTXVsdGlwbGUNCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgYXV0b1RhYmxlSGVpZ2h0LA0KICAgICAgc2l0ZUFycjpbXSwNCiAgICAgIHNlbGxlckFycjpbXSwNCiAgICAgIG11bHRpVmFsdWVzOiBbXSwNCiAgICAgIHBvcFZpc2libGU6ZmFsc2UsDQogICAgICBwZXJzb25WaXNpYmxlOmZhbHNlLA0KICAgICAgLy9TS1XjgIFBU0lO44CB5qCH562+DQogICAgICBwb3BDb250ZW50OiB1bmRlZmluZWQsLy9wb3BDb250ZW506L6T5YWl5YaF5a65DQogICAgICBsb2FkaW5nOiBmYWxzZSwNCiAgICAgIHNhdmluZzogZmFsc2UsDQogICAgICBzeW5jVGl0bGU6ICflkIzmraVMaXN0aW5nJywNCiAgICAgIGV4cG9ydFRpbWU6bmV3IERhdGUoKSwNCiAgICAgIGZvcm1WYWx1ZXM6ew0KICAgICAgICBzZWFyY2hLZXk6bnVsbCwNCiAgICAgICAgc2hvcHM6W10sDQogICAgICAgIHNpdGVzOltdLA0KICAgICAgICBmdWxmaWxsQ2hhbm5lbDpudWxsLA0KICAgICAgICBzZWxsZXJzOltdDQogICAgICB9LA0KICAgICAgcGFnZUluZm86IHsNCiAgICAgICAgdG90YWw6IDAsDQogICAgICAgIHBhZ2U6IDEsDQogICAgICAgIGxpbWl0OiAxMA0KICAgICAgfSwNCiAgICAgIGNvbHVtbnM6IFt7dHlwZTogJ3NlbGVjdGlvbicsd2lkdGg6IDYwfSwNCiAgICAgICAgeyAgdGl0bGU6ICflm77niYcnLCAga2V5OiAnc21hbGxJbWFnZVVybCcsICB3aWR0aDogMTAwLHNsb3Q6ICdpbWFnZVNlY3Rpb24nfSwNCiAgICAgICAgeyAgdGl0bGU6ICfnvZHlupcnLCAga2V5OiAnc2hvcE5hbWUnLCAgd2lkdGg6IDkwLHJlbmRlcjogKGgsIHtyb3d9KSA9PiAoPHNwYW4gdi1jb3B5dGV4dD17cm93LnNob3BOYW1lfT57cm93LnNob3BOYW1lfTwvc3Bhbj4pfSwNCiAgICAgICAgeyAgdGl0bGU6ICdNU0tVJywgIGtleTogJ3NlbGxlclNrdScsICB3aWR0aDogMjAwLHJlbmRlcjogKGgsIHtyb3d9KSA9PiAoPHNwYW4gdi1jb3B5dGV4dD17cm93LnNlbGxlclNrdX0+e3Jvdy5zZWxsZXJTa3V9PC9zcGFuPil9LA0KICAgICAgICB7ICB0aXRsZTogJ2FzaW4nLCAga2V5OiAnYXNpbicsICB3aWR0aDogMTIwLHJlbmRlcjogKGgsIHtyb3d9KSA9PiAoPHNwYW4gdi1jb3B5dGV4dD17cm93LmFzaW59Pntyb3cuYXNpbn08L3NwYW4+KX0sDQogICAgICAgIHsgIHRpdGxlOiAnZm5za3UvZ3RpbicsICBrZXk6ICdmbnNrdScsICBtaW5XaWR0aDogMTUwLHJlbmRlcjogKGgsIHtyb3d9KSA9PiAoPHNwYW4gdi1jb3B5dGV4dD17cm93WydmbnNrdSddfT57cm93WydmbnNrdSddfTwvc3Bhbj4pfSwNCiAgICAgICAgeyAgdGl0bGU6ICfniLZBc2luJywgIGtleTogJ3BhcmVudEFzaW4nLCAgd2lkdGg6IDEyMCxyZW5kZXI6IChoLCB7cm93fSkgPT4gKDxzcGFuIHYtY29weXRleHQ9e3Jvd1sncGFyZW50QXNpbiddfT57cm93WydwYXJlbnRBc2luJ119PC9zcGFuPil9LA0KICAgICAgICB7ICB0aXRsZTogJ3VwYycsICBrZXk6ICd1cGMnLCAgd2lkdGg6IDEyMCxyZW5kZXI6IChoLCB7cm93fSkgPT4gKDxzcGFuIHYtY29weXRleHQ9e3Jvd1sndXBjJ119Pntyb3dbJ3VwYyddfTwvc3Bhbj4pfSwNCiAgICAgICAgeyAgdGl0bGU6ICfphY3pgIHmlrnlvI8nLCAga2V5OiAnZnVsZmlsbG1lbnRUeXBlJywgIHdpZHRoOiA5MH0sDQogICAgICAgIHsgIHRpdGxlOiAn54q25oCBJywgIGtleTogJ3N0YXR1cycsIHdpZHRoOiA4MCxyZW5kZXI6IChfLCB7IHJvdyB9KT0+e2xldCBzdGF0dXMgPSByb3cuc3RhdHVzfDA7DQogICAgICAgICAgICBsZXQgaXNEZWxldGUgPSByb3dbJ2lzRGVsZXRlJ118MDsNCiAgICAgICAgICAgIGxldCB0ZXh0ID0gaXNEZWxldGUgPT09IDE/J+W3suWIoOmZpCc6KHN0YXR1cyA9PT0xPyflnKjllK4nOiflgZzllK4nKTsNCiAgICAgICAgICAgIHJldHVybiAoPHNwYW4+e3RleHR9PC9zcGFuPik7fX0sDQogICAgICAgIHsgIHRpdGxlOiAn5biB56eNJywgIGtleTogJ2N1cnJlbmN5Q29kZScsIHdpZHRoOiA4MH0sDQogICAgICAgIHsgIHRpdGxlOiAn5Lu35qC8JywgIGtleTogJ3ByaWNlJywgd2lkdGg6IDgwfSwNCiAgICAgICAgeyAgdGl0bGU6ICfkvJjmg6Dku7cnLCAga2V5OiAnbGlzdGluZ1ByaWNlJywgd2lkdGg6IDgwfSwNCiAgICAgICAgeyAgdGl0bGU6ICfmgLvku7cnLCAga2V5OiAnbGFuZGVkUHJpY2UnLCB3aWR0aDogODB9LA0KICAgICAgICB7ICB0aXRsZTogJ+agh+mimCcsICBrZXk6ICdpdGVtTmFtZScsICB3aWR0aDogMjAwLHNsb3Q6ICdpdGVtTmFtZSd9LA0KICAgICAgICB7ICB0aXRsZTogJ+S6p+WTgee8lueggScsICBrZXk6ICdsb2NhbFNrdScsICB3aWR0aDogMTIwLHJlbmRlcjogKGgsIHtyb3d9KSA9PiAoPHNwYW4gdi1jb3B5dGV4dD17cm93Wydsb2NhbFNrdSddfT57cm93Wydsb2NhbFNrdSddfTwvc3Bhbj4pfSwNCiAgICAgICAgeyAgdGl0bGU6ICfkuqflk4Hlk4HlkI0nLCAga2V5OiAnbG9jYWxOYW1lJywgIHdpZHRoOiAyMDAscmVuZGVyOiAoaCwge3Jvd30pID0+ICg8c3BhbiB2LWNvcHl0ZXh0PXtyb3dbJ2xvY2FsTmFtZSddfT57cm93Wydsb2NhbE5hbWUnXX08L3NwYW4+KX0sDQogICAgICAgIHsgIHRpdGxlOiAn5o+P6L+wJywgIGtleTogJ2Rlc2NyaXB0aW9uJywgIG1pbldpZHRoOiAyMDAsc2xvdDogJ2Rlc2NyaXB0aW9uJ30sDQogICAgICAgIHsgIHRpdGxlOiAn5aSH5rOoJywgIGtleTogJ3JlbWFyaycsICB3aWR0aDogMTAwfSwNCiAgICAgICAgeyAgdGl0bGU6ICdGQk3lupPlrZgnLCAga2V5OiAncXVhbnRpdHknLCAgd2lkdGg6IDgwfSwNCiAgICAgICAgeyAgdGl0bGU6ICdGQkHlj6/llK4nLCAga2V5OiAnYWZuRnVsZmlsbGFibGVRdWFudGl0eScsICB3aWR0aDogODB9LA0KICAgICAgICB7ICB0aXRsZTogJ0ZCQeS4jeWPr+WUricsICBrZXk6ICdhZm5VbnNlbGxhYmxlUXVhbnRpdHknLCAgd2lkdGg6IDgwfSwNCiAgICAgICAgeyAgdGl0bGU6ICflvoXosIPku5MnLCAga2V5OiAncmVzZXJ2ZWRGY1RyYW5zZmVycycsICB3aWR0aDogODB9LA0KICAgICAgICB7ICB0aXRsZTogJ+iwg+S7k+S4rScsICBrZXk6ICdyZXNlcnZlZEZjUHJvY2Vzc2luZycsICB3aWR0aDogODB9LA0KICAgICAgICB7ICB0aXRsZTogJ+W+heWPkei0pycsICBrZXk6ICdyZXNlcnZlZEN1c3RvbWVyT3JkZXJzJywgIHdpZHRoOiA4MH0sDQogICAgICAgIHsgIHRpdGxlOiAn5Zyo6YCUJywgIGtleTogJ2FmbkluYm91bmRTaGlwcGVkUXVhbnRpdHknLCAgd2lkdGg6IDgwfSwNCiAgICAgICAgeyAgdGl0bGU6ICforqHliJLlhaXlupMnLCAga2V5OiAnYWZuSW5ib3VuZFdvcmtpbmdRdWFudGl0eScsICB3aWR0aDogODB9LA0KICAgICAgICB7ICB0aXRsZTogJ+WFpeW6k+S4rScsICBrZXk6ICdhZm5JbmJvdW5kUmVjZWl2aW5nUXVhbnRpdHknLCAgd2lkdGg6IDgwfSwNCiAgICAgICAgeyAgdGl0bGU6ICfotJ/otKPkuronLCAga2V5OiAncHJpbmNpcGFsVXNlcicsICB3aWR0aDogMjAwLHJlbmRlcjogKGgsIHtyb3d9KSA9PiAoPHNwYW4gdi1jb3B5dGV4dD17cm93WydwcmluY2lwYWxVc2VyTmFtZSddfT57cm93WydwcmluY2lwYWxVc2VyTmFtZSddfTwvc3Bhbj4pfQ0KICAgICAgXSwNCiAgICAgIGRhdGE6IFtdLA0KICAgICAgbW9kYWxTeW5jVmlzaWJsZTpmYWxzZSwNCiAgICAgIG1vZGFsSW1wb3J0VmlzaWJsZTpmYWxzZSwNCiAgICAgIGltcG9ydFRpdGxlOiLlr7zlhaXllYblk4HlhbPogZQiLA0KICAgICAgaW1wb3J0VGFza1R5cGU6Ik1TS1VfQU5TX1NLVV9SRUxBWF9JTVBPUlQiLA0KICAgICAgaW1wb3J0VXJsOiIvYmFzZS9saXN0aW5nL2ltcG9ydFJlbGF4RmlsZSIsDQogICAgICB0ZW1wbGF0ZVVybDoiL2Jhc2UvbGlzdGluZy9kb3dubG9hZFJlbGF4VGVtcGxhdGUiLA0KICAgICAgdGVtcGxhdGVOYW1lOiJMaXN0aW5n5YWz6IGU5pys5Zyw5ZWG5ZOB5qih5p2/IiwNCiAgICAgIGV4ZWN1dGVVcmw6Ii9iYXNlL2xpc3RpbmcvZXhlY3V0ZSIsDQogICAgICBtb2RhbEV4cG9ydFZpc2libGU6ZmFsc2UsDQogICAgICBleHBvcnRUaXRsZToi5a+85Ye6TGlzdGluZyIsDQogICAgICBleHBvcnRUYXNrVHlwZToiTVNLVV9SRUNPUkRfRVhQT1JUIiwNCiAgICAgIGludGVydmFsOiBudWxsLA0KICAgICAgZXhwb3J0RmlsZU5hbWU6Ikxpc3RpbmfliJfooagiDQogICAgfQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgbG9hZERhdGEocGFnZSl7DQoNCiAgICB9LA0KICAgIG9wZW5QZXJzb24oKXsNCiAgICAgIHRoaXMucmVzZXRNdWx0aXBsZSgpOw0KICAgICAgLy/miZPlvIDkurrlkZjpgInmi6kNCiAgICAgIGNvbnN0IHsgcGVyc29uU2VsZWN0UmVmIH0gPSB0aGlzLiRyZWZzOw0KICAgICAgY29uc3QgeyBzZWxsZXJBcnIsIHBhZ2VJbmZvIH0gPSB0aGlzOw0KICAgICAgY29uc3Qgc2VsZWN0ZWRJZHMgPSBwYWdlSW5mby5zZWxsZXJJZCB8fCBbXTsNCiAgICAgIGlmIChwZXJzb25TZWxlY3RSZWYpIHBlcnNvblNlbGVjdFJlZi5zZXREZWZhdWx0KA0KICAgICAgICAgIHNlbGxlckFyci5maWx0ZXIodiA9PiBzZWxlY3RlZElkcy5pbmNsdWRlcyh2LmlkKSkubWFwKHY9Pih7IG5hbWU6IHYubmlja05hbWUsIGlkOiB2LmlkIH0pKQ0KICAgICAgKTsvL+e7mee7hOS7tuiuvue9rum7mOiupOmAieS4rQ0KICAgICAgdGhpcy5wZXJzb25WaXNpYmxlID0gdHJ1ZTsNCiAgICB9LA0KICAgIHJlc2V0TXVsdGlwbGUoKSB7IC8v6YeN572ucG9wDQogICAgICB0aGlzLnBvcENvbnRlbnQgPSB1bmRlZmluZWQ7DQogICAgICB0aGlzLnBvcFZpc2libGUgPSBmYWxzZTsNCiAgICB9LA0KICAgIGNsb3NlRHJvcGRvd24oKSB7IC8v5YWz6Zet6L6T5YWl5paH5pys5qGGDQogICAgICBjb25zdCB7IHBvcENvbnRlbnQgfSA9IHRoaXM7DQogICAgICBjb25zdCB7IG11bHRpcGxlUmVmIH0gPSB0aGlzLiRyZWZzOw0KICAgICAgdGhpcy5wb3BWaXNpYmxlID0gZmFsc2U7DQogICAgICBpZighcG9wQ29udGVudCkgcmV0dXJuOw0KICAgICAgY29uc3QgY29udGVudCA9IHBvcENvbnRlbnQgPyBwb3BDb250ZW50LnRyaW0oKS5yZXBsYWNlKC/vvIwvZywgIiwiKSA6ICcnOw0KICAgICAgdGhpcy5tdWx0aVZhbHVlcyA9IGNvbnRlbnQuc3BsaXQoJ1xuJykuZmlsdGVyKHY9PiEhdik7DQogICAgICB0aGlzLm11bHRpVmFsdWVzID0gWy4uLm5ldyBTZXQodGhpcy5tdWx0aVZhbHVlcyldOw0KICAgICAgaWYobXVsdGlwbGVSZWYgJiYgbXVsdGlwbGVSZWYuc2V0VmFsdWVBcnJheSl7DQogICAgICAgIG11bHRpcGxlUmVmLnNldFZhbHVlQXJyYXkodGhpcy5tdWx0aVZhbHVlcyk7DQogICAgICB9DQogICAgICB0aGlzLnBvcENvbnRlbnQgPSB1bmRlZmluZWQ7DQogICAgfSwNCiAgICBnZXRQYXJhbXMobmVlZFBhZ2UgPSBmYWxzZSkgew0KICAgICAgY29uc3QgeyBmb3JtVmFsdWVzLCBwYWdlSW5mbywgbXVsdGlWYWx1ZXMgfSA9IHRoaXM7DQogICAgICBjb25zdCBnZXRTdHIgPSB2YWx1ZSA9PiB2YWx1ZSAmJiBBcnJheS5pc0FycmF5KHZhbHVlKSA/IHZhbHVlLmpvaW4oIiwiKSA6IHVuZGVmaW5lZDsNCiAgICAgIGNvbnN0IHBhZ2VQYXJhbSA9IG5lZWRQYWdlID8gcGFnZUluZm8gOiB7fTsNCiAgICAgIGNvbnN0IHBhcmFtcyA9IHsNCiAgICAgICAgLi4ucGFnZVBhcmFtLA0KICAgICAgICBmdWxmaWxsQ2hhbm5lbDpmb3JtVmFsdWVzLmZ1bGZpbGxDaGFubmVsLA0KICAgICAgICBzZWFyY2hLZXk6Zm9ybVZhbHVlcy5zZWFyY2hLZXksDQogICAgICAgIHNob3BJZDogZ2V0U3RyKGZvcm1WYWx1ZXMuc2hvcHMpLA0KICAgICAgICBzaXRlSWQ6IGdldFN0cihmb3JtVmFsdWVzLnNpdGVzKSwNCiAgICAgICAgc2VsbGVySWQ6IGdldFN0cihmb3JtVmFsdWVzLnNlbGxlcnMpDQogICAgICB9Ow0KICAgICAgaWYgKGZvcm1WYWx1ZXMuc2VhcmNoS2V5ICYmIG11bHRpVmFsdWVzLmxlbmd0aCA+IDApew0KICAgICAgICBwYXJhbXNbInNlYXJjaEtleSJdID0gZm9ybVZhbHVlcy5zZWFyY2hLZXk7DQogICAgICAgIGNvbnN0IGdldFN0ciA9IHZhbHVlID0+IHZhbHVlICYmIEFycmF5LmlzQXJyYXkodmFsdWUpID8gdmFsdWUuam9pbigiJiMmIikgOiB1bmRlZmluZWQ7DQogICAgICAgIHBhcmFtc1sic2VhcmNoVmFsdWUiXSA9IGdldFN0cihtdWx0aVZhbHVlcyk7DQogICAgICB9DQogICAgICByZXR1cm4gcGFyYW1zOw0KICAgIH0sDQogICAgaGFuZGxlU2VhcmNoKHBhZ2UpIHsNCiAgICAgIGlmIChwYWdlKSB7DQogICAgICAgIHRoaXMucGFnZUluZm8ucGFnZSA9IHBhZ2UNCiAgICAgIH0NCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWUNCiAgICAgIExpc3RpbmcubGlzdFBhZ2UodGhpcy5nZXRQYXJhbXModHJ1ZSkpLnRoZW4oKHJlcyk9PnsNCiAgICAgICAgaWYgKHJlcyAmJiByZXNbJ2NvZGUnXSA9PT0gMCkgew0KICAgICAgICAgIGNvbnN0IGRhdGEgPSByZXMuZGF0YSB8fCB7fTsNCiAgICAgICAgICB0aGlzLmRhdGEgPSBkYXRhLnJlY29yZHMgfHwgW107DQogICAgICAgICAgY29uc3QgZ2V0VmFsdWUgPSAodmFsdWUsIGRlZmF1bHRWYWx1ZSkgPT4NCiAgICAgICAgICAgIHZhbHVlIHx8IHZhbHVlID09PSAwID8gK3ZhbHVlIDogZGVmYXVsdFZhbHVlOw0KICAgICAgICAgIHRoaXMucGFnZUluZm8gPSB7DQogICAgICAgICAgICB0b3RhbDogZ2V0VmFsdWUoZGF0YS50b3RhbCwgMCksDQogICAgICAgICAgICBwYWdlOiBnZXRWYWx1ZShkYXRhLnBhZ2UsIDEpLA0KICAgICAgICAgICAgbGltaXQ6IGdldFZhbHVlKGRhdGEubGltaXQsIDEwKQ0KICAgICAgICAgIH07DQogICAgICAgIH0gZWxzZSB0aGlzLmRhdGEgPSBbXTsNCiAgICAgIH0pLmNhdGNoKCgpID0+IHsNCiAgICAgICAgdGhpcy5kYXRhID0gW107DQogICAgICB9KS5maW5hbGx5KCgpID0+IHsNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICB9KTsNCiAgICB9LA0KICAgIGhhbmRsZVBhZ2UoY3VycmVudCkgew0KICAgICAgdGhpcy5wYWdlSW5mby5wYWdlID0gY3VycmVudA0KICAgICAgdGhpcy5oYW5kbGVTZWFyY2goKQ0KICAgIH0sDQogICAgaGFuZGxlUGFnZVNpemUoc2l6ZSkgew0KICAgICAgdGhpcy5wYWdlSW5mby5saW1pdCA9IHNpemUNCiAgICAgIHRoaXMuaGFuZGxlU2VhcmNoKCkNCiAgICB9LA0KICAgIGhhbmRsZVJlc2V0Rm9ybShmb3JtKSB7DQogICAgICB0aGlzLiRyZWZzW2Zvcm1dLnJlc2V0RmllbGRzKCk7DQogICAgICB0aGlzLmZvcm1WYWx1ZXMuc2hvcHM9W107DQogICAgfSwNCiAgICAvLyDkurrlkZjpgInmi6nnm7jlhbMNCiAgICBzZXRTZWxlY3RJbmZvKGluZm89e30pew0KICAgICAgdGhpcy5zZWxsZXJBcnIgPSBpbmZvLnBlcnNvbkFyciB8fCBbXTsNCiAgICB9LA0KICAgIGxvYWRTaXRlKCl7DQogICAgICBTaXRlLmdldEFsbCgpLnRoZW4oKHJlcyk9PnsNCiAgICAgICAgaWYocmVzWydjb2RlJ10gPT09MCl7DQogICAgICAgICAgcmVzWydkYXRhJ10uZm9yRWFjaChpdGVtPT57DQogICAgICAgICAgICB0aGlzLnNpdGVBcnIucHVzaCh7ImlkIjppdGVtLmlkLCJuYW1lIjppdGVtLm5hbWV9KTsNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgaGFuZGxlTW9kYWwodHlwZSl7DQogICAgICBpZih0eXBlID09PSAnc3luYycpew0KICAgICAgICB0aGlzLm1vZGFsU3luY1Zpc2libGUgPSB0cnVlOw0KICAgICAgfWVsc2UgaWYodHlwZSA9PT0gJ2ltcG9ydFJlbGF4Jyl7DQogICAgICAgIHRoaXMubW9kYWxJbXBvcnRWaXNpYmxlID0gdHJ1ZTsNCiAgICAgIH1lbHNlIGlmKHR5cGUgPT09ICdleHBvcnRMb2cnKXsNCiAgICAgICAgdGhpcy5tb2RhbEV4cG9ydFZpc2libGUgPSB0cnVlOw0KICAgICAgfQ0KICAgIH0sDQogICAgaGFuZGxlUmVzZXQodHlwZSl7DQogICAgICBpZih0eXBlID09PSAnc3luYycpew0KICAgICAgICB0aGlzLm1vZGFsU3luY1Zpc2libGUgPSBmYWxzZTsNCiAgICAgIH0NCiAgICB9LA0KICAgIGV4ZWN1dGVFeHBvcnQgKCkgew0KICAgICAgaWYobmV3IERhdGUoKSAtIHRoaXMuZXhwb3J0VGltZSA8PSA1MDAwKXsNCiAgICAgICAgdGhpcy4kTWVzc2FnZS5zdWNjZXNzKCflr7zlh7rpl7TpmpQ1U++8gScpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQogICAgICB0aGlzLmV4cG9ydFRpbWUgPSBuZXcgRGF0ZSgpOw0KICAgICAgTGlzdGluZy5leHBvcnRGaWxlKHRoaXMuZ2V0UGFyYW1zKGZhbHNlKSkudGhlbihyZXMgPT4gew0KICAgICAgICBpZiAocmVzWydjb2RlJ10gPT09IDApIHsNCiAgICAgICAgICB0aGlzLiRNZXNzYWdlLnN1Y2Nlc3MoJ+WvvOWHuua2iOaBr+a3u+WKoOaIkOWKn++8gScpOw0KICAgICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgICAgIEV4cG9ydEZpbGVKcy5pbnRlcnZhbEZ1bmMoeyJpZCI6cmVzLmRhdGEsImZpbGVOYW1lIjp0aGlzLmV4cG9ydEZpbGVOYW1lfSk7DQogICAgICAgIH1lbHNlew0KICAgICAgICAgIHRoaXMuJE1lc3NhZ2UuZXJyb3IocmVzWydtZXNzYWdlJ10pOw0KICAgICAgICB9DQogICAgICB9KS5jYXRjaCgoKSA9PiB7DQoNCiAgICAgIH0pLmZpbmFsbHkoKCkgPT4gew0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIH0pOw0KICAgIH0sDQogIH0sDQoNCiAgbW91bnRlZDogZnVuY3Rpb24gKCkgew0KICAgIHRoaXMubG9hZFNpdGUoKTsNCiAgICB0aGlzLmhhbmRsZVNlYXJjaCgxKTsNCiAgfQ0KfQ0KDQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AA0GA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/view/module/basf/listing", "sourcesContent": ["<template>\r\n  <div class=\"saleListingPage\">\r\n    <Card :shadow=\"true\">\r\n      <div class=\"search-con search-con-top\">\r\n        <Form ref=\"searchForm\" class=\"searchForm\" :model=\"formValues\" :inline=\"true\" @submit.native.prevent>\r\n          <FormItem prop=\"shop\">\r\n            <ShopSelect v-model=\"formValues.shops\" placeholder=\"店铺\" width=\"205px\" :valueField=\"'id'\"/>\r\n          </FormItem>\r\n          <FormItem prop=\"site\">\r\n            <i-select :multiple=\"true\" :filterable=\"true\" v-model=\"formValues.sites\"  placeholder=\"站点\" :max-tag-count=\"1\" style=\"width: 160px\" :transfer=\"true\">\r\n              <i-option v-for=\"v in siteArr\" :value=\"v.id\" v-bind:key=\"v.id\">{{ v.name }}</i-option>\r\n            </i-select>\r\n          </FormItem>\r\n          <FormItem class=\"saleListingInputItemX\" prop=\"searchKey\">\r\n            <div class=\"flex-h\">\r\n              <Select v-model=\"formValues.searchKey\" style=\"width:100px\" :transfer=\"true\">\r\n                <Option value=\"SELLER_SKU\">MSKU</Option>\r\n                <Option value=\"ASIN\">ASIN</Option>\r\n                <Option value=\"FNSKU\">fnsku</Option>\r\n                <Option value=\"PARENT_ASIN\">父ASIN</Option>\r\n                <Option value=\"LOCAL_SKU\">料品SKU</Option>\r\n                <Option value=\"LOCAL_NAME\">料品名称</Option>\r\n              </Select>\r\n              <Multiple placeholder=\"请输入(回车分隔)\" @changeValue=\"(values)=>{ multiValues = values || []; }\" ref=\"multipleRef\"></Multiple>\r\n              <Button :visible=\"false\" @click=\"()=>{popVisible=true;}\">输入</Button>\r\n              <Dropdown trigger=\"custom\" :visible=\"popVisible\" style=\"margin-left: 3px\" :transfer=\"true\" transfer-class-name=\"orderBillDrop\">\r\n                <template #list>\r\n                  <DropdownMenu class=\"popContentClass\">\r\n                    <Input v-model=\"popContent\" type=\"textarea\" :autosize=\"{minRows: 4,maxRows: 8}\" placeholder=\"请输入内容，回车或逗号分隔\" style=\"width: 260px\"/>\r\n                    <div style=\"text-align: right; padding-top: 3px\">\r\n                      <Button type=\"info\" size=\"small\" @click=\"closeDropdown\">确定</Button>\r\n                    </div>\r\n                  </DropdownMenu>\r\n                </template>\r\n              </Dropdown>\r\n            </div>\r\n          </FormItem>\r\n          <FormItem prop=\"fulfillChannel\">\r\n            <Select v-model=\"formValues.fulfillChannel\" placeholder=\"配送方式\" style=\"width: 100px\" :clearable=\"true\" :transfer=\"true\">\r\n              <Option value=\"FBA\">FBA</Option>\r\n              <Option value=\"FBM\">FBM</Option>\r\n            </Select>\r\n          </FormItem>\r\n          <FormItem prop=\"sellers\" class=\"sellerSelectItem\">\r\n            <Select multiple type=\"text\" v-model=\"formValues.sellers\" placeholder=\"销售员\" filterable :max-tag-count=\"1\" style=\"width: 233px\" :transfer=\"true\" >\r\n              <Option v-for=\"item in sellerArr\" :value=\"item.id\" :key=\"item.id\">{{ item.nickName }}</Option>\r\n            </Select>\r\n            <Button type=\"dashed\" @click=\"openPerson\" style=\"margin-left: 3px\" size=\"default\">选择</Button>\r\n            <person-select :visible=\"personVisible\" :onCancel=\"()=>personVisible=false\"\r\n                           @setPerson=\"arr => (formValues.sellers = arr.map(v => v.id))\" @setSelectInfo=\"setSelectInfo\"\r\n                           ref=\"personSelectRef\" groupName=\"operations_persons\" :multiple=\"true\" :isQuery=\"true\" />\r\n          </FormItem>\r\n          <FormItem>\r\n            <Button type=\"primary\" @click=\"handleSearch(1)\">查询</Button>&nbsp;\r\n            <Button @click=\"handleResetForm('searchForm')\">重置</Button>\r\n          </FormItem>\r\n        </Form>\r\n      </div>\r\n      <div class=\"search-con search-con-top\">\r\n        <ButtonGroup>\r\n          <Button type=\"primary\" class=\"search-btn\" @click=\"handleModal('sync')\"><span>同步listing</span></Button>\r\n          <Button style=\"margin-left:15px;\" class=\"search-btn\" @click=\"handleModal('importRelax')\"><span>导入关联</span></Button>\r\n          <Button style=\"margin-left:15px;\" @click=\"executeExport();\">导出Listing</Button>\r\n          <Button style=\"margin-left:15px;\" @click=\"handleModal('exportLog')\" >查看导出记录</Button>\r\n        </ButtonGroup>\r\n      </div>\r\n      <Table :border=\"true\" ref=\"autoTableRef\" :max-height=\"autoTableHeight($refs.autoTableRef)\" :columns=\"columns\"\r\n             :data=\"data\" :loading=\"loading\">\r\n        <template v-slot:imageSection=\"{row}\">\r\n          <div class=\"productImgDiv\">\r\n            <span><Img :src=\"row.smallImageUrl\"/></span>\r\n          </div>\r\n        </template>\r\n        <template v-slot:itemName=\"{ row }\">\r\n          <Tooltip :transfer=\"true\" placement=\"right-end\" :max-width=\"500\">\r\n            <div slot=\"content\" style=\"word-break: break-all; white-space: pre-wrap\">\r\n              {{ row.itemName}}\r\n            </div>\r\n            <div class=\"overflowText\" style=\"max-width: 300px\" v-copytext=\"row.itemName\">\r\n              {{row.itemName.length>40?(row.itemName.substring(0,37)+\"...\"):row.itemName }}\r\n            </div>\r\n          </Tooltip>\r\n        </template>\r\n        <template v-slot:description=\"{ row }\">\r\n          <Tooltip :transfer=\"true\" placement=\"right-end\" :max-width=\"500\">\r\n            <div slot=\"content\" style=\"word-break: break-all; white-space: pre-wrap\">\r\n              {{ row.description}}\r\n            </div>\r\n            <div class=\"overflowText\" style=\"max-width: 300px\" v-copytext=\"row.description\">\r\n              {{row.description.length>40?(row.description.substring(0,50)+\"...\"):row.description }}\r\n            </div>\r\n          </Tooltip>\r\n        </template>\r\n      </Table>\r\n      <Page :total=\"pageInfo.total\" size=\"small\" :current=\"pageInfo.page\" :page-size=\"pageInfo.limit\" :show-elevator=\"true\"\r\n            :show-sizer=\"true\" :show-total=\"true\" @on-change=\"handlePage\" @on-page-size-change='handlePageSize'></Page>\r\n    </Card>\r\n    <ImportFile :onCancel=\"()=>{this.modalImportVisible=false;}\" :visible=\"modalImportVisible\" ref=\"ImportModalRef\" :title=\"importTitle\" :taskType=\"importTaskType\"\r\n                :downTemplateUrl=\"templateUrl\" :templateName=\"templateName\" :url=\"importUrl\" :shadow=\"true\" :executeUrl=\"executeUrl\"/>\r\n    <ListingSync :onCancel=\"()=>{this.modalSyncVisible=false;}\" :visible=\"modalSyncVisible\" ref=\"syncModalRef\" :shadow=\"true\"/>\r\n    <ExportFile :onCancel=\"()=>{this.modalExportVisible=false;}\" :visible=\"modalExportVisible\" ref=\"ExportModalRef\" :title=\"exportTitle\" :taskType=\"exportTaskType\"\r\n                :shadow=\"true\" :executeUrl=\"executeUrl\" :fileName=\"exportFileName\"/>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Listing from '@/api/basf/listing'\r\nimport ExportFileJs from '@/api/common/exportFile'\r\nimport {autoTableHeight} from '@/libs/tools.js'\r\nimport ShopSelect from \"@/components/shopSelect/index.vue\";\r\nimport ListingSync from \"./components/ListingSync.vue\";\r\nimport ImportFile from \"../../common/importFile.vue\";\r\nimport ExportFile from \"../../common/exportFile.vue\";\r\nimport Multiple from \"@/view/module/common/multipleInput.vue\";\r\nimport personSelect from \"@/components/person-select-radio/index.vue\";\r\nimport Site from \"@/api/basf/site\";\r\nexport default {\r\n  name: 'listing',\r\n  components: {\r\n    personSelect,\r\n    ShopSelect,\r\n    ListingSync,\r\n    ImportFile,\r\n    ExportFile,\r\n    Multiple\r\n  },\r\n  data() {\r\n    return {\r\n      autoTableHeight,\r\n      siteArr:[],\r\n      sellerArr:[],\r\n      multiValues: [],\r\n      popVisible:false,\r\n      personVisible:false,\r\n      //SKU、ASIN、标签\r\n      popContent: undefined,//popContent输入内容\r\n      loading: false,\r\n      saving: false,\r\n      syncTitle: '同步Listing',\r\n      exportTime:new Date(),\r\n      formValues:{\r\n        searchKey:null,\r\n        shops:[],\r\n        sites:[],\r\n        fulfillChannel:null,\r\n        sellers:[]\r\n      },\r\n      pageInfo: {\r\n        total: 0,\r\n        page: 1,\r\n        limit: 10\r\n      },\r\n      columns: [{type: 'selection',width: 60},\r\n        {  title: '图片',  key: 'smallImageUrl',  width: 100,slot: 'imageSection'},\r\n        {  title: '网店',  key: 'shopName',  width: 90,render: (h, {row}) => (<span v-copytext={row.shopName}>{row.shopName}</span>)},\r\n        {  title: 'MSKU',  key: 'sellerSku',  width: 200,render: (h, {row}) => (<span v-copytext={row.sellerSku}>{row.sellerSku}</span>)},\r\n        {  title: 'asin',  key: 'asin',  width: 120,render: (h, {row}) => (<span v-copytext={row.asin}>{row.asin}</span>)},\r\n        {  title: 'fnsku/gtin',  key: 'fnsku',  minWidth: 150,render: (h, {row}) => (<span v-copytext={row['fnsku']}>{row['fnsku']}</span>)},\r\n        {  title: '父Asin',  key: 'parentAsin',  width: 120,render: (h, {row}) => (<span v-copytext={row['parentAsin']}>{row['parentAsin']}</span>)},\r\n        {  title: 'upc',  key: 'upc',  width: 120,render: (h, {row}) => (<span v-copytext={row['upc']}>{row['upc']}</span>)},\r\n        {  title: '配送方式',  key: 'fulfillmentType',  width: 90},\r\n        {  title: '状态',  key: 'status', width: 80,render: (_, { row })=>{let status = row.status|0;\r\n            let isDelete = row['isDelete']|0;\r\n            let text = isDelete === 1?'已删除':(status ===1?'在售':'停售');\r\n            return (<span>{text}</span>);}},\r\n        {  title: '币种',  key: 'currencyCode', width: 80},\r\n        {  title: '价格',  key: 'price', width: 80},\r\n        {  title: '优惠价',  key: 'listingPrice', width: 80},\r\n        {  title: '总价',  key: 'landedPrice', width: 80},\r\n        {  title: '标题',  key: 'itemName',  width: 200,slot: 'itemName'},\r\n        {  title: '产品编码',  key: 'localSku',  width: 120,render: (h, {row}) => (<span v-copytext={row['localSku']}>{row['localSku']}</span>)},\r\n        {  title: '产品品名',  key: 'localName',  width: 200,render: (h, {row}) => (<span v-copytext={row['localName']}>{row['localName']}</span>)},\r\n        {  title: '描述',  key: 'description',  minWidth: 200,slot: 'description'},\r\n        {  title: '备注',  key: 'remark',  width: 100},\r\n        {  title: 'FBM库存',  key: 'quantity',  width: 80},\r\n        {  title: 'FBA可售',  key: 'afnFulfillableQuantity',  width: 80},\r\n        {  title: 'FBA不可售',  key: 'afnUnsellableQuantity',  width: 80},\r\n        {  title: '待调仓',  key: 'reservedFcTransfers',  width: 80},\r\n        {  title: '调仓中',  key: 'reservedFcProcessing',  width: 80},\r\n        {  title: '待发货',  key: 'reservedCustomerOrders',  width: 80},\r\n        {  title: '在途',  key: 'afnInboundShippedQuantity',  width: 80},\r\n        {  title: '计划入库',  key: 'afnInboundWorkingQuantity',  width: 80},\r\n        {  title: '入库中',  key: 'afnInboundReceivingQuantity',  width: 80},\r\n        {  title: '负责人',  key: 'principalUser',  width: 200,render: (h, {row}) => (<span v-copytext={row['principalUserName']}>{row['principalUserName']}</span>)}\r\n      ],\r\n      data: [],\r\n      modalSyncVisible:false,\r\n      modalImportVisible:false,\r\n      importTitle:\"导入商品关联\",\r\n      importTaskType:\"MSKU_ANS_SKU_RELAX_IMPORT\",\r\n      importUrl:\"/base/listing/importRelaxFile\",\r\n      templateUrl:\"/base/listing/downloadRelaxTemplate\",\r\n      templateName:\"Listing关联本地商品模板\",\r\n      executeUrl:\"/base/listing/execute\",\r\n      modalExportVisible:false,\r\n      exportTitle:\"导出Listing\",\r\n      exportTaskType:\"MSKU_RECORD_EXPORT\",\r\n      interval: null,\r\n      exportFileName:\"Listing列表\"\r\n    }\r\n  },\r\n  methods: {\r\n    loadData(page){\r\n\r\n    },\r\n    openPerson(){\r\n      this.resetMultiple();\r\n      //打开人员选择\r\n      const { personSelectRef } = this.$refs;\r\n      const { sellerArr, pageInfo } = this;\r\n      const selectedIds = pageInfo.sellerId || [];\r\n      if (personSelectRef) personSelectRef.setDefault(\r\n          sellerArr.filter(v => selectedIds.includes(v.id)).map(v=>({ name: v.nickName, id: v.id }))\r\n      );//给组件设置默认选中\r\n      this.personVisible = true;\r\n    },\r\n    resetMultiple() { //重置pop\r\n      this.popContent = undefined;\r\n      this.popVisible = false;\r\n    },\r\n    closeDropdown() { //关闭输入文本框\r\n      const { popContent } = this;\r\n      const { multipleRef } = this.$refs;\r\n      this.popVisible = false;\r\n      if(!popContent) return;\r\n      const content = popContent ? popContent.trim().replace(/，/g, \",\") : '';\r\n      this.multiValues = content.split('\\n').filter(v=>!!v);\r\n      this.multiValues = [...new Set(this.multiValues)];\r\n      if(multipleRef && multipleRef.setValueArray){\r\n        multipleRef.setValueArray(this.multiValues);\r\n      }\r\n      this.popContent = undefined;\r\n    },\r\n    getParams(needPage = false) {\r\n      const { formValues, pageInfo, multiValues } = this;\r\n      const getStr = value => value && Array.isArray(value) ? value.join(\",\") : undefined;\r\n      const pageParam = needPage ? pageInfo : {};\r\n      const params = {\r\n        ...pageParam,\r\n        fulfillChannel:formValues.fulfillChannel,\r\n        searchKey:formValues.searchKey,\r\n        shopId: getStr(formValues.shops),\r\n        siteId: getStr(formValues.sites),\r\n        sellerId: getStr(formValues.sellers)\r\n      };\r\n      if (formValues.searchKey && multiValues.length > 0){\r\n        params[\"searchKey\"] = formValues.searchKey;\r\n        const getStr = value => value && Array.isArray(value) ? value.join(\"&#&\") : undefined;\r\n        params[\"searchValue\"] = getStr(multiValues);\r\n      }\r\n      return params;\r\n    },\r\n    handleSearch(page) {\r\n      if (page) {\r\n        this.pageInfo.page = page\r\n      }\r\n      this.loading = true\r\n      Listing.listPage(this.getParams(true)).then((res)=>{\r\n        if (res && res['code'] === 0) {\r\n          const data = res.data || {};\r\n          this.data = data.records || [];\r\n          const getValue = (value, defaultValue) =>\r\n            value || value === 0 ? +value : defaultValue;\r\n          this.pageInfo = {\r\n            total: getValue(data.total, 0),\r\n            page: getValue(data.page, 1),\r\n            limit: getValue(data.limit, 10)\r\n          };\r\n        } else this.data = [];\r\n      }).catch(() => {\r\n        this.data = [];\r\n      }).finally(() => {\r\n        this.loading = false;\r\n      });\r\n    },\r\n    handlePage(current) {\r\n      this.pageInfo.page = current\r\n      this.handleSearch()\r\n    },\r\n    handlePageSize(size) {\r\n      this.pageInfo.limit = size\r\n      this.handleSearch()\r\n    },\r\n    handleResetForm(form) {\r\n      this.$refs[form].resetFields();\r\n      this.formValues.shops=[];\r\n    },\r\n    // 人员选择相关\r\n    setSelectInfo(info={}){\r\n      this.sellerArr = info.personArr || [];\r\n    },\r\n    loadSite(){\r\n      Site.getAll().then((res)=>{\r\n        if(res['code'] ===0){\r\n          res['data'].forEach(item=>{\r\n            this.siteArr.push({\"id\":item.id,\"name\":item.name});\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleModal(type){\r\n      if(type === 'sync'){\r\n        this.modalSyncVisible = true;\r\n      }else if(type === 'importRelax'){\r\n        this.modalImportVisible = true;\r\n      }else if(type === 'exportLog'){\r\n        this.modalExportVisible = true;\r\n      }\r\n    },\r\n    handleReset(type){\r\n      if(type === 'sync'){\r\n        this.modalSyncVisible = false;\r\n      }\r\n    },\r\n    executeExport () {\r\n      if(new Date() - this.exportTime <= 5000){\r\n        this.$Message.success('导出间隔5S！');\r\n        return;\r\n      }\r\n      this.exportTime = new Date();\r\n      Listing.exportFile(this.getParams(false)).then(res => {\r\n        if (res['code'] === 0) {\r\n          this.$Message.success('导出消息添加成功！');\r\n          this.loading = false;\r\n          ExportFileJs.intervalFunc({\"id\":res.data,\"fileName\":this.exportFileName});\r\n        }else{\r\n          this.$Message.error(res['message']);\r\n        }\r\n      }).catch(() => {\r\n\r\n      }).finally(() => {\r\n        this.loading = false;\r\n      });\r\n    },\r\n  },\r\n\r\n  mounted: function () {\r\n    this.loadSite();\r\n    this.handleSearch(1);\r\n  }\r\n}\r\n\r\n</script>\r\n<style lang=\"less\">\r\n.copyLabel, .ivu-tooltip {\r\n  cursor: pointer;\r\n}\r\n.orderBillDrop{\r\n  .popContentClass{\r\n    margin: 3px 8px 0 8px;\r\n    textarea{\r\n      resize: none;\r\n    }\r\n  }\r\n}\r\n\r\n.search-con-top{\r\n  position: relative;\r\n  padding: 0;\r\n  //标签、sku、asin搜索项\r\n  .saleListingInputItemX{\r\n    .flex-h{\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      margin-top:2px;\r\n    }\r\n    .poptipContentIn{\r\n      margin: 3px 8px 0 8px;\r\n      textarea{\r\n        resize: none;\r\n      }\r\n    }\r\n  }\r\n  .clickDropdownForm{\r\n    position: absolute;\r\n    right: 122px;\r\n    top: 42px;\r\n  }\r\n}\r\n</style>\r\n"]}]}