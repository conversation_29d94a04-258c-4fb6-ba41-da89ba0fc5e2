{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\basf\\listing\\components\\ListingSync.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\basf\\listing\\components\\ListingSync.vue", "mtime": 1752737748515}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQppbXBvcnQgU2hvcFNlbGVjdCBmcm9tICJAL2NvbXBvbmVudHMvc2hvcFNlbGVjdC9pbmRleC52dWUiOw0KaW1wb3J0IExpc3RpbmcgZnJvbSAnQC9hcGkvYmFzZi9saXN0aW5nJw0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiTGlzdGluZ1N5bmMiLA0KICBjb21wb25lbnRzOiB7U2hvcFNlbGVjdH0sDQogIHByb3BzOiB7DQogICAgb25DYW5jZWw6IHsNCiAgICAgIHR5cGU6IEZ1bmN0aW9uDQogICAgfSwNCiAgICB2aXNpYmxlOiB7DQogICAgICAvL+W8ueeql+WFs+mXreaOp+WItg0KICAgICAgdHlwZTogQm9vbGVhbg0KICAgIH0sDQogICAgbG9hZGluZzogew0KICAgICAgdHlwZTogQm9vbGVhbg0KICAgIH0sDQogIH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIHN5bmNUaXRsZToi5ZCM5q2lTGlzdGluZyIsDQogICAgICBzeW5jTG9hZGluZzpmYWxzZSwNCiAgICAgIGxvYWRTdGF0dXM6ZmFsc2UsDQogICAgICBzeW5jRm9ybTp7c2hvcHM6W119DQogICAgfTsNCiAgfSwNCg0KICBtZXRob2RzOiB7DQogICAgb25WaXNpYmxlQ2hhbmdlKHZpc2libGUpew0KICAgICAgaWYoIXZpc2libGUpew0KICAgICAgICB0aGlzLiRyZWZzWyJzeW5jRm9ybVJlZiJdLnJlc2V0RmllbGRzKCk7DQogICAgICB9DQogICAgfSwNCiAgICBvbk9rKCkgew0KICAgICAgLy/ngrnlh7vnoa7lrpoNCiAgICAgIHRoaXMuc3luY0xvYWRpbmcgPSB0cnVlOw0KICAgICAgY29uc3QgZ2V0U3RyID0gdmFsdWUgPT4gdmFsdWUgJiYgQXJyYXkuaXNBcnJheSh2YWx1ZSkgPyB2YWx1ZS5qb2luKCIsIikgOiB1bmRlZmluZWQ7DQogICAgICBjb25zdCBjb250ZW50ID0gdGhpcy5zeW5jRm9ybS5zZWxsZXJTa3UgPyB0aGlzLnN5bmNGb3JtLnNlbGxlclNrdS50cmltKCkucmVwbGFjZSgv77yML2csICIsIikgOiAnJzsNCiAgICAgIGxldCBzZWxsZXJTa3VzID0gY29udGVudC5zcGxpdCgnXG4nKS5maWx0ZXIodj0+ISF2KS5qb2luKCIsIik7DQogICAgICBMaXN0aW5nLnN5bmNMaXN0aW5nKHsic2hvcElkIjpnZXRTdHIodGhpcy5zeW5jRm9ybS5zaG9wcyksInNlbGxlclNrdSI6c2VsbGVyU2t1c30pLnRoZW4oKHJlcyk9PnsNCiAgICAgICAgaWYocmVzICYmIHJlc1snY29kZSddID09PTApew0KICAgICAgICAgIHRoaXMuJE1lc3NhZ2Uuc3VjY2Vzcygi5o+Q5Lqk5ZCM5q2l5Lu75Yqh5oiQ5YqfLOS7u+WKoeato+WcqOaJp+ihjCIpOw0KICAgICAgICAgIHRoaXMub25DYW5jZWwoKTsNCiAgICAgICAgfWVsc2V7DQogICAgICAgICAgdGhpcy4kTWVzc2FnZS5lcnJvcihyZXNbJ21lc3NhZ2UnXSkNCiAgICAgICAgfQ0KICAgICAgfSkuY2F0Y2goKCk9Pnt9KS5maW5hbGx5KCgpPT57dGhpcy5zeW5jTG9hZGluZyA9IGZhbHNlO30pOw0KICAgIH0NCiAgfQ0KfTsNCg=="}, {"version": 3, "sources": ["ListingSync.vue"], "names": [], "mappings": ";AAsBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "ListingSync.vue", "sourceRoot": "src/view/module/lx/listing/components", "sourcesContent": ["<!--\r\n@create date 2019-12-10\r\n@desc 设置同步选择弹框,由于有shopSelect控件，导致放入统一页面时弹框在模态框后面\r\n-->\r\n<template>\r\n  <Modal :title=\"syncTitle\" width=\"500px\" @on-cancel=\"onCancel\" @on-ok=\"onOk\" :value=\"visible\"  @on-visible-change=\"onVisibleChange\">\r\n    <Form ref=\"syncFormRef\" :model=\"syncForm\" :label-width=\"100\">\r\n      <FormItem prop=\"shops\" label=\"网店\">\r\n        <ShopSelect v-model=\"syncForm.shops\" placeholder=\"店铺\" width=\"205px\" :valueField=\"'sid'\" :appendToBody=\"false\"/>\r\n      </FormItem>\r\n      <FormItem label=\"销售Sku\" prop=\"sellerSku\">\r\n        <Input v-model=\"syncForm.sellerSku\" type=\"textarea\" placeholder=\"请输入内容,多个以逗号隔开,最多支持10个\"></Input>\r\n      </FormItem>\r\n    </Form>\r\n    <div slot=\"footer\"></div>\r\n    <template v-slot:footer=\"{}\">\r\n      <Button @click=\"onCancel\" :disabled=\"syncLoading\">取消</Button>\r\n      <Button type=\"primary\" @click=\"onOk\" :loading=\"syncLoading\" style=\"margin-left: 15px\">确认</Button>\r\n    </template>\r\n  </Modal>\r\n</template>\r\n<script>\r\nimport ShopSelect from \"@/components/shopSelect/index.vue\";\r\nimport Listing from '@/api/basf/listing'\r\nexport default {\r\n  name: \"ListingSync\",\r\n  components: {ShopSelect},\r\n  props: {\r\n    onCancel: {\r\n      type: Function\r\n    },\r\n    visible: {\r\n      //弹窗关闭控制\r\n      type: Boolean\r\n    },\r\n    loading: {\r\n      type: Boolean\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      syncTitle:\"同步Listing\",\r\n      syncLoading:false,\r\n      loadStatus:false,\r\n      syncForm:{shops:[]}\r\n    };\r\n  },\r\n\r\n  methods: {\r\n    onVisibleChange(visible){\r\n      if(!visible){\r\n        this.$refs[\"syncFormRef\"].resetFields();\r\n      }\r\n    },\r\n    onOk() {\r\n      //点击确定\r\n      this.syncLoading = true;\r\n      const getStr = value => value && Array.isArray(value) ? value.join(\",\") : undefined;\r\n      const content = this.syncForm.sellerSku ? this.syncForm.sellerSku.trim().replace(/，/g, \",\") : '';\r\n      let sellerSkus = content.split('\\n').filter(v=>!!v).join(\",\");\r\n      Listing.syncListing({\"shopId\":getStr(this.syncForm.shops),\"sellerSku\":sellerSkus}).then((res)=>{\r\n        if(res && res['code'] ===0){\r\n          this.$Message.success(\"提交同步任务成功,任务正在执行\");\r\n          this.onCancel();\r\n        }else{\r\n          this.$Message.error(res['message'])\r\n        }\r\n      }).catch(()=>{}).finally(()=>{this.syncLoading = false;});\r\n    }\r\n  }\r\n};\r\n</script>\r\n"]}]}