{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\base\\workflow\\add.vue?vue&type=style&index=0&id=71fab40d&lang=less&scoped=true&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\base\\workflow\\add.vue", "mtime": 1753948515847}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1752744819993}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["add.vue"], "names": [], "mappings": ";AAg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file": "add.vue", "sourceRoot": "src/view/module/base/workflow", "sourcesContent": ["<template>\r\n  <div class=\"bpmn-workflow-designer\">\r\n    <!-- 顶部工具栏 -->\r\n    <div class=\"toolbar\">\r\n      <Button type=\"primary\" icon=\"md-save\" @click=\"saveWorkflow\">{{ workflowId ? '更新流程' : '保存流程' }}</Button>\r\n      <Button icon=\"md-eye\" @click=\"showXml\">查看XML</Button>\r\n      <Button icon=\"md-refresh\" @click=\"createNewDiagram\">初始化流程</Button>\r\n      <Button icon=\"md-download\" @click=\"exportBpmn\">导出BPMN</Button>\r\n      <Button icon=\"md-cloud-upload\" @click=\"importBpmn\">导入BPMN</Button>\r\n<!--      <Button type=\"warning\" icon=\"md-cloud-upload\" @click=\"testUploadBpmn\">测试上传</Button>-->\r\n      <Button icon=\"md-arrow-back\" @click=\"goBack\">返回列表</Button>\r\n<!--      <Button type=\"warning\" icon=\"md-bug\" @click=\"testNotificationConfig\">测试通知配置</Button>-->\r\n<!--      <Button type=\"info\" icon=\"md-search\" @click=\"checkCurrentXML\">检查当前XML</Button>-->\r\n<!--      <Button type=\"success\" icon=\"md-create\" @click=\"testTitleUpdate\">测试标题更新</Button>-->\r\n    </div>\r\n\r\n    <!-- 三栏布局容器 -->\r\n    <div class=\"designer-container\">\r\n      <!-- 左侧控件栏 -->\r\n      <div class=\"left-panel\">\r\n        <Card title=\"流程元素\" :bordered=\"false\">\r\n          <div class=\"element-categories\">\r\n            <!-- 基础控件 -->\r\n            <div class=\"category-section\">\r\n              <h4>基础控件</h4>\r\n              <div class=\"element-list\">\r\n                <div\r\n                  v-for=\"element in basicControls\"\r\n                  :key=\"element.type\"\r\n                  class=\"element-item\"\r\n                  data-type=\"basic\"\r\n                  @click=\"addElement(element)\"\r\n                >\r\n                  <Icon :type=\"element.icon\" :style=\"{ color: element.color }\" />\r\n                  <span>{{ element.name }}</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 逻辑控件 -->\r\n            <div class=\"category-section\">\r\n              <h4>逻辑控件</h4>\r\n              <div class=\"element-list\">\r\n                <div\r\n                  v-for=\"element in logicControls\"\r\n                  :key=\"element.type\"\r\n                  class=\"element-item\"\r\n                  data-type=\"logic\"\r\n                  @click=\"addElement(element)\"\r\n                >\r\n                  <Icon :type=\"element.icon\" :style=\"{ color: element.color }\" />\r\n                  <span>{{ element.name }}</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </Card>\r\n      </div>\r\n\r\n      <!-- 中间BPMN设计工作区 -->\r\n      <div class=\"center-panel\">\r\n        <div class=\"canvas-wrapper\">\r\n          <!-- BPMN画布容器 - 始终存在于DOM中，但在初始化时需要可见 -->\r\n          <div ref=\"bpmnCanvas\" class=\"bpmn-canvas\" :style=\"{ visibility: loading ? 'hidden' : 'visible' }\"></div>\r\n\r\n          <!-- 加载指示器 -->\r\n          <div v-if=\"loading\" class=\"loading-container\">\r\n            <Spin size=\"large\">\r\n              <Icon type=\"ios-loading\" size=\"18\" class=\"spin-icon-load\"></Icon>\r\n              <div>正在加载 BPMN 设计器...</div>\r\n            </Spin>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 右侧审批人选择栏 -->\r\n      <div class=\"right-panel\">\r\n        <Card title=\"审批人配置\" :bordered=\"false\">\r\n          <div class=\"approver-config\">\r\n            <!-- 当前选中元素信息 -->\r\n            <div v-if=\"selectedElement\" class=\"selected-element-info\">\r\n              <h4>{{ getElementDisplayName(selectedElement.type) }}</h4>\r\n              <p class=\"element-type\">类型: {{ getElementTypeName(selectedElement.type) }}</p>\r\n\r\n              <!-- 开始控件配置 -->\r\n              <div v-if=\"selectedElement.type === 'bpmn:StartEvent'\" class=\"start-config\">\r\n                <Form :model=\"startConfig\" :label-width=\"80\" @submit.prevent>\r\n                  <FormItem label=\"控件名称\">\r\n                    <Input\r\n                      v-model=\"startConfig.name\"\r\n                      placeholder=\"请输入开始控件名称\"\r\n                      @on-change=\"updateElementProperty('name', startConfig.name)\"\r\n                    />\r\n                  </FormItem>\r\n                </Form>\r\n              </div>\r\n\r\n              <!-- 普通控件配置 -->\r\n              <div v-else-if=\"selectedElement.type === 'bpmn:UserTask'\" class=\"normal-config\">\r\n                <Form :model=\"normalConfig\" :label-width=\"80\" @submit.prevent>\r\n                  <FormItem label=\"控件名称\">\r\n                    <Input\r\n                      v-model=\"normalConfig.name\"\r\n                      placeholder=\"请输入普通控件名称\"\r\n                      @on-change=\"updateElementProperty('name', normalConfig.name)\"\r\n                    />\r\n                  </FormItem>\r\n\r\n                  <FormItem label=\"分配方式\">\r\n                    <RadioGroup v-model=\"normalConfig.assignmentType\" @on-change=\"onAssignmentTypeChange\">\r\n                      <Radio label=\"assignee\">指定执行人</Radio>\r\n                      <!-- <Radio label=\"candidateUsers\">候选用户</Radio>\r\n                      <Radio label=\"candidateGroups\">候选组</Radio> -->\r\n                    </RadioGroup>\r\n                  </FormItem>\r\n\r\n                  <FormItem v-if=\"normalConfig.assignmentType === 'assignee'\" label=\"执行人\">\r\n                    <Cascader\r\n                      v-model=\"normalConfig.assigneeCascader\"\r\n                      :data=\"userCascaderData\"\r\n                      placeholder=\"请选择执行人\"\r\n                      :transfer=\"true\"\r\n                      @on-change=\"onAssigneeChange\"\r\n                      style=\"width: 100%\"\r\n                    />\r\n                    <!-- 显示已选择的执行人姓名 -->\r\n                    <div v-if=\"selectedAssigneeName\" class=\"selected-assignee\" style=\"margin-top: 8px; padding: 8px; background: #f0f9ff; border: 1px solid #d1ecf1; border-radius: 4px;\">\r\n                      <Icon type=\"md-person\" style=\"color: #2d8cf0; margin-right: 4px;\" />\r\n                      <span style=\"color: #2d8cf0; font-weight: 500;\">已选择：{{ selectedAssigneeName }}</span>\r\n                    </div>\r\n                  </FormItem>\r\n                  <!-- <FormItem label=\"执行方式\">\r\n                    <RadioGroup\r\n                      v-model=\"normalConfig.executeType\"\r\n                      @on-change=\"updateElementProperty('executeType', normalConfig.executeType)\"\r\n                    >\r\n                      <Radio label=\"manual\">手动执行</Radio>\r\n                      <Radio label=\"auto\">自动执行</Radio>\r\n                    </RadioGroup>\r\n                  </FormItem> -->\r\n                </Form>\r\n              </div>\r\n\r\n              <!-- 结束控件配置 -->\r\n              <div v-else-if=\"selectedElement.type === 'bpmn:EndEvent'\" class=\"end-config\">\r\n                <Form :model=\"endConfig\" :label-width=\"80\" @submit.prevent>\r\n                  <FormItem label=\"控件名称\">\r\n                    <Input\r\n                      v-model=\"endConfig.name\"\r\n                      placeholder=\"请输入结束控件名称\"\r\n                      @on-change=\"updateElementProperty('name', endConfig.name)\"\r\n                    />\r\n                  </FormItem>\r\n\r\n                  <FormItem label=\"结束类型\">\r\n                    <RadioGroup\r\n                      v-model=\"endConfig.endType\"\r\n                      @on-change=\"updateElementProperty('endType', endConfig.endType)\"\r\n                    >\r\n                      <Radio label=\"normal\">正常结束</Radio>\r\n                      <Radio label=\"terminate\">终止结束</Radio>\r\n                    </RadioGroup>\r\n                  </FormItem>\r\n                </Form>\r\n              </div>\r\n\r\n              <!-- 分支控件配置 -->\r\n              <div v-else-if=\"selectedElement.type === 'bpmn:ExclusiveGateway'\" class=\"branch-config\">\r\n                <Form :model=\"branchConfig\" :label-width=\"80\" @submit.prevent>\r\n                  <FormItem label=\"控件名称\">\r\n                    <Input\r\n                      v-model=\"branchConfig.name\"\r\n                      placeholder=\"请输入分支控件名称\"\r\n                      @on-change=\"updateElementProperty('name', branchConfig.name)\"\r\n                    />\r\n                  </FormItem>\r\n                  <Divider>出口连线配置</Divider>\r\n                  <div v-if=\"branchConfig.outgoingFlows && branchConfig.outgoingFlows.length > 0\">\r\n                    <div v-for=\"(flow, index) in branchConfig.outgoingFlows\" :key=\"flow.id\" class=\"flow-config\">\r\n                      <Card :bordered=\"false\" style=\"margin-bottom: 12px; background: #f8f9fa;\">\r\n                        <p slot=\"title\">\r\n                          <Icon type=\"md-arrow-forward\" />\r\n                          连线 {{ index + 1 }}: {{ flow.targetName || flow.target }}\r\n                        </p>\r\n\r\n                        <FormItem label=\"分支条件\" :label-width=\"80\">\r\n                          <RadioGroup\r\n                            :value=\"getConditionType(flow.condition)\"\r\n                            @on-change=\"updateFlowConditionType(flow.id, $event)\"\r\n                          >\r\n                            <Radio label=\"approve\">\r\n                              <Icon type=\"md-checkmark-circle\" style=\"color: #52c41a;\" />\r\n                              通过\r\n                            </Radio>\r\n                            <Radio label=\"reject\">\r\n                              <Icon type=\"md-close-circle\" style=\"color: #f5222d;\" />\r\n                              驳回\r\n                            </Radio>\r\n                          </RadioGroup>\r\n                        </FormItem>\r\n                      </Card>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div v-else style=\"text-align: center; padding: 20px; color: #999;\">\r\n                    <Icon type=\"md-information-circle\" size=\"24\" />\r\n                    <p style=\"margin-top: 8px;\">请先连接分支到其他节点</p>\r\n                  </div>\r\n                </Form>\r\n              </div>\r\n\r\n              <!-- 合并控件配置 -->\r\n              <div v-else-if=\"selectedElement.type === 'bpmn:ParallelGateway'\" class=\"merge-config\">\r\n                <Form :model=\"mergeConfig\" :label-width=\"80\" @submit.prevent>\r\n                  <FormItem label=\"控件名称\">\r\n                    <Input\r\n                      v-model=\"mergeConfig.name\"\r\n                      placeholder=\"请输入合并控件名称\"\r\n                      @on-change=\"updateElementProperty('name', mergeConfig.name)\"\r\n                    />\r\n                  </FormItem>\r\n\r\n                  <FormItem label=\"合并方式\">\r\n                    <RadioGroup\r\n                      v-model=\"mergeConfig.mergeType\"\r\n                      @on-change=\"updateElementProperty('mergeType', mergeConfig.mergeType)\"\r\n                    >\r\n                      <Radio label=\"all\">等待所有分支</Radio>\r\n                      <Radio label=\"any\">等待任一分支</Radio>\r\n                    </RadioGroup>\r\n                  </FormItem>\r\n                </Form>\r\n              </div>\r\n\r\n              <!-- 通知控件配置 -->\r\n              <div v-else-if=\"selectedElement.type === 'bpmn:ServiceTask'\" class=\"notification-config\">\r\n                <Form :model=\"notificationConfig\" :label-width=\"80\" @submit.prevent>\r\n                  <FormItem label=\"控件名称\">\r\n                    <Input\r\n                      v-model=\"notificationConfig.name\"\r\n                      placeholder=\"请输入通知控件名称\"\r\n                      @on-change=\"updateElementProperty('name', notificationConfig.name)\"\r\n                    />\r\n                  </FormItem>\r\n                  <FormItem label=\"通知接收人\">\r\n                    <Cascader\r\n                      v-model=\"notificationConfig.recipientCascader\"\r\n                      :data=\"userCascaderData\"\r\n                      placeholder=\"请选择通知接收人\"\r\n                      :transfer=\"true\"\r\n                      @on-change=\"onRecipientChange\"\r\n                      style=\"width: 100%\"\r\n                    />\r\n                    <!-- 显示已选择的接收人姓名 -->\r\n                    <div v-if=\"selectedRecipientName\" class=\"selected-recipient\" style=\"margin-top: 8px; padding: 8px; background: #f0f9ff; border: 1px solid #d1ecf1; border-radius: 4px;\">\r\n                      <Icon type=\"md-person\" style=\"color: #2d8cf0; margin-right: 4px;\" />\r\n                      <span style=\"color: #2d8cf0; font-weight: 500;\">已选择：{{ selectedRecipientName }}</span>\r\n                    </div>\r\n                  </FormItem>\r\n\r\n                  <FormItem label=\"通知标题\">\r\n                    <Input\r\n                      v-model=\"notificationConfig.notificationTitle\"\r\n                      placeholder=\"请输入通知标题，支持变量如：${processName}\"\r\n                      @on-change=\"updateElementProperty('notificationTitle', notificationConfig.notificationTitle)\"\r\n                    />\r\n                    <div style=\"margin-top: 4px; font-size: 12px; color: #999;\">\r\n                      支持变量：${processName}、${initiator}、${currentDate}等\r\n                    </div>\r\n                  </FormItem>\r\n\r\n                  <FormItem label=\"通知内容\">\r\n                    <Input\r\n                      v-model=\"notificationConfig.notificationContent\"\r\n                      type=\"textarea\"\r\n                      :rows=\"4\"\r\n                      placeholder=\"请输入通知内容，支持变量如：${processName}已提交，请及时处理\"\r\n                      @on-change=\"updateElementProperty('notificationContent', notificationConfig.notificationContent)\"\r\n                    />\r\n                    <div style=\"margin-top: 4px; font-size: 12px; color: #999;\">\r\n                      支持变量：${processName}、${initiator}、${amount}、${department}等\r\n                    </div>\r\n                  </FormItem>\r\n                </Form>\r\n              </div>\r\n\r\n              <!-- 通用属性 -->\r\n              <!-- <div class=\"common-config\">\r\n                <Form :label-width=\"80\" @submit.prevent>\r\n                  <FormItem label=\"元素ID\">\r\n                    <Input v-model=\"selectedElement.id\" disabled />\r\n                  </FormItem>\r\n                </Form>\r\n              </div> -->\r\n            </div>\r\n\r\n            <!-- 未选中元素时的提示 -->\r\n            <div v-else class=\"no-selection\">\r\n              <Icon type=\"md-information-circle\" size=\"48\" color=\"#c5c8ce\" />\r\n              <p>请在设计器中选择一个元素来配置属性</p>\r\n            </div>\r\n          </div>\r\n        </Card>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- XML 预览模态框 -->\r\n    <Modal v-model=\"xmlModalVisible\" title=\"BPMN XML\" width=\"80%\">\r\n      <div class=\"xml-content\">\r\n        <pre><code>{{ bpmnXml }}</code></pre>\r\n      </div>\r\n      <div slot=\"footer\">\r\n        <Button @click=\"xmlModalVisible = false\">关闭</Button>\r\n        <Button type=\"primary\" @click=\"copyXml\">复制XML</Button>\r\n      </div>\r\n    </Modal>\r\n\r\n    <!-- 保存流程模态框 -->\r\n    <Modal v-model=\"saveModalVisible\" :title=\"workflowId ? '更新流程' : '保存流程'\" @on-ok=\"handleSave\">\r\n      <Form :model=\"saveForm\" :label-width=\"80\" @submit.prevent>\r\n        <FormItem label=\"业务流程\" required v-if=\"!workflowId\">\r\n          <Select v-model=\"saveForm.workflowKey\" placeholder=\"请选择流程功能类型\" :loading=\"functionTypeLoading\">\r\n            <Option v-for=\"item in functionTypeList\" :key=\"item.key\" :value=\"item.key\">\r\n              {{ item.name }}\r\n            </Option>\r\n          </Select>\r\n        </FormItem>\r\n        <FormItem label=\"业务流程\" v-if=\"workflowId\">\r\n          <Input :value=\"getWorkflowKeyName\" disabled />\r\n        </FormItem>\r\n        <FormItem label=\"流程名称\" required>\r\n          <Input v-model=\"saveForm.name\" placeholder=\"请输入流程名称\" />\r\n        </FormItem>\r\n      </Form>\r\n    </Modal>\r\n\r\n    <!-- 文件输入 -->\r\n    <input\r\n      ref=\"fileInput\"\r\n      type=\"file\"\r\n      accept=\".bpmn,.xml\"\r\n      style=\"display: none\"\r\n      @change=\"handleFileImport\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport BpmnModeler from 'bpmn-js/lib/Modeler'\r\nimport workflowApi from '@/api/base/workflow'\r\nimport personApi from '@/api/globalApi/personApi'\r\nimport { getAll as getAllDepartments } from '@/api/base/department'\r\n\r\n// BPMN 样式\r\nimport 'bpmn-js/dist/assets/diagram-js.css'\r\nimport 'bpmn-js/dist/assets/bpmn-font/css/bpmn.css'\r\nimport 'bpmn-js/dist/assets/bpmn-font/css/bpmn-codes.css'\r\n\r\nexport default {\r\n  name: 'WorkflowAdd',\r\n  data() {\r\n    return {\r\n      // 流程ID（编辑模式）\r\n      workflowId: null,\r\n      // BPMN 相关\r\n      bpmnModeler: null,\r\n      bpmnXml: '',\r\n      xmlModalVisible: false,\r\n      loading: true,\r\n      // 当前选中的元素\r\n      selectedElement: null,\r\n      // 保存相关\r\n      saveModalVisible: false,\r\n      saveForm: {\r\n        workflowKey: '',\r\n        name: ''\r\n      },\r\n      // 功能类型相关\r\n      functionTypeList: [],\r\n      functionTypeLoading: false,\r\n      // 防止循环调用的标志\r\n      _updatingRecipients: false,\r\n      // 左侧控件栏元素定义\r\n      basicControls: [\r\n        { type: 'bpmn:StartEvent', name: '开始', icon: 'md-play', color: '#52c41a' },\r\n        { type: 'bpmn:UserTask', name: '普通控件', icon: 'md-document', color: '#1890ff' },\r\n        { type: 'bpmn:ServiceTask', name: '通知控件', icon: 'md-notifications', color: '#fa8c16' },\r\n        { type: 'bpmn:EndEvent', name: '结束', icon: 'md-square', color: '#f5222d' }\r\n      ],\r\n      logicControls: [\r\n        { type: 'bpmn:ExclusiveGateway', name: '分支', icon: 'md-git-branch', color: '#fa8c16' },\r\n        // { type: 'bpmn:ParallelGateway', name: '合并', icon: 'md-git-merge', color: '#722ed1' }\r\n      ],\r\n      // 开始控件配置\r\n      startConfig: {\r\n        name: '开始',\r\n        description: ''\r\n      },\r\n      // 普通控件配置\r\n      normalConfig: {\r\n        name: '普通控件',\r\n        assignmentType: 'assignee',\r\n        assignee: '',\r\n        assigneeCascader: [],\r\n        candidateUsers: [],\r\n        candidateGroups: [],\r\n        executeType: 'manual',\r\n        description: ''\r\n      },\r\n      // 结束控件配置\r\n      endConfig: {\r\n        name: '结束',\r\n        endType: 'normal',\r\n        description: ''\r\n      },\r\n      // 分支控件配置\r\n      branchConfig: {\r\n        name: '分支',\r\n        description: '',\r\n        outgoingFlows: [] // 存储出口连线信息\r\n      },\r\n      // 合并控件配置\r\n      mergeConfig: {\r\n        name: '合并',\r\n        mergeType: 'all',\r\n        description: ''\r\n      },\r\n      // 通知控件配置\r\n      notificationConfig: {\r\n        name: '通知控件',\r\n        recipients: '', // 通知接收人（单选）\r\n        recipientCascader: [], // 级联选择器的值\r\n        notificationTitle: '', // 通知标题\r\n        notificationContent: '', // 通知内容\r\n        variables: [], // 流程变量\r\n        autoExecute: true, // 自动执行（不需要人工干预）\r\n        description: ''\r\n      },\r\n      // 用户列表（从API获取）\r\n      userList: [],\r\n      // 部门列表\r\n      departmentList: [],\r\n      // 组列表\r\n      groupList: [],\r\n      // 级联选择器数据\r\n      userCascaderData: [],\r\n      // 默认的 BPMN XML\r\n      defaultBpmnXml: `<?xml version=\"1.0\" encoding=\"UTF-8\"?>\r\n<definitions xmlns=\"http://www.omg.org/spec/BPMN/20100524/MODEL\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:bpmndi=\"http://www.omg.org/spec/BPMN/20100524/DI\" xmlns:omgdc=\"http://www.omg.org/spec/DD/20100524/DC\" xmlns:omgdi=\"http://www.omg.org/spec/DD/20100524/DI\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:activiti=\"http://activiti.org/bpmn\" targetNamespace=\"http://www.activiti.org/test\">\r\n  <process id=\"simpleProcess\" name=\"Simple Process\" isExecutable=\"true\">\r\n    <startEvent id=\"startEvent\" name=\"开始\" />\r\n    <endEvent id=\"endEvent\" name=\"结束\" />\r\n    <sequenceFlow id=\"flow1\" sourceRef=\"startEvent\" targetRef=\"endEvent\" />\r\n  </process>\r\n  <bpmndi:BPMNDiagram id=\"BPMNDiagram_simpleProcess\">\r\n    <bpmndi:BPMNPlane id=\"BPMNPlane_simpleProcess\" bpmnElement=\"simpleProcess\">\r\n      <bpmndi:BPMNShape id=\"BPMNShape_startEvent\" bpmnElement=\"startEvent\">\r\n        <omgdc:Bounds x=\"200\" y=\"150\" width=\"35\" height=\"35\" />\r\n        <bpmndi:BPMNLabel>\r\n          <omgdc:Bounds x=\"207\" y=\"161\" width=\"22\" height=\"14\" />\r\n        </bpmndi:BPMNLabel>\r\n      </bpmndi:BPMNShape>\r\n      <bpmndi:BPMNShape id=\"BPMNShape_endEvent\" bpmnElement=\"endEvent\">\r\n        <omgdc:Bounds x=\"400\" y=\"150\" width=\"35\" height=\"35\" />\r\n        <bpmndi:BPMNLabel>\r\n          <omgdc:Bounds x=\"407\" y=\"161\" width=\"22\" height=\"14\" />\r\n        </bpmndi:BPMNLabel>\r\n      </bpmndi:BPMNShape>\r\n      <bpmndi:BPMNEdge id=\"BPMNEdge_flow1\" bpmnElement=\"flow1\">\r\n        <omgdi:waypoint x=\"235\" y=\"167.5\" />\r\n        <omgdi:waypoint x=\"400\" y=\"167.5\" />\r\n      </bpmndi:BPMNEdge>\r\n    </bpmndi:BPMNPlane>\r\n  </bpmndi:BPMNDiagram>\r\n</definitions>`\r\n    }\r\n  },\r\n\r\n  mounted() {\r\n    // 获取路由参数中的流程ID\r\n    this.workflowId = this.$route.params.id\r\n\r\n    // 加载功能类型数据\r\n    this.loadFunctionTypes()\r\n\r\n    // 加载用户和部门数据\r\n    this.loadUsersAndDepartments()\r\n\r\n    // 添加键盘事件监听，禁止回车键刷新页面\r\n    this.addKeyboardEventListeners()\r\n\r\n    // 使用多重延迟和重试机制确保DOM完全渲染\r\n    this.$nextTick(() => {\r\n      setTimeout(() => {\r\n        this.initBpmnModelerWithRetry()\r\n      }, 200) // 增加延迟时间\r\n    })\r\n  },\r\n\r\n  beforeDestroy() {\r\n    if (this.bpmnModeler) {\r\n      this.bpmnModeler.destroy()\r\n    }\r\n\r\n    // 移除键盘事件监听器\r\n    this.removeKeyboardEventListeners()\r\n  },\r\n\r\n  computed: {\r\n    // 根据workflowKey获取对应的功能类型名称\r\n    getWorkflowKeyName() {\r\n      if (!this.saveForm.workflowKey || !this.functionTypeList.length) {\r\n        return this.saveForm.workflowKey || ''\r\n      }\r\n\r\n      const functionType = this.functionTypeList.find(item => item.key === this.saveForm.workflowKey)\r\n      return functionType ? functionType.name : this.saveForm.workflowKey\r\n    },\r\n\r\n    // 获取选中执行人的姓名\r\n    selectedAssigneeName() {\r\n      if (!this.normalConfig.assignee || !this.userList.length) {\r\n        return ''\r\n      }\r\n\r\n      const selectedUser = this.userList.find(user => user.id === this.normalConfig.assignee)\r\n      return selectedUser ? `${selectedUser.name}` : ''\r\n    },\r\n\r\n    // 获取选中通知接收人的姓名\r\n    selectedRecipientName() {\r\n      console.log('notificationConfig.recipients:', this.notificationConfig);\r\n\r\n\r\n      if (!this.notificationConfig.recipients || !this.userList.length) {\r\n        return ''\r\n      }\r\n\r\n      const selectedUser = this.userList.find(user => user.id === this.notificationConfig.recipients)\r\n      return selectedUser ? `${selectedUser.name}` : ''\r\n    },\r\n\r\n    // 获取选中候选用户的姓名列表\r\n    selectedCandidateUsersNames() {\r\n      if (!this.normalConfig.candidateUsers || !Array.isArray(this.normalConfig.candidateUsers) || !this.userList.length) {\r\n        return []\r\n      }\r\n\r\n      return this.normalConfig.candidateUsers.map(userId => {\r\n        const user = this.userList.find(u => u.id === userId)\r\n        return user ? `${user.name} (${user.department})` : userId\r\n      }).filter(name => name)\r\n    },\r\n\r\n    // 获取选中候选组的姓名列表\r\n    selectedCandidateGroupsNames() {\r\n      if (!this.normalConfig.candidateGroups || !Array.isArray(this.normalConfig.candidateGroups)) {\r\n        return []\r\n      }\r\n\r\n      // 暂时返回原始ID，因为groupList未定义\r\n      return this.normalConfig.candidateGroups.filter(groupId => groupId)\r\n    },\r\n\r\n\r\n\r\n    // 根据部门ID获取该部门的用户列表\r\n    getUsersByDepartment() {\r\n      return (departmentId) => {\r\n        return this.userList.filter(user => user.parent1 === departmentId)\r\n      }\r\n    },\r\n\r\n    // 动态生成的BPMN XML\r\n    dynamicBpmnXml() {\r\n      const processId = this.saveForm.workflowKey || 'simpleProcess'\r\n      const processName = this.saveForm.name || this.getWorkflowKeyName || 'Simple Process'\r\n\r\n      return `<?xml version=\"1.0\" encoding=\"UTF-8\"?>\r\n<definitions xmlns=\"http://www.omg.org/spec/BPMN/20100524/MODEL\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:bpmndi=\"http://www.omg.org/spec/BPMN/20100524/DI\" xmlns:omgdc=\"http://www.omg.org/spec/DD/20100524/DC\" xmlns:omgdi=\"http://www.omg.org/spec/DD/20100524/DI\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:activiti=\"http://activiti.org/bpmn\" targetNamespace=\"http://www.activiti.org/test\">\r\n  <process id=\"${processId}\" name=\"${processName}\" isExecutable=\"true\">\r\n    <startEvent id=\"startEvent\" name=\"开始\" />\r\n    <endEvent id=\"endEvent\" name=\"结束\" />\r\n    <sequenceFlow id=\"flow1\" sourceRef=\"startEvent\" targetRef=\"endEvent\" />\r\n  </process>\r\n  <bpmndi:BPMNDiagram id=\"BPMNDiagram_${processId}\">\r\n    <bpmndi:BPMNPlane id=\"BPMNPlane_${processId}\" bpmnElement=\"${processId}\">\r\n      <bpmndi:BPMNShape id=\"BPMNShape_startEvent\" bpmnElement=\"startEvent\">\r\n        <omgdc:Bounds x=\"100\" y=\"100\" width=\"36\" height=\"36\" />\r\n        <bpmndi:BPMNLabel>\r\n          <omgdc:Bounds x=\"108\" y=\"143\" width=\"22\" height=\"14\" />\r\n        </bpmndi:BPMNLabel>\r\n      </bpmndi:BPMNShape>\r\n      <bpmndi:BPMNShape id=\"BPMNShape_endEvent\" bpmnElement=\"endEvent\">\r\n        <omgdc:Bounds x=\"300\" y=\"100\" width=\"36\" height=\"36\" />\r\n        <bpmndi:BPMNLabel>\r\n          <omgdc:Bounds x=\"308\" y=\"143\" width=\"22\" height=\"14\" />\r\n        </bpmndi:BPMNLabel>\r\n      </bpmndi:BPMNShape>\r\n      <bpmndi:BPMNEdge id=\"BPMNEdge_flow1\" bpmnElement=\"flow1\">\r\n        <omgdi:waypoint x=\"136\" y=\"118\" />\r\n        <omgdi:waypoint x=\"300\" y=\"118\" />\r\n      </bpmndi:BPMNEdge>\r\n    </bpmndi:BPMNPlane>\r\n  </bpmndi:BPMNDiagram>\r\n</definitions>`\r\n    }\r\n  },\r\n\r\n  watch: {\r\n    // 监听saveForm的变化，动态更新BPMN XML中的process元素\r\n    'saveForm.workflowKey': {\r\n      handler(newKey, oldKey) {\r\n        if (newKey && this.bpmnModeler) {\r\n          // 延迟执行，确保DOM更新完成\r\n          this.$nextTick(() => {\r\n            this.updateProcessElement()\r\n          })\r\n        }\r\n      },\r\n      immediate: false\r\n    },\r\n    'saveForm.name': {\r\n      handler(newName) {\r\n        if (newName && this.bpmnModeler) {\r\n          this.$nextTick(() => {\r\n            this.updateProcessElement()\r\n          })\r\n        }\r\n      },\r\n      immediate: false\r\n    },\r\n\r\n    // 监听用户列表变化，确保数据加载完成后更新回显\r\n    userList: {\r\n      handler(newUserList) {\r\n        if (newUserList && newUserList.length > 0) {\r\n          this.$nextTick(() => {\r\n            this.updateCurrentAssigneeCascader()\r\n            this.updateCurrentRecipientsCascader()\r\n          })\r\n        }\r\n      },\r\n      immediate: false\r\n    }\r\n  },\r\n\r\n  methods: {\r\n    // 加载功能类型数据\r\n    async loadFunctionTypes() {\r\n      try {\r\n        this.functionTypeLoading = true\r\n        const response = await workflowApi.getFunctionTypeList()\r\n\r\n        if (response.code === 0 && response.data) {\r\n          this.functionTypeList = response.data.records;\r\n        } else {\r\n          this.$Message.error('加载功能类型失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('加载功能类型失败:', error)\r\n        this.$Message.error('加载功能类型失败')\r\n      } finally {\r\n        this.functionTypeLoading = false\r\n      }\r\n    },\r\n\r\n    // 加载用户和部门数据\r\n    async loadUsersAndDepartments() {\r\n      try {\r\n        // 并行加载用户和部门数据\r\n        const [usersResponse, departmentsResponse] = await Promise.all([\r\n          personApi.getAllPersons({}),\r\n          getAllDepartments()\r\n        ])\r\n        // 处理用户数据\r\n        if (usersResponse && Array.isArray(usersResponse)) {\r\n          this.userList = usersResponse\r\n        } else {\r\n          // 如果API返回的不是数组，尝试从其他属性中获取\r\n          if (usersResponse && usersResponse.data && Array.isArray(usersResponse.data)) {\r\n            this.userList = usersResponse.data\r\n          }\r\n        }\r\n\r\n        // 处理部门数据\r\n        if (departmentsResponse.code === 0 && departmentsResponse.data) {\r\n          this.departmentList = departmentsResponse.data\r\n        }\r\n        // 构建级联选择器数据\r\n        this.buildUserCascaderData()\r\n      } catch (error) {\r\n        this.$Message.error('加载用户数据失败')\r\n      }\r\n    },\r\n\r\n    // 构建级联选择器数据结构\r\n    buildUserCascaderData() {\r\n      const departmentMap = new Map()\r\n      // 构建部门映射\r\n      this.departmentList.forEach(dept => {\r\n        departmentMap.set(dept.id, {\r\n          value: dept.id,\r\n          label: dept.departmentName,\r\n          children: []\r\n        })\r\n      })\r\n      // 将用户分配到对应部门\r\n      this.userList.forEach(user => {\r\n        // 尝试多种可能的部门字段名\r\n        const deptId = user.parent1\r\n        if (deptId && departmentMap.has(deptId)) {\r\n          departmentMap.get(deptId).children.push({\r\n            value: user.id,\r\n            label: user.name || user.nickName || user.userName,\r\n            user: user // 保存完整用户信息\r\n          })\r\n        }\r\n      })\r\n      // 转换为数组格式，只包含有用户的部门\r\n      this.userCascaderData = Array.from(departmentMap.values())\r\n        .filter(dept => dept.children.length > 0)\r\n      // 数据构建完成后，更新当前选中元素的级联选择器值\r\n      this.updateCurrentAssigneeCascader()\r\n    },\r\n\r\n    // 根据用户ID获取级联选择器的值（用于回显）\r\n    getAssigneeCascaderValue(userId) {\r\n      if (!userId || !this.userList.length) {\r\n        return []\r\n      }\r\n      const user = this.userList.find(u => u.id === userId)\r\n      if (user) {\r\n        // 尝试多种可能的部门字段名\r\n        const deptId = user.parent1\r\n        if (deptId) {\r\n          return [deptId, userId]\r\n        }\r\n      }\r\n      return []\r\n    },\r\n\r\n    // 更新当前选中元素的级联选择器值\r\n    updateCurrentAssigneeCascader() {\r\n      if (this.selectedElement &&\r\n          this.selectedElement.type === 'bpmn:UserTask' &&\r\n          this.normalConfig.assignmentType === 'assignee' &&\r\n          this.normalConfig.assignee) {\r\n        const cascaderValue = this.getAssigneeCascaderValue(this.normalConfig.assignee)\r\n        if (cascaderValue.length > 0) {\r\n          this.normalConfig.assigneeCascader = cascaderValue\r\n        }\r\n      }\r\n    },\r\n\r\n    // 更新当前选中通知元素的接收人级联选择器值\r\n    updateCurrentRecipientsCascader() {\r\n      if (this.selectedElement &&\r\n          this.selectedElement.type === 'bpmn:ServiceTask' &&\r\n          this.notificationConfig.recipients) {\r\n        const cascaderValue = this.getRecipientCascaderValue(this.notificationConfig.recipients)\r\n        if (cascaderValue.length > 0) {\r\n          this.notificationConfig.recipientCascader = cascaderValue\r\n        }\r\n      }\r\n    },\r\n\r\n    // 检查BPMN是否被用户修改过\r\n    async checkIfBpmnModified() {\r\n      try {\r\n        if (!this.bpmnModeler) {\r\n          return false\r\n        }\r\n        // 获取当前的BPMN XML\r\n        const { xml } = await this.bpmnModeler.saveXML({ format: true })\r\n        // 检查是否包含用户添加的元素（除了默认的开始和结束事件）\r\n        const parser = new DOMParser()\r\n        const xmlDoc = parser.parseFromString(xml, 'text/xml')\r\n        // 获取所有流程元素\r\n        const userTasks = xmlDoc.querySelectorAll('userTask')\r\n        const exclusiveGateways = xmlDoc.querySelectorAll('exclusiveGateway')\r\n        const parallelGateways = xmlDoc.querySelectorAll('parallelGateway')\r\n        const sequenceFlows = xmlDoc.querySelectorAll('sequenceFlow')\r\n        // 如果有用户任务、网关或超过2个连线（默认只有开始到结束的连线），说明被修改过\r\n        const isModified = userTasks.length > 0 ||\r\n                          exclusiveGateways.length > 0 ||\r\n                          parallelGateways.length > 0 ||\r\n                          sequenceFlows.length > 1\r\n        return isModified\r\n      } catch (error) {\r\n        // 出错时假设已被修改，避免意外覆盖用户设计\r\n        return true\r\n      }\r\n    },\r\n\r\n    // 更新BPMN XML中的process元素\r\n    async updateProcessElement() {\r\n      if (!this.bpmnModeler) {\r\n        return\r\n      }\r\n      try {\r\n        // 获取当前的BPMN XML\r\n        const { xml } = await this.bpmnModeler.saveXML({ format: true })\r\n        // 解析XML并更新process元素\r\n        const parser = new DOMParser()\r\n        const xmlDoc = parser.parseFromString(xml, 'text/xml')\r\n        // 查找process元素\r\n        const processElement = xmlDoc.querySelector('process')\r\n        if (processElement) {\r\n          // 更新process的id和name属性\r\n          if (this.saveForm.workflowKey) {\r\n            processElement.setAttribute('id', this.saveForm.workflowKey)\r\n          }\r\n          if (this.saveForm.name) {\r\n            processElement.setAttribute('name', this.saveForm.name)\r\n          } else if (this.getWorkflowKeyName) {\r\n            processElement.setAttribute('name', this.getWorkflowKeyName)\r\n          }\r\n          // 将更新后的XML转换回字符串\r\n          const serializer = new XMLSerializer()\r\n          const updatedXml = serializer.serializeToString(xmlDoc)\r\n          // 重新导入更新后的XML\r\n          await this.bpmnModeler.importXML(updatedXml)\r\n        }\r\n      } catch (error) {\r\n      }\r\n    },\r\n\r\n    // 带重试机制的初始化方法\r\n    async initBpmnModelerWithRetry() {\r\n      // 如果是编辑模式，先加载现有流程数据\r\n      if (this.workflowId) {\r\n        await this.loadWorkflowData()\r\n      }\r\n      // 容器存在，开始正式初始化\r\n      await this.initBpmnModeler()\r\n    },\r\n\r\n    // 加载工作流数据（编辑模式）\r\n    async loadWorkflowData() {\r\n      try {\r\n        // 调用API获取流程数据\r\n        const response = await workflowApi.getWorkflowById(this.workflowId)\r\n        if (response.code === 0 && response.data) {\r\n          const workflowData = response.data\r\n          // 设置表单数据\r\n          this.saveForm = {\r\n            workflowKey: workflowData.workflowKey || '',\r\n            name: workflowData.workflowName || ''\r\n          }\r\n          // 设置BPMN XML\r\n          this.bpmnXml = workflowData.bpmnXml || this.defaultBpmnXml\r\n        } else {\r\n          throw new Error(response.message || '获取流程数据失败')\r\n        }\r\n      } catch (error) {\r\n        this.$Message.error('加载流程数据失败')\r\n        // 如果加载失败，使用默认数据\r\n        this.saveForm = {\r\n          workflowKey: '',\r\n          name: ''\r\n        }\r\n        this.bpmnXml = this.defaultBpmnXml\r\n      }\r\n    },\r\n\r\n    // 初始化 BPMN 建模器\r\n    async initBpmnModeler() {\r\n      try {\r\n        this.loading = true\r\n        // 临时显示容器以获取正确的尺寸\r\n        this.$refs.bpmnCanvas.style.visibility = 'visible'\r\n        // 等待DOM更新\r\n        await this.$nextTick()\r\n        // 确保容器有有效的尺寸\r\n        await this.ensureValidContainerSize()\r\n        // 重新检查容器尺寸\r\n        const finalSize = {\r\n          width: this.$refs.bpmnCanvas.offsetWidth,\r\n          height: this.$refs.bpmnCanvas.offsetHeight\r\n        }\r\n        // 验证尺寸有效性\r\n        if (finalSize.width <= 0 || finalSize.height <= 0) {\r\n          throw new Error(`容器尺寸无效: ${finalSize.width}x${finalSize.height}`)\r\n        }\r\n        // 创建BPMN建模器\r\n        this.bpmnModeler = new BpmnModeler({\r\n          container: this.$refs.bpmnCanvas,\r\n          keyboard: {\r\n            bindTo: window\r\n          }\r\n        })\r\n        // 导入流程（编辑模式使用已加载的XML，新建模式使用默认XML）\r\n        const xmlToImport = this.bpmnXml || this.defaultBpmnXml\r\n        await this.importDiagram(xmlToImport)\r\n        // 监听元素选择事件\r\n        this.setupEventListeners()\r\n        // 确保画布正确初始化\r\n        await this.ensureCanvasInitialized()\r\n        this.loading = false\r\n        this.$Message.success('BPMN设计器加载成功')\r\n      } catch (error) {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    // 确保容器有有效的尺寸\r\n    async ensureValidContainerSize() {\r\n      const container = this.$refs.bpmnCanvas\r\n      let retryCount = 0\r\n      const maxRetries = 3\r\n      while (retryCount < maxRetries) {\r\n        const size = {\r\n          width: container.offsetWidth,\r\n          height: container.offsetHeight\r\n        }\r\n        // 如果尺寸有效，直接返回\r\n        if (size.width > 0 && size.height > 0) {\r\n          return size\r\n        }\r\n        // 强制设置尺寸\r\n        if (size.width <= 0) {\r\n          container.style.width = '100%'\r\n          container.style.minWidth = '800px'\r\n        }\r\n        if (size.height <= 0) {\r\n          container.style.height = '70vh'\r\n          container.style.minHeight = '500px'\r\n        }\r\n        // 确保容器可见以获取正确尺寸\r\n        container.style.visibility = 'visible'\r\n        // 等待样式生效\r\n        await new Promise(resolve => setTimeout(resolve, 200))\r\n        retryCount++\r\n      }\r\n      // 最后一次检查\r\n      const finalSize = {\r\n        width: container.offsetWidth,\r\n        height: container.offsetHeight\r\n      }\r\n\r\n      if (finalSize.width <= 0 || finalSize.height <= 0) {\r\n        throw new Error(`无法设置有效的容器尺寸: ${finalSize.width}x${finalSize.height}`)\r\n      }\r\n      return finalSize\r\n    },\r\n\r\n    // 确保画布正确初始化\r\n    async ensureCanvasInitialized() {\r\n      try {\r\n        const canvas = this.bpmnModeler.get('canvas')\r\n        // 获取当前视图框\r\n        const viewbox = canvas.viewbox()\r\n        // 检查视图框是否有效\r\n        if (!viewbox || isNaN(viewbox.x) || isNaN(viewbox.y) || isNaN(viewbox.width) || isNaN(viewbox.height)) {\r\n          // 重置画布视图\r\n          canvas.zoom('fit-viewport')\r\n          // 等待重置完成\r\n          await new Promise(resolve => setTimeout(resolve, 100))\r\n          // 再次检查\r\n          const newViewbox = canvas.viewbox()\r\n          // 如果还是无效，设置默认视图框\r\n          if (!newViewbox || isNaN(newViewbox.x) || isNaN(newViewbox.y) || isNaN(newViewbox.width) || isNaN(newViewbox.height)) {\r\n            canvas.viewbox({ x: 0, y: 0, width: 1000, height: 600 })\r\n          }\r\n        }\r\n        // 设置默认缩放\r\n        canvas.zoom(1.0)\r\n      } catch (error) {\r\n      }\r\n    },\r\n\r\n    // 设置事件监听器\r\n    setupEventListeners() {\r\n      try {\r\n        const eventBus = this.bpmnModeler.get('eventBus')\r\n        // 监听元素选择事件\r\n        eventBus.on('selection.changed', (event) => {\r\n          try {\r\n            const element = event.newSelection[0]\r\n            if (element) {\r\n              this.onElementSelected(element)\r\n            } else {\r\n              this.selectedElement = null\r\n            }\r\n          } catch (error) {\r\n          }\r\n        })\r\n        // 监听元素属性变化事件\r\n        eventBus.on('element.changed', (event) => {\r\n          try {\r\n            if (event.element === this.selectedElement) {\r\n              this.onElementChanged(event.element)\r\n            }\r\n          } catch (error) {\r\n          }\r\n        })\r\n        // 监听画布变换事件，防止NaN错误\r\n        eventBus.on('canvas.viewbox.changed', (event) => {\r\n          try {\r\n            const viewbox = event.viewbox\r\n            if (viewbox && (isNaN(viewbox.x) || isNaN(viewbox.y) || isNaN(viewbox.width) || isNaN(viewbox.height))) {\r\n              // 重置到安全的视图框\r\n              const canvas = this.bpmnModeler.get('canvas')\r\n              canvas.viewbox({ x: 0, y: 0, width: 1000, height: 600 })\r\n            }\r\n          } catch (error) {}\r\n        })\r\n      } catch (error) {}\r\n    },\r\n\r\n    // 元素选中处理\r\n    onElementSelected(element) {\r\n      this.selectedElement = element\r\n      this.loadElementProperties(element)\r\n\r\n      // 如果是分支元素，刷新出口连线信息\r\n      if (element && element.type === 'bpmn:ExclusiveGateway') {\r\n        this.$nextTick(() => {\r\n          this.branchConfig.outgoingFlows = this.getOutgoingFlows(element)\r\n        })\r\n      }\r\n    },\r\n\r\n    // 元素变化处理\r\n    onElementChanged(element) {\r\n      this.loadElementProperties(element)\r\n    },\r\n\r\n    // 加载元素属性\r\n    loadElementProperties(element) {\r\n      const businessObject = element.businessObject\r\n      // 首先重置所有配置到默认状态，避免切换元素时数据残留\r\n      this.resetAllConfigs()\r\n      // 根据元素类型设置对应的配置\r\n      switch (element.type) {\r\n        case 'bpmn:StartEvent':\r\n          this.startConfig = {\r\n            name: businessObject.name !== undefined ? businessObject.name : '开始'\r\n          }\r\n          break\r\n        case 'bpmn:UserTask':\r\n          // 尝试多种方式读取assignee\r\n          let assigneeValue = ''\r\n          if (businessObject.$attrs && businessObject.$attrs['activiti:assignee']) {\r\n            assigneeValue = businessObject.$attrs['activiti:assignee']\r\n          } else if (businessObject['activiti:assignee']) {\r\n            assigneeValue = businessObject['activiti:assignee']\r\n          } else if (businessObject.assignee) {\r\n            assigneeValue = businessObject.assignee\r\n          } else if (typeof businessObject.get === 'function') {\r\n            assigneeValue = businessObject.get('activiti:assignee') || ''\r\n          }\r\n\r\n          // 确定分配方式\r\n          let assignmentType = 'assignee'\r\n          if (businessObject['activiti:candidateUsers'] || businessObject.candidateUsers) {\r\n            assignmentType = 'candidateUsers'\r\n          } else if (businessObject['activiti:candidateGroups'] || businessObject.candidateGroups) {\r\n            assignmentType = 'candidateGroups'\r\n          }\r\n\r\n          this.normalConfig = {\r\n            name: businessObject.name !== undefined ? businessObject.name : '普通控件',\r\n            assignmentType: assignmentType,\r\n            assignee: assigneeValue,\r\n            assigneeCascader: this.getAssigneeCascaderValue(assigneeValue),\r\n            candidateUsers: this.parseCommaSeparated(businessObject['activiti:candidateUsers'] || businessObject.candidateUsers),\r\n            candidateGroups: this.parseCommaSeparated(businessObject['activiti:candidateGroups'] || businessObject.candidateGroups),\r\n            executeType: businessObject.executeType || 'manual'\r\n          }\r\n\r\n          // 如果用户数据还没加载完成，延迟设置级联选择器的值\r\n          if (assigneeValue && this.normalConfig.assigneeCascader.length === 0) {\r\n            this.$nextTick(() => {\r\n              setTimeout(() => {\r\n                this.normalConfig.assigneeCascader = this.getAssigneeCascaderValue(assigneeValue)\r\n              }, 100)\r\n            })\r\n          }\r\n          break\r\n\r\n        case 'bpmn:EndEvent':\r\n          this.endConfig = {\r\n            name: businessObject.name !== undefined ? businessObject.name : '结束',\r\n            endType: businessObject.endType || 'normal'\r\n          }\r\n          break\r\n\r\n        case 'bpmn:ExclusiveGateway':\r\n          this.branchConfig = {\r\n            name: businessObject.name !== undefined ? businessObject.name : '分支',\r\n            outgoingFlows: this.getOutgoingFlows(element)\r\n          }\r\n          break\r\n\r\n        case 'bpmn:ParallelGateway':\r\n          this.mergeConfig = {\r\n            name: businessObject.name !== undefined ? businessObject.name : '合并',\r\n            mergeType: businessObject.mergeType || 'all'\r\n          }\r\n          break\r\n\r\n        case 'bpmn:ServiceTask':\r\n          console.log('加载ServiceTask属性:', businessObject)\r\n          console.log('businessObject.$attrs:', businessObject.$attrs)\r\n          console.log('businessObject.extensionElements:', businessObject.extensionElements)\r\n\r\n          // 优先从extensionElements中解析，如果没有则从$attrs中解析\r\n          let notificationData = this.parseServiceTaskExtensionElements(businessObject)\r\n\r\n          // 如果extensionElements中没有数据，尝试从$attrs中读取\r\n          if (!notificationData.recipients && !notificationData.notificationTitle && !notificationData.notificationContent) {\r\n            console.log('从$attrs中读取通知配置')\r\n            let recipients = businessObject.$attrs['activiti:recipients'] || ''\r\n            let notificationTitle = businessObject.$attrs['activiti:notificationTitle'] || ''\r\n            let notificationContent = businessObject.$attrs['activiti:notificationContent'] || ''\r\n\r\n            // 去除 ${} 包装\r\n            recipients = this.removeExpressionWrapper(recipients)\r\n            notificationTitle = this.removeExpressionWrapper(notificationTitle)\r\n            notificationContent = this.removeExpressionWrapper(notificationContent)\r\n\r\n            notificationData = { recipients, notificationTitle, notificationContent }\r\n            console.log('从$attrs解析的数据:', notificationData)\r\n          }\r\n\r\n          console.log('解析的通知数据:：', notificationData)\r\n\r\n          this.notificationConfig = {\r\n            name: businessObject.name !== undefined ? businessObject.name : '通知控件',\r\n            recipients: notificationData.recipients || '',\r\n            recipientCascader: this.getRecipientCascaderValue(notificationData.recipients) || [],\r\n            notificationTitle: notificationData.notificationTitle || '',\r\n            notificationContent: notificationData.notificationContent || '',\r\n            variables: this.parseVariables(businessObject.variables) || [],\r\n            autoExecute: businessObject.autoExecute !== undefined ? businessObject.autoExecute : true,\r\n            description: businessObject.description || ''\r\n          }\r\n\r\n          console.log('设置后的notificationConfig:', this.notificationConfig)\r\n\r\n          // 清理不需要的属性\r\n          this.$nextTick(() => {\r\n            this.cleanServiceTaskProperties()\r\n          })\r\n          break\r\n      }\r\n    },\r\n\r\n    // 重置所有配置到默认状态\r\n    resetAllConfigs() {\r\n      this.startConfig = {\r\n        name: '开始',\r\n        description: ''\r\n      }\r\n\r\n      this.normalConfig = {\r\n        name: '普通控件',\r\n        assignmentType: 'assignee',\r\n        assignee: '',\r\n        assigneeCascader: [],\r\n        candidateUsers: [],\r\n        candidateGroups: [],\r\n        executeType: 'manual',\r\n        description: ''\r\n      }\r\n\r\n      this.endConfig = {\r\n        name: '结束',\r\n        endType: 'normal',\r\n        description: ''\r\n      }\r\n\r\n      this.branchConfig = {\r\n        name: '分支',\r\n        description: '',\r\n        outgoingFlows: []\r\n      }\r\n\r\n      this.mergeConfig = {\r\n        name: '合并',\r\n        mergeType: 'all',\r\n        description: ''\r\n      }\r\n\r\n      this.notificationConfig = {\r\n        name: '通知控件',\r\n        recipients: '',\r\n        recipientCascader: [],\r\n        notificationTitle: '',\r\n        notificationContent: '',\r\n        variables: [],\r\n        autoExecute: true,\r\n        description: ''\r\n      }\r\n    },\r\n\r\n    // 添加元素到画布\r\n    addElement(elementDef) {\r\n      if (!this.bpmnModeler) {\r\n        this.$Message.error('BPMN建模器未初始化')\r\n        return\r\n      }\r\n      // 跳过连线元素\r\n      if (elementDef.type === 'bpmn:SequenceFlow') {\r\n        this.$Message.info('请使用工具栏中的连线工具创建连线')\r\n        return\r\n      }\r\n      // 直接使用最简单可靠的方法\r\n      this.createElementDirectly(elementDef)\r\n    },\r\n\r\n    // 直接创建元素的方法\r\n    createElementDirectly(elementDef, customPosition = null) {\r\n      try {\r\n        const modeling = this.bpmnModeler.get('modeling')\r\n        const elementFactory = this.bpmnModeler.get('elementFactory')\r\n        const canvas = this.bpmnModeler.get('canvas')\r\n        const bpmnFactory = this.bpmnModeler.get('bpmnFactory')\r\n        // 安全地获取画布中心位置\r\n        const position = customPosition || this.getSafeElementPosition(canvas)\r\n        // 验证位置有效性\r\n        if (isNaN(position.x) || isNaN(position.y)) {\r\n          throw new Error(`无效的元素位置: x=${position.x}, y=${position.y}`)\r\n        }\r\n        // 创建业务对象\r\n        const businessObject = bpmnFactory.create(elementDef.type, {\r\n          name: elementDef.name\r\n        })\r\n        // 创建形状\r\n        const shape = elementFactory.createShape({\r\n          type: elementDef.type,\r\n          businessObject: businessObject\r\n        })\r\n        // 获取根元素\r\n        const rootElement = canvas.getRootElement()\r\n        // 添加到画布\r\n        const newElement = modeling.createShape(shape, position, rootElement)\r\n\r\n        // 如果是ServiceTask，设置必要的实现属性\r\n        if (elementDef.type === 'bpmn:ServiceTask') {\r\n          modeling.updateProperties(newElement, {\r\n            'activiti:class': 'com.sy.erp.server.activiti.NotificationDelegate'  // 设置自定义委托类\r\n          })\r\n        }\r\n\r\n        // 选中新元素\r\n        const selection = this.bpmnModeler.get('selection')\r\n        selection.select(newElement)\r\n\r\n        // 强制刷新画布\r\n        canvas.zoom(canvas.zoom())\r\n\r\n        this.$Message.success(`已添加${elementDef.name}`)\r\n        return newElement\r\n      } catch (error) {\r\n        this.$Message.error(`创建元素失败: ${error.message}`)\r\n      }\r\n    },\r\n\r\n    // 安全地获取元素位置\r\n    getSafeElementPosition(canvas) {\r\n      try {\r\n        const viewbox = canvas.viewbox()\r\n        // 检查视图框有效性\r\n        if (!viewbox || isNaN(viewbox.x) || isNaN(viewbox.y) || isNaN(viewbox.width) || isNaN(viewbox.height)) {\r\n          return {\r\n            x: 300 + (Math.random() * 100 - 50),\r\n            y: 200 + (Math.random() * 100 - 50)\r\n          }\r\n        }\r\n        // 计算安全的中心位置\r\n        const centerX = viewbox.x + viewbox.width / 2\r\n        const centerY = viewbox.y + viewbox.height / 2\r\n        // 验证中心位置\r\n        if (isNaN(centerX) || isNaN(centerY)) {\r\n          return {\r\n            x: 300 + (Math.random() * 100 - 50),\r\n            y: 200 + (Math.random() * 100 - 50)\r\n          }\r\n        }\r\n        // 添加随机偏移避免重叠\r\n        return {\r\n          x: centerX + (Math.random() * 100 - 50),\r\n          y: centerY + (Math.random() * 100 - 50)\r\n        }\r\n      } catch (error) {\r\n        // 返回安全的默认位置\r\n        return {\r\n          x: 300 + (Math.random() * 100 - 50),\r\n          y: 200 + (Math.random() * 100 - 50)\r\n        }\r\n      }\r\n    },\r\n\r\n\r\n\r\n    // 更新元素属性\r\n    updateElementProperty(key, value) {\r\n      if (!this.selectedElement) return\r\n      try {\r\n        const modeling = this.bpmnModeler.get('modeling')\r\n\r\n        // 构建更新属性对象\r\n        const properties = {}\r\n\r\n        if (key === 'name') {\r\n          // 允许设置空字符串作为名称\r\n          properties.name = value !== undefined ? value : ''\r\n        } else if (key === 'assignee') {\r\n          // 使用Activiti命名空间的assignee属性\r\n          if (value) {\r\n            properties['activiti:assignee'] = value\r\n          } else {\r\n            properties['activiti:assignee'] = undefined // 删除属性\r\n          }\r\n        } else if (key === 'candidateUsers') {\r\n          // 使用Activiti命名空间的candidateUsers属性\r\n          const candidateUsersValue = Array.isArray(value) ? value.join(',') : value\r\n          if (candidateUsersValue) {\r\n            properties['activiti:candidateUsers'] = candidateUsersValue\r\n          } else {\r\n            properties['activiti:candidateUsers'] = undefined // 删除属性\r\n          }\r\n        } else if (key === 'candidateGroups') {\r\n          // 使用Activiti命名空间的candidateGroups属性\r\n          const candidateGroupsValue = Array.isArray(value) ? value.join(',') : value\r\n          if (candidateGroupsValue) {\r\n            properties['activiti:candidateGroups'] = candidateGroupsValue\r\n          } else {\r\n            properties['activiti:candidateGroups'] = undefined // 删除属性\r\n          }\r\n        } else if (key === 'executeType') {\r\n          properties.executeType = value\r\n        } else if (key === 'endType') {\r\n          properties.endType = value\r\n        } else if (key === 'mergeType') {\r\n          properties.mergeType = value\r\n        } else if (key === 'hasDefault') {\r\n          properties.hasDefault = value\r\n        } else if (key === 'condition') {\r\n          properties.condition = value\r\n        } else if (key === 'description') {\r\n          properties.description = value\r\n        } else if (key === 'recipients') {\r\n          // 处理接收人数据 - 直接设置到$attrs中\r\n          properties['activiti:recipients'] = value\r\n          // 确保ServiceTask有必要的实现属性\r\n          if (this.selectedElement.type === 'bpmn:ServiceTask') {\r\n            properties['activiti:class'] = 'com.sy.erp.server.activiti.NotificationDelegate'\r\n          }\r\n        } else if (key === 'notificationTitle') {\r\n          // 处理通知标题 - 直接设置到$attrs中\r\n          console.log('更新通知标题:', value)\r\n          properties['activiti:notificationTitle'] = value\r\n        } else if (key === 'notificationContent') {\r\n          // 处理通知内容 - 直接设置到$attrs中\r\n          properties['activiti:notificationContent'] = value\r\n        } else if (key === 'variables') {\r\n          properties.variables = JSON.stringify(value)\r\n        } else if (key === 'autoExecute') {\r\n          properties.autoExecute = value\r\n        }\r\n\r\n        // 更新业务对象属性\r\n        modeling.updateProperties(this.selectedElement, properties)\r\n\r\n        console.log(`更新属性 ${key}:`, value)\r\n        console.log('实际更新的properties:', properties)\r\n      } catch (error) {\r\n        console.error('更新元素属性失败:', error)\r\n        this.$Message.error('更新属性失败')\r\n      }\r\n    },\r\n\r\n    // 删除元素属性\r\n    removeElementProperty(propertyName) {\r\n      if (!this.selectedElement) return\r\n      try {\r\n        const modeling = this.bpmnModeler.get('modeling')\r\n\r\n        // 构建删除属性对象，设置为undefined来删除属性\r\n        const properties = {}\r\n        properties[propertyName] = undefined\r\n\r\n        // 更新业务对象属性\r\n        modeling.updateProperties(this.selectedElement, properties)\r\n\r\n        console.log(`删除属性: ${propertyName}`)\r\n      } catch (error) {\r\n        console.error('删除元素属性失败:', error)\r\n      }\r\n    },\r\n\r\n    // 获取元素的出口连线信息\r\n    getOutgoingFlows(element) {\r\n      if (!element || !element.outgoing) {\r\n        return []\r\n      }\r\n\r\n      return element.outgoing.map(flow => {\r\n        const target = flow.target\r\n        const targetName = target ? (target.businessObject.name || target.id) : 'Unknown'\r\n\r\n        // 获取连线的条件表达式\r\n        let condition = ''\r\n        if (flow.businessObject.conditionExpression) {\r\n          condition = flow.businessObject.conditionExpression.body || ''\r\n        }\r\n\r\n        // 检查是否为默认分支\r\n        const isDefault = element.businessObject.default === flow.id\r\n\r\n        return {\r\n          id: flow.id,\r\n          target: target ? target.id : '',\r\n          targetName: targetName,\r\n          condition: condition,\r\n          isDefault: isDefault\r\n        }\r\n      })\r\n    },\r\n\r\n    // 更新连线条件\r\n    updateFlowCondition(flowId, condition) {\r\n      try {\r\n        const modeling = this.bpmnModeler.get('modeling')\r\n        const elementRegistry = this.bpmnModeler.get('elementRegistry')\r\n        const bpmnFactory = this.bpmnModeler.get('bpmnFactory')\r\n\r\n        const flow = elementRegistry.get(flowId)\r\n        if (!flow) {\r\n          console.error('找不到连线:', flowId)\r\n          return\r\n        }\r\n\r\n        if (condition && condition.trim()) {\r\n          // 验证条件表达式格式\r\n          const validation = this.validateConditionExpression(condition)\r\n          if (!validation.valid) {\r\n            this.$Message.error(validation.message)\r\n            return\r\n          }\r\n\r\n          // 使用bpmnFactory创建条件表达式，确保包含所有必要属性\r\n          const conditionExpression = bpmnFactory.create('bpmn:FormalExpression')\r\n          conditionExpression.body = condition.trim()\r\n\r\n          modeling.updateProperties(flow, {\r\n            conditionExpression: conditionExpression\r\n          })\r\n\r\n          console.log('条件表达式已设置:', {\r\n            flowId: flowId,\r\n            condition: condition.trim(),\r\n            expression: conditionExpression\r\n          })\r\n        } else {\r\n          // 清除条件表达式\r\n          modeling.updateProperties(flow, {\r\n            conditionExpression: undefined\r\n          })\r\n\r\n          console.log('条件表达式已清除:', flowId)\r\n        }\r\n\r\n        console.log(`更新连线条件: ${flowId} -> ${condition}`)\r\n\r\n        // 更新本地数据\r\n        const flowIndex = this.branchConfig.outgoingFlows.findIndex(f => f.id === flowId)\r\n        if (flowIndex !== -1) {\r\n          this.branchConfig.outgoingFlows[flowIndex].condition = condition\r\n        }\r\n      } catch (error) {\r\n        console.error('更新连线条件失败:', error)\r\n        this.$Message.error('更新连线条件失败')\r\n      }\r\n    },\r\n\r\n    // 设置默认分支\r\n    setDefaultFlow(flowId, isDefault) {\r\n      try {\r\n        const modeling = this.bpmnModeler.get('modeling')\r\n\r\n        if (isDefault) {\r\n          // 设置为默认分支\r\n          modeling.updateProperties(this.selectedElement, {\r\n            default: flowId\r\n          })\r\n\r\n          // 清除该连线的条件表达式（默认分支不需要条件）\r\n          this.updateFlowCondition(flowId, '')\r\n\r\n          // 取消其他分支的默认状态\r\n          this.branchConfig.outgoingFlows.forEach(flow => {\r\n            if (flow.id !== flowId) {\r\n              flow.isDefault = false\r\n            }\r\n          })\r\n        } else {\r\n          // 取消默认分支\r\n          modeling.updateProperties(this.selectedElement, {\r\n            default: undefined\r\n          })\r\n        }\r\n\r\n        console.log(`设置默认分支: ${flowId} -> ${isDefault}`)\r\n\r\n        // 更新本地数据\r\n        const flowIndex = this.branchConfig.outgoingFlows.findIndex(f => f.id === flowId)\r\n        if (flowIndex !== -1) {\r\n          this.branchConfig.outgoingFlows[flowIndex].isDefault = isDefault\r\n        }\r\n      } catch (error) {\r\n        console.error('设置默认分支失败:', error)\r\n        this.$Message.error('设置默认分支失败')\r\n      }\r\n    },\r\n\r\n    // 验证条件表达式格式\r\n    validateConditionExpression(condition) {\r\n      if (!condition || !condition.trim()) {\r\n        return { valid: true, message: '' }\r\n      }\r\n\r\n      const trimmedCondition = condition.trim()\r\n\r\n      // 预定义条件直接通过\r\n      if (trimmedCondition === '${agree == true}' || trimmedCondition === '${agree == false}') {\r\n        return { valid: true, message: '' }\r\n      }\r\n\r\n      // 自定义条件的基本格式检查\r\n      if (!trimmedCondition.startsWith('${') || !trimmedCondition.endsWith('}')) {\r\n        return {\r\n          valid: false,\r\n          message: '条件表达式必须以 ${ 开始，以 } 结束'\r\n        }\r\n      }\r\n\r\n      // 检查表达式内容\r\n      const expression = trimmedCondition.slice(2, -1).trim()\r\n      if (!expression) {\r\n        return {\r\n          valid: false,\r\n          message: '条件表达式不能为空'\r\n        }\r\n      }\r\n\r\n      return { valid: true, message: '' }\r\n    },\r\n\r\n    // 获取条件类型（通过、驳回或自定义）\r\n    getConditionType(condition) {\r\n      if (!condition) return ''\r\n\r\n      const trimmedCondition = condition.trim()\r\n      if (trimmedCondition === '${agree == true}') {\r\n        return 'approve'\r\n      } else if (trimmedCondition === '${agree == false}') {\r\n        return 'reject'\r\n      } else {\r\n        return 'custom'\r\n      }\r\n    },\r\n\r\n    // 更新连线条件类型\r\n    updateFlowConditionType(flowId, conditionType) {\r\n      let condition = ''\r\n\r\n      switch (conditionType) {\r\n        case 'approve':\r\n          condition = '${agree == true}'\r\n          break\r\n        case 'reject':\r\n          condition = '${agree == false}'\r\n          break\r\n        case 'custom':\r\n          // 保持当前条件或清空\r\n          const flowIndex = this.branchConfig.outgoingFlows.findIndex(f => f.id === flowId)\r\n          if (flowIndex !== -1) {\r\n            const currentCondition = this.branchConfig.outgoingFlows[flowIndex].condition\r\n            if (currentCondition !== '${agree == true}' && currentCondition !== '${agree == false}') {\r\n              condition = currentCondition\r\n            } else {\r\n              condition = ''\r\n            }\r\n          }\r\n          break\r\n        default:\r\n          condition = ''\r\n      }\r\n\r\n      // 更新连线条件\r\n      this.updateFlowCondition(flowId, condition)\r\n\r\n      // 更新本地数据\r\n      const flowIndex = this.branchConfig.outgoingFlows.findIndex(f => f.id === flowId)\r\n      if (flowIndex !== -1) {\r\n        this.branchConfig.outgoingFlows[flowIndex].condition = condition\r\n      }\r\n    },\r\n\r\n    // 获取元素显示名称\r\n    getElementDisplayName(type) {\r\n      const displayMap = {\r\n        'bpmn:StartEvent': '开始',\r\n        'bpmn:UserTask': '普通控件',\r\n        'bpmn:ServiceTask': '通知控件',\r\n        'bpmn:EndEvent': '结束',\r\n        'bpmn:ExclusiveGateway': '分支',\r\n        'bpmn:ParallelGateway': '合并'\r\n      }\r\n      return displayMap[type] || type\r\n    },\r\n\r\n    // 获取元素类型名称\r\n    getElementTypeName(type) {\r\n      const typeMap = {\r\n        'bpmn:StartEvent': '基础控件',\r\n        'bpmn:UserTask': '基础控件',\r\n        'bpmn:ServiceTask': '基础控件',\r\n        'bpmn:EndEvent': '基础控件',\r\n        'bpmn:ExclusiveGateway': '逻辑控件',\r\n        'bpmn:ParallelGateway': '逻辑控件'\r\n      }\r\n      return typeMap[type] || type\r\n    },\r\n\r\n    // 解析逗号分隔的字符串为数组\r\n    parseCommaSeparated(value) {\r\n      if (!value) return []\r\n      if (Array.isArray(value)) return value\r\n      return value.split(',').map(item => item.trim()).filter(item => item)\r\n    },\r\n\r\n    // 解析变量JSON字符串\r\n    parseVariables(value) {\r\n      if (!value) return []\r\n      if (Array.isArray(value)) return value\r\n      try {\r\n        return JSON.parse(value)\r\n      } catch (error) {\r\n        console.error('解析变量失败:', error)\r\n        return []\r\n      }\r\n    },\r\n\r\n    // 添加变量\r\n    addVariable() {\r\n      this.notificationConfig.variables.push({\r\n        name: '',\r\n        value: ''\r\n      })\r\n      this.updateElementProperty('variables', this.notificationConfig.variables)\r\n    },\r\n\r\n    // 删除变量\r\n    removeVariable(index) {\r\n      this.notificationConfig.variables.splice(index, 1)\r\n      this.updateElementProperty('variables', this.notificationConfig.variables)\r\n    },\r\n\r\n    // 更新变量\r\n    updateVariable(index, field, value) {\r\n      if (this.notificationConfig.variables[index]) {\r\n        this.notificationConfig.variables[index][field] = value\r\n        this.updateElementProperty('variables', this.notificationConfig.variables)\r\n      }\r\n    },\r\n\r\n    // 解析接收人数据（从逗号分隔的字符串转换为用户ID数组）\r\n    parseRecipients(value) {\r\n      console.log('parseRecipients 输入:', value)\r\n\r\n      if (!value) return []\r\n      if (Array.isArray(value)) return value\r\n\r\n      // 从逗号分隔的字符串中解析用户ID\r\n      const userIds = value.split(',').map(item => item.trim()).filter(item => item)\r\n      console.log('解析出的用户ID列表:', userIds)\r\n\r\n      return userIds\r\n    },\r\n\r\n\r\n\r\n    // 获取接收人姓名\r\n    getRecipientName(userId) {\r\n      const user = this.userList.find(u => u.id === userId)\r\n      return user ? `${user.name}` : userId\r\n    },\r\n\r\n    // 根据用户ID获取级联选择器的值（用于回显）\r\n    getRecipientCascaderValue(userId) {\r\n      console.log('getRecipientCascaderValue 被调用，userId:', userId)\r\n      console.log('userList length:', this.userList.length)\r\n\r\n      if (!userId || !this.userList.length) {\r\n        console.log('userId为空或userList未加载')\r\n        return []\r\n      }\r\n\r\n      const user = this.userList.find(u => u.id === userId)\r\n      console.log('找到的用户:', user)\r\n\r\n      if (user) {\r\n        const deptId = user.parent1\r\n        console.log('用户部门ID:', deptId)\r\n        if (deptId) {\r\n          const result = [deptId, userId]\r\n          console.log('返回级联选择器值:', result)\r\n          return result\r\n        }\r\n      } else {\r\n        console.log('未找到对应的用户，用户ID:', userId)\r\n      }\r\n\r\n      return []\r\n    },\r\n\r\n\r\n\r\n    // 去除表达式包装，提取实际值\r\n    removeExpressionWrapper(value) {\r\n      if (value && typeof value === 'string' && value.startsWith('${') && value.endsWith('}')) {\r\n        return value.slice(2, -1) // 去掉开头的 ${ 和结尾的 }\r\n      }\r\n      return value\r\n    },\r\n\r\n    // 解析ServiceTask的extensionElements\r\n    parseServiceTaskExtensionElements(businessObject) {\r\n      const result = {\r\n        recipients: '',\r\n        notificationTitle: '',\r\n        notificationContent: ''\r\n      }\r\n\r\n      try {\r\n        if (!businessObject.extensionElements || !businessObject.extensionElements.values) {\r\n          console.log('没有找到extensionElements')\r\n          return result\r\n        }\r\n\r\n        console.log('extensionElements.values:', businessObject.extensionElements.values)\r\n\r\n        // 遍历extensionElements中的所有元素\r\n        businessObject.extensionElements.values.forEach(element => {\r\n          console.log('处理element:', element)\r\n\r\n          if (element.$type === 'activiti:field') {\r\n            const fieldName = element.name\r\n            let fieldValue = ''\r\n\r\n            // 从activiti:string元素中获取值\r\n            if (element.$children && element.$children.length > 0) {\r\n              const stringElement = element.$children[0]\r\n              if (stringElement.$type === 'activiti:string') {\r\n                fieldValue = stringElement.$body || stringElement.body || ''\r\n              }\r\n            } else if (element.string) {\r\n              // 兼容其他可能的格式\r\n              if (typeof element.string === 'string') {\r\n                fieldValue = element.string\r\n              } else if (element.string.body) {\r\n                fieldValue = element.string.body\r\n              } else if (element.string.$body) {\r\n                fieldValue = element.string.$body\r\n              } else if (element.string.value) {\r\n                fieldValue = element.string.value\r\n              }\r\n            }\r\n\r\n            // 去除 ${} 包装，提取实际值\r\n            fieldValue = this.removeExpressionWrapper(fieldValue)\r\n\r\n            console.log(`找到field: ${fieldName} = ${fieldValue}`)\r\n\r\n            if (fieldName === 'recipients') {\r\n              result.recipients = fieldValue\r\n            } else if (fieldName === 'notificationTitle') {\r\n              result.notificationTitle = fieldValue\r\n            } else if (fieldName === 'notificationContent') {\r\n              result.notificationContent = fieldValue\r\n            }\r\n          }\r\n        })\r\n\r\n        console.log('解析结果:', result)\r\n      } catch (error) {\r\n        console.error('解析ServiceTask extensionElements失败:', error)\r\n      }\r\n\r\n      return result\r\n    },\r\n\r\n    // 转换ServiceTask的属性为extensionElements格式（字符串操作）\r\n    convertServiceTaskAttributesToExtensions(xml) {\r\n      try {\r\n        console.log('=== 开始转换ServiceTask属性为extensionElements ===')\r\n        console.log('输入XML长度:', xml.length)\r\n\r\n        // 使用正则表达式查找所有ServiceTask元素\r\n        const serviceTaskRegex = /<serviceTask([^>]*?)(?:\\/>|>(.*?)<\\/serviceTask>)/gs\r\n\r\n        // 先检查是否有ServiceTask\r\n        const serviceTaskMatches = xml.match(/<serviceTask[^>]*>/g)\r\n        console.log('找到的ServiceTask元素:', serviceTaskMatches)\r\n\r\n        return xml.replace(serviceTaskRegex, (match, attributes, content) => {\r\n          console.log('=== 处理ServiceTask ===')\r\n          console.log('匹配的内容:', match)\r\n          console.log('属性部分:', attributes)\r\n          console.log('内容部分:', content)\r\n\r\n          // 提取activiti属性\r\n          const recipientsMatch = attributes.match(/activiti:recipients=\"([^\"]*)\"/)\r\n          const titleMatch = attributes.match(/activiti:notificationTitle=\"([^\"]*)\"/)\r\n          const contentMatch = attributes.match(/activiti:notificationContent=\"([^\"]*)\"/)\r\n\r\n          const recipients = recipientsMatch ? recipientsMatch[1] : null\r\n          const notificationTitle = titleMatch ? titleMatch[1] : null\r\n          const notificationContent = contentMatch ? contentMatch[1] : null\r\n\r\n          console.log('提取的属性值:', { recipients, notificationTitle, notificationContent })\r\n          console.log('原始属性字符串:', attributes)\r\n\r\n          // 如果没有需要转换的属性，返回原始内容\r\n          if (!recipients && !notificationTitle && !notificationContent) {\r\n            console.log('没有需要转换的属性，返回原始内容')\r\n            return match\r\n          }\r\n\r\n          console.log('找到需要转换的属性，开始转换')\r\n\r\n          // 移除通知相关的activiti属性\r\n          let cleanAttributes = attributes\r\n            .replace(/\\s*activiti:recipients=\"[^\"]*\"/g, '')\r\n            .replace(/\\s*activiti:notificationTitle=\"[^\"]*\"/g, '')\r\n            .replace(/\\s*activiti:notificationContent=\"[^\"]*\"/g, '')\r\n\r\n          // 确保有activiti:class属性\r\n          if (!cleanAttributes.includes('activiti:class=')) {\r\n            cleanAttributes += ' activiti:class=\"com.sy.erp.server.activiti.NotificationDelegate\"'\r\n          }\r\n\r\n          // 构建extensionElements\r\n          let extensionElements = ''\r\n          if (recipients || notificationTitle || notificationContent) {\r\n            extensionElements = '\\n      <extensionElements>'\r\n\r\n            if (recipients) {\r\n              extensionElements += `\r\n        <activiti:field name=\"recipients\">\r\n          <activiti:string>\\${${recipients}}</activiti:string>\r\n        </activiti:field>`\r\n            }\r\n\r\n            if (notificationTitle) {\r\n              extensionElements += `\r\n        <activiti:field name=\"notificationTitle\">\r\n          <activiti:string>\\${${notificationTitle}}</activiti:string>\r\n        </activiti:field>`\r\n            }\r\n\r\n            if (notificationContent) {\r\n              extensionElements += `\r\n        <activiti:field name=\"notificationContent\">\r\n          <activiti:string>\\${${notificationContent}}</activiti:string>\r\n        </activiti:field>`\r\n            }\r\n\r\n            extensionElements += '\\n      </extensionElements>'\r\n          }\r\n\r\n          // 重新构建ServiceTask元素\r\n          if (content) {\r\n            // 有内容的ServiceTask\r\n            return `<serviceTask${cleanAttributes}>${extensionElements}${content}</serviceTask>`\r\n          } else {\r\n            // 自闭合的ServiceTask\r\n            return `<serviceTask${cleanAttributes}>${extensionElements}\\n    </serviceTask>`\r\n          }\r\n        })\r\n      } catch (error) {\r\n        console.error('转换ServiceTask属性失败:', error)\r\n        return xml // 如果转换失败，返回原始XML\r\n      }\r\n    },\r\n\r\n    // 清理ServiceTask元素的不需要属性\r\n    cleanServiceTaskProperties() {\r\n      if (!this.selectedElement || this.selectedElement.type !== 'bpmn:ServiceTask') {\r\n        return\r\n      }\r\n\r\n      try {\r\n        const modeling = this.bpmnModeler.get('modeling')\r\n        const businessObject = this.selectedElement.businessObject\r\n\r\n        // 如果存在targetRoles属性，将其删除\r\n        if (businessObject.targetRoles !== undefined) {\r\n          console.log('删除不需要的targetRoles属性')\r\n          modeling.updateProperties(this.selectedElement, {\r\n            targetRoles: undefined\r\n          })\r\n        }\r\n      } catch (error) {\r\n        console.error('清理ServiceTask属性失败:', error)\r\n      }\r\n    },\r\n\r\n\r\n\r\n\r\n\r\n    // 执行人级联选择器change事件\r\n    onAssigneeChange(value) {\r\n      if (value && value.length === 2) {\r\n        // value[0] 是部门ID，value[1] 是用户ID\r\n        const userId = value[1]\r\n        this.normalConfig.assignee = userId\r\n        this.updateElementProperty('assignee', userId)\r\n      } else {\r\n        this.normalConfig.assignee = ''\r\n        this.updateElementProperty('assignee', '')\r\n      }\r\n    },\r\n\r\n    // 通知接收人级联选择器change事件\r\n    onRecipientChange(value) {\r\n      console.log('onRecipientChange 被调用，value:', value)\r\n      if (value && value.length === 2) {\r\n        // value[0] 是部门ID，value[1] 是用户ID\r\n        const userId = value[1]\r\n        console.log('设置接收人ID:', userId)\r\n        this.notificationConfig.recipients = userId\r\n        this.updateElementProperty('recipients', userId)\r\n      } else {\r\n        console.log('清空接收人')\r\n        this.notificationConfig.recipients = ''\r\n        this.updateElementProperty('recipients', '')\r\n      }\r\n    },\r\n\r\n    // 分配方式改变时的处理\r\n    onAssignmentTypeChange(type) {\r\n      // 清空其他分配方式的值并删除对应的XML属性\r\n      if (type !== 'assignee') {\r\n        this.normalConfig.assignee = ''\r\n        this.normalConfig.assigneeCascader = []\r\n        this.removeElementProperty('activiti:assignee')\r\n      }\r\n      if (type !== 'candidateUsers') {\r\n        this.normalConfig.candidateUsers = []\r\n        this.removeElementProperty('activiti:candidateUsers')\r\n      }\r\n      if (type !== 'candidateGroups') {\r\n        this.normalConfig.candidateGroups = []\r\n        this.removeElementProperty('activiti:candidateGroups')\r\n      }\r\n    },\r\n\r\n    // 导入流程图\r\n    async importDiagram(xml) {\r\n      try {\r\n        const result = await this.bpmnModeler.importXML(xml)\r\n        this.bpmnXml = xml\r\n\r\n        // 确保画布正确渲染\r\n        const canvas = this.bpmnModeler.get('canvas')\r\n        canvas.zoom('fit-viewport')\r\n\r\n        // 如果有警告，打印出来但不阻止流程\r\n        if (result.warnings && result.warnings.length > 0) {\r\n          console.warn('BPMN导入警告:', result.warnings)\r\n        }\r\n\r\n        console.log('流程图加载成功')\r\n      } catch (error) {\r\n        console.error('导入流程图失败:', error)\r\n        this.$Message.error(`流程图加载失败: ${error.message}`)\r\n        throw error\r\n      }\r\n    },\r\n\r\n    // 保存工作流\r\n    async saveWorkflow() {\r\n      this.saveModalVisible = true\r\n    },\r\n\r\n    // 处理保存\r\n    async handleSave() {\r\n      if (!this.saveForm.name.trim()) {\r\n        this.$Message.error('请输入流程名称')\r\n        return false\r\n      }\r\n\r\n      // 新建模式需要验证workflowKey\r\n      if (!this.workflowId && !this.saveForm.workflowKey.trim()) {\r\n        this.$Message.error('请输入流程Key')\r\n        return false\r\n      }\r\n\r\n      try {\r\n        // 新建模式检查workflowKey是否已存在\r\n        if (!this.workflowId) {\r\n          const checkResponse = await workflowApi.checkWorkflowKey(this.saveForm.workflowKey)\r\n          if (checkResponse.code === 0 && checkResponse.data) {\r\n            this.$Message.error('该工作流Key已存在，请选择其他功能类型')\r\n            return false\r\n          }\r\n        }\r\n\r\n        // 在保存前先更新process元素\r\n        await this.updateProcessElement()\r\n\r\n        // 获取当前的 BPMN XML\r\n        let { xml } = await this.bpmnModeler.saveXML({ format: true })\r\n\r\n        // 转换ServiceTask的属性为extensionElements格式\r\n        xml = this.convertServiceTaskAttributesToExtensions(xml)\r\n\r\n        const workflowData = {\r\n          id: this.workflowId, // 编辑模式包含ID\r\n          workflowKey: this.saveForm.workflowKey, // 工作流Key\r\n          workflowName: this.saveForm.name, // 后端字段名为 workflowName\r\n          bpmnXml: xml\r\n          // description 和 category 字段在后端 Workflow 模型中不存在\r\n          // updateTime 由后端自动填充，不需要前端传递\r\n        }\r\n\r\n        // 调用API保存到后端\r\n        let response\r\n        if (this.workflowId) {\r\n          // 编辑模式：更新现有流程\r\n          response = await workflowApi.updateWorkflow(workflowData)\r\n        } else {\r\n          // 新建模式：创建新流程\r\n          response = await workflowApi.save(workflowData)\r\n        }\r\n\r\n        if (response.code === 0) {\r\n          this.$Message.success(this.workflowId ? '工作流更新成功！' : '工作流保存成功！')\r\n          this.saveModalVisible = false\r\n\r\n          // 保存成功后返回列表页\r\n          setTimeout(() => {\r\n            this.goBack()\r\n          }, 1000)\r\n        } else {\r\n          this.$Message.error(response.message || '保存失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('保存工作流失败:', error)\r\n        this.$Message.error('保存失败，请重试')\r\n      }\r\n    },\r\n\r\n    // 导出 BPMN\r\n    async exportBpmn() {\r\n      try {\r\n        const { xml } = await this.bpmnModeler.saveXML({ format: true })\r\n\r\n        const blob = new Blob([xml], { type: 'application/xml' })\r\n        const url = URL.createObjectURL(blob)\r\n        const a = document.createElement('a')\r\n        a.href = url\r\n        a.download = `workflow_${Date.now()}.bpmn`\r\n        a.click()\r\n        URL.revokeObjectURL(url)\r\n\r\n        this.$Message.success('流程导出成功')\r\n      } catch (error) {\r\n        console.error('导出失败:', error)\r\n        this.$Message.error('流程导出失败')\r\n      }\r\n    },\r\n\r\n    // 测试上传BPMN文件\r\n    async testUploadBpmn() {\r\n      if (!this.saveForm.workflowKey.trim()) {\r\n        this.$Message.error('请先输入流程Key')\r\n        return\r\n      }\r\n\r\n      try {\r\n        const { xml } = await this.bpmnModeler.saveXML({ format: true })\r\n\r\n        this.$Message.loading('正在测试上传BPMN文件...')\r\n        const response = await workflowApi.uploadBpmnFile(this.saveForm.workflowKey, xml)\r\n\r\n        if (response.code === 0) {\r\n          this.$Message.success(`BPMN文件上传成功！文件路径：${response.data}`)\r\n          console.log('上传成功，文件路径：', response.data)\r\n        } else {\r\n          this.$Message.error(response.message || 'BPMN文件上传失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('测试上传失败:', error)\r\n        this.$Message.error('测试上传失败，请重试')\r\n      }\r\n    },\r\n\r\n    // 导入 BPMN\r\n    importBpmn() {\r\n      this.$refs.fileInput.click()\r\n    },\r\n\r\n    // 处理文件导入\r\n    handleFileImport(event) {\r\n      const file = event.target.files[0]\r\n      if (!file) return\r\n\r\n      const reader = new FileReader()\r\n      reader.onload = (e) => {\r\n        const xml = e.target.result\r\n        this.importDiagram(xml)\r\n      }\r\n      reader.readAsText(file)\r\n\r\n      // 清空文件输入\r\n      event.target.value = ''\r\n    },\r\n\r\n    // 显示 XML\r\n    async showXml() {\r\n      try {\r\n        if (!this.bpmnModeler) {\r\n          this.$Message.error('BPMN建模器未初始化')\r\n          return\r\n        }\r\n\r\n        console.log('开始获取XML...')\r\n\r\n        // 先验证模型的有效性\r\n        const elementRegistry = this.bpmnModeler.get('elementRegistry')\r\n        const allElements = elementRegistry.getAll()\r\n\r\n        console.log('当前模型元素数量:', allElements.length)\r\n\r\n        // 检查是否有问题的条件表达式\r\n        const sequenceFlows = allElements.filter(el => el.type === 'bpmn:SequenceFlow')\r\n        sequenceFlows.forEach(flow => {\r\n          if (flow.businessObject.conditionExpression) {\r\n            console.log('发现条件表达式:', {\r\n              flowId: flow.id,\r\n              condition: flow.businessObject.conditionExpression,\r\n              body: flow.businessObject.conditionExpression.body\r\n            })\r\n          }\r\n        })\r\n\r\n        const { xml } = await this.bpmnModeler.saveXML({ format: true })\r\n        this.bpmnXml = xml\r\n        this.xmlModalVisible = true\r\n\r\n        console.log('XML获取成功')\r\n      } catch (error) {\r\n        console.error('获取XML失败:', error)\r\n        console.error('错误详情:', {\r\n          message: error.message,\r\n          stack: error.stack,\r\n          name: error.name\r\n        })\r\n\r\n        // 尝试获取更多错误信息\r\n        if (error.message && error.message.includes('isGeneric')) {\r\n          this.$Message.error('条件表达式格式错误，请检查分支条件设置')\r\n        } else {\r\n          this.$Message.error(`获取XML失败: ${error.message}`)\r\n        }\r\n      }\r\n    },\r\n\r\n    // 复制 XML\r\n    copyXml() {\r\n      navigator.clipboard.writeText(this.bpmnXml).then(() => {\r\n        this.$Message.success('XML已复制到剪贴板')\r\n      }).catch(() => {\r\n        this.$Message.error('复制失败')\r\n      })\r\n    },\r\n\r\n    // 创建新流程图\r\n    createNewDiagram() {\r\n      this.$Modal.confirm({\r\n        title: '确认初始化新流程',\r\n        content: '初始化新流程将清空当前内容，确定继续吗？',\r\n        onOk: () => {\r\n          // 使用动态生成的BPMN XML，如果没有选择功能类型则使用默认XML\r\n          const xmlToUse = this.saveForm.workflowKey ? this.dynamicBpmnXml : this.defaultBpmnXml\r\n          this.importDiagram(xmlToUse)\r\n        }\r\n      })\r\n    },\r\n\r\n    // 返回列表\r\n    goBack() {\r\n      this.$router.push('/base/workflow/index')\r\n    },\r\n\r\n    // 测试通知配置\r\n    async testNotificationConfig() {\r\n      if (!this.selectedElement || this.selectedElement.type !== 'bpmn:ServiceTask') {\r\n        this.$Message.warning('请先选择一个通知控件')\r\n        return\r\n      }\r\n\r\n      console.log('=== 测试通知配置 ===')\r\n      console.log('当前选中元素:', this.selectedElement)\r\n      console.log('当前通知配置:', this.notificationConfig)\r\n\r\n      // 设置测试数据\r\n      this.notificationConfig.recipients = 'test-user-123'\r\n      this.notificationConfig.notificationTitle = '测试通知标题'\r\n      this.notificationConfig.notificationContent = '测试通知内容'\r\n\r\n      // 更新到BPMN元素\r\n      this.updateElementProperty('recipients', this.notificationConfig.recipients)\r\n      this.updateElementProperty('notificationTitle', this.notificationConfig.notificationTitle)\r\n      this.updateElementProperty('notificationContent', this.notificationConfig.notificationContent)\r\n\r\n      console.log('设置测试数据后的businessObject:', this.selectedElement.businessObject)\r\n      console.log('businessObject.$attrs:', this.selectedElement.businessObject.$attrs)\r\n\r\n      // 获取XML并查看结果\r\n      try {\r\n        const { xml } = await this.bpmnModeler.saveXML({ format: true })\r\n        console.log('=== 原始XML ===')\r\n        console.log(xml)\r\n\r\n        // 测试转换\r\n        const convertedXml = this.convertServiceTaskAttributesToExtensions(xml)\r\n        console.log('=== 转换后的XML ===')\r\n        console.log(convertedXml)\r\n\r\n        // 检查是否包含CDATA\r\n        if (convertedXml.includes('<![CDATA[')) {\r\n          console.log('✅ CDATA格式正确生成')\r\n        } else {\r\n          console.log('❌ 缺少CDATA格式')\r\n        }\r\n\r\n        this.$Message.success('测试完成，请查看控制台日志')\r\n      } catch (error) {\r\n        console.error('测试失败:', error)\r\n        this.$Message.error('测试失败')\r\n      }\r\n    },\r\n\r\n    // 检查当前XML\r\n    async checkCurrentXML() {\r\n      try {\r\n        const { xml } = await this.bpmnModeler.saveXML({ format: true })\r\n        console.log('=== 当前完整XML ===')\r\n        console.log(xml)\r\n\r\n        // 查找ServiceTask部分\r\n        const serviceTaskMatches = xml.match(/<serviceTask[^>]*>[\\s\\S]*?<\\/serviceTask>/g)\r\n        if (serviceTaskMatches) {\r\n          console.log('=== 找到的ServiceTask元素 ===')\r\n          serviceTaskMatches.forEach((task, index) => {\r\n            console.log(`ServiceTask ${index + 1}:`)\r\n            console.log(task)\r\n          })\r\n        } else {\r\n          console.log('没有找到ServiceTask元素')\r\n        }\r\n\r\n        this.$Message.success('XML检查完成，请查看控制台')\r\n      } catch (error) {\r\n        console.error('检查XML失败:', error)\r\n        this.$Message.error('检查XML失败')\r\n      }\r\n    },\r\n\r\n    // 测试标题更新\r\n    testTitleUpdate() {\r\n      if (!this.selectedElement || this.selectedElement.type !== 'bpmn:ServiceTask') {\r\n        this.$Message.warning('请先选择一个通知控件')\r\n        return\r\n      }\r\n\r\n      console.log('=== 测试标题更新 ===')\r\n      console.log('当前通知配置:', this.notificationConfig)\r\n\r\n      // 修改标题\r\n      const newTitle = '新的测试标题_' + Date.now()\r\n      console.log('设置新标题:', newTitle)\r\n\r\n      this.notificationConfig.notificationTitle = newTitle\r\n      this.updateElementProperty('notificationTitle', newTitle)\r\n\r\n      console.log('更新后的通知配置:', this.notificationConfig)\r\n      console.log('更新后的businessObject.$attrs:', this.selectedElement.businessObject.$attrs)\r\n\r\n      this.$Message.success('标题更新测试完成，请查看控制台')\r\n    },\r\n\r\n    // 添加键盘事件监听器\r\n    addKeyboardEventListeners() {\r\n      // 绑定键盘事件到document\r\n      document.addEventListener('keydown', this.handleKeyDown, true)\r\n      document.addEventListener('keypress', this.handleKeyPress, true)\r\n    },\r\n\r\n    // 移除键盘事件监听器\r\n    removeKeyboardEventListeners() {\r\n      document.removeEventListener('keydown', this.handleKeyDown, true)\r\n      document.removeEventListener('keypress', this.handleKeyPress, true)\r\n    },\r\n\r\n    // 处理键盘按下事件\r\n    handleKeyDown(event) {\r\n      // 检查是否是回车键\r\n      if (event.key === 'Enter' || event.keyCode === 13) {\r\n        // 检查是否在特定的输入元素中\r\n        const target = event.target\r\n        const tagName = target.tagName.toLowerCase()\r\n\r\n        // 允许在textarea中使用回车键\r\n        if (tagName === 'textarea') {\r\n          return\r\n        }\r\n\r\n        // 允许在特定的输入框中使用回车键（如搜索框）\r\n        if (tagName === 'input' && target.type === 'search') {\r\n          return\r\n        }\r\n\r\n        // 阻止其他情况下的回车键默认行为\r\n        event.preventDefault()\r\n        event.stopPropagation()\r\n\r\n        console.log('回车键被阻止，防止页面刷新')\r\n      }\r\n    },\r\n\r\n    // 处理键盘按键事件\r\n    handleKeyPress(event) {\r\n      // 检查是否是回车键\r\n      if (event.key === 'Enter' || event.keyCode === 13) {\r\n        const target = event.target\r\n        const tagName = target.tagName.toLowerCase()\r\n\r\n        // 允许在textarea中使用回车键\r\n        if (tagName === 'textarea') {\r\n          return\r\n        }\r\n\r\n        // 允许在特定的输入框中使用回车键\r\n        if (tagName === 'input' && target.type === 'search') {\r\n          return\r\n        }\r\n\r\n        // 阻止其他情况下的回车键\r\n        event.preventDefault()\r\n        event.stopPropagation()\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.bpmn-workflow-designer {\r\n  height: 100vh;\r\n  min-height: 100vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n  background: #f5f7fa;\r\n\r\n  .toolbar {\r\n    background: white;\r\n    padding: 12px 16px;\r\n    border-bottom: 1px solid #e8eaec;\r\n    display: flex;\r\n    gap: 8px;\r\n    box-shadow: 0 2px 4px rgba(0,0,0,0.1);\r\n    z-index: 10;\r\n  }\r\n\r\n  .designer-container {\r\n    flex: 1;\r\n    display: flex;\r\n    overflow: hidden;\r\n    min-height: 75vh; // 确保整个设计器容器有足够高度\r\n\r\n    // 左侧控件栏\r\n    .left-panel {\r\n      width: 280px;\r\n      background: white;\r\n      border-right: 1px solid #e8eaec;\r\n      overflow-y: auto;\r\n\r\n      .element-categories {\r\n        padding: 16px;\r\n\r\n        .category-section {\r\n          margin-bottom: 24px;\r\n\r\n          h4 {\r\n            margin: 0 0 12px 0;\r\n            color: #515a6e;\r\n            font-size: 14px;\r\n            font-weight: 500;\r\n            padding-bottom: 8px;\r\n            border-bottom: 1px solid #e8eaec;\r\n          }\r\n\r\n          .element-list {\r\n            display: flex;\r\n            flex-direction: column;\r\n            gap: 8px;\r\n\r\n            .element-item {\r\n              display: flex;\r\n              align-items: center;\r\n              padding: 12px 14px;\r\n              background: #f8f8f9;\r\n              border: 1px solid #dcdee2;\r\n              border-radius: 8px;\r\n              cursor: pointer;\r\n              transition: all 0.3s;\r\n              position: relative;\r\n\r\n              &:hover {\r\n                background: #e8f4ff;\r\n                border-color: #2d8cf0;\r\n                transform: translateY(-2px);\r\n                box-shadow: 0 4px 12px rgba(45, 140, 240, 0.3);\r\n              }\r\n\r\n              &:active {\r\n                transform: translateY(-1px);\r\n              }\r\n\r\n              i {\r\n                margin-right: 12px;\r\n                font-size: 18px;\r\n                width: 20px;\r\n                text-align: center;\r\n              }\r\n\r\n              span {\r\n                font-size: 14px;\r\n                color: #515a6e;\r\n                font-weight: 600;\r\n              }\r\n\r\n              // 基础控件样式\r\n              &[data-type=\"basic\"] {\r\n                border-left: 4px solid #52c41a;\r\n\r\n                i {\r\n                  color: #52c41a;\r\n                }\r\n\r\n                &:hover {\r\n                  background: #f6ffed;\r\n                  border-color: #52c41a;\r\n                  box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);\r\n                }\r\n              }\r\n\r\n              // 逻辑控件样式\r\n              &[data-type=\"logic\"] {\r\n                border-left: 4px solid #fa8c16;\r\n\r\n                i {\r\n                  color: #fa8c16;\r\n                }\r\n\r\n                &:hover {\r\n                  background: #fff7e6;\r\n                  border-color: #fa8c16;\r\n                  box-shadow: 0 4px 12px rgba(250, 140, 22, 0.3);\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    // 中间设计工作区\r\n    .center-panel {\r\n      flex: 1;\r\n      background: white;\r\n      position: relative;\r\n      min-height: 75vh; // 确保最小高度\r\n      display: flex;\r\n      flex-direction: column;\r\n\r\n      .debug-info {\r\n        flex-shrink: 0; // 不压缩调试信息\r\n      }\r\n\r\n      .canvas-wrapper {\r\n        flex: 1; // 占用剩余空间\r\n        min-height: 70vh; // 确保最小高度\r\n        position: relative;\r\n        overflow: hidden; // 防止内容溢出\r\n\r\n        .bpmn-canvas {\r\n          width: 100%;\r\n          height: 100%;\r\n          min-height: 70vh; // 确保最小高度\r\n          position: absolute; // 绝对定位确保完整覆盖\r\n          top: 0;\r\n          left: 0;\r\n          right: 0;\r\n          bottom: 0;\r\n        }\r\n\r\n        .loading-container {\r\n          position: absolute;\r\n          top: 0;\r\n          left: 0;\r\n          right: 0;\r\n          bottom: 0;\r\n          display: flex;\r\n          flex-direction: column;\r\n          align-items: center;\r\n          justify-content: center;\r\n          background: rgba(255, 255, 255, 0.95);\r\n          z-index: 1000; // 确保在最上层\r\n          color: #666;\r\n          backdrop-filter: blur(2px); // 添加模糊效果\r\n\r\n          .spin-icon-load {\r\n            animation: ani-spin 1s linear infinite;\r\n          }\r\n\r\n          @keyframes ani-spin {\r\n            from { transform: rotate(0deg); }\r\n            to { transform: rotate(360deg); }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    // 右侧审批人选择栏\r\n    .right-panel {\r\n      width: 350px;\r\n      background: white;\r\n      border-left: 1px solid #e8eaec;\r\n      overflow-y: auto;\r\n\r\n      .approver-config {\r\n        padding: 16px;\r\n\r\n        .selected-element-info {\r\n          h4 {\r\n            margin: 0 0 8px 0;\r\n            color: #2d8cf0;\r\n            font-size: 16px;\r\n            font-weight: 600;\r\n          }\r\n\r\n          .element-type {\r\n            margin: 0 0 16px 0;\r\n            color: #999;\r\n            font-size: 12px;\r\n          }\r\n\r\n          .approval-config,\r\n          .gateway-config,\r\n          .common-config {\r\n            margin-bottom: 16px;\r\n\r\n            .ivu-form-item {\r\n              margin-bottom: 16px;\r\n            }\r\n          }\r\n        }\r\n\r\n        .no-selection {\r\n          display: flex;\r\n          flex-direction: column;\r\n          align-items: center;\r\n          justify-content: center;\r\n          padding: 60px 20px;\r\n          text-align: center;\r\n          color: #c5c8ce;\r\n\r\n          p {\r\n            margin: 16px 0 0 0;\r\n            font-size: 14px;\r\n          }\r\n        }\r\n      }\r\n\r\n      .user-management {\r\n        padding: 16px;\r\n\r\n        .user-list {\r\n          .user-item {\r\n            display: flex;\r\n            align-items: center;\r\n            padding: 12px 0;\r\n            border-bottom: 1px solid #f0f0f0;\r\n\r\n            &:last-child {\r\n              border-bottom: none;\r\n            }\r\n\r\n            .user-info {\r\n              margin-left: 12px;\r\n              flex: 1;\r\n\r\n              .user-name {\r\n                font-size: 14px;\r\n                font-weight: 500;\r\n                color: #515a6e;\r\n                margin-bottom: 4px;\r\n              }\r\n\r\n              .user-dept {\r\n                font-size: 12px;\r\n                color: #999;\r\n              }\r\n            }\r\n          }\r\n        }\r\n\r\n        .group-list {\r\n          .group-item {\r\n            display: flex;\r\n            align-items: center;\r\n            padding: 12px 0;\r\n            border-bottom: 1px solid #f0f0f0;\r\n\r\n            &:last-child {\r\n              border-bottom: none;\r\n            }\r\n\r\n            i {\r\n              color: #2d8cf0;\r\n              font-size: 18px;\r\n              margin-right: 12px;\r\n            }\r\n\r\n            .group-info {\r\n              flex: 1;\r\n\r\n              .group-name {\r\n                font-size: 14px;\r\n                font-weight: 500;\r\n                color: #515a6e;\r\n                margin-bottom: 4px;\r\n              }\r\n\r\n              .group-desc {\r\n                font-size: 12px;\r\n                color: #999;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .xml-content {\r\n    max-height: 500px;\r\n    overflow: auto;\r\n    background: #f8f9fa;\r\n    border: 1px solid #e8eaec;\r\n    border-radius: 4px;\r\n    padding: 16px;\r\n\r\n    pre {\r\n      margin: 0;\r\n      white-space: pre-wrap;\r\n      word-wrap: break-word;\r\n      font-family: 'Courier New', monospace;\r\n      font-size: 12px;\r\n      line-height: 1.4;\r\n    }\r\n  }\r\n}\r\n\r\n// 全局样式覆盖\r\n:deep(.djs-palette) {\r\n  left: 20px;\r\n  top: 20px;\r\n  z-index: 100;\r\n}\r\n\r\n:deep(.djs-container) {\r\n  background: #f8f9fa;\r\n}\r\n\r\n:deep(.properties-panel-header) {\r\n  background: #f5f7fa;\r\n  border-bottom: 1px solid #e8eaec;\r\n}\r\n\r\n// BPMN 工具栏样式优化\r\n:deep(.djs-palette .djs-palette-entries) {\r\n  background: white;\r\n  border: 1px solid #e8eaec;\r\n  border-radius: 6px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n}\r\n\r\n:deep(.djs-palette .entry) {\r\n  border-radius: 4px;\r\n  transition: all 0.2s;\r\n\r\n  &:hover {\r\n    background: #e8f4ff !important;\r\n  }\r\n}\r\n\r\n// BPMN 元素样式优化\r\n:deep(.djs-element) {\r\n  .djs-visual rect,\r\n  .djs-visual circle,\r\n  .djs-visual path {\r\n    stroke: #2d8cf0;\r\n    stroke-width: 2;\r\n  }\r\n\r\n  &.djs-selected .djs-visual rect,\r\n  &.djs-selected .djs-visual circle,\r\n  &.djs-selected .djs-visual path {\r\n    stroke: #1890ff;\r\n    stroke-width: 3;\r\n  }\r\n}\r\n\r\n// 上下文菜单样式\r\n:deep(.djs-context-pad) {\r\n  background: white;\r\n  border: 1px solid #e8eaec;\r\n  border-radius: 6px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n\r\n  .entry {\r\n    border-radius: 4px;\r\n    transition: all 0.2s;\r\n\r\n    &:hover {\r\n      background: #e8f4ff !important;\r\n    }\r\n  }\r\n}\r\n\r\n// 选中元素高亮样式\r\n:deep(.djs-element.djs-selected) {\r\n  .djs-visual {\r\n    rect, circle, path, polygon {\r\n      stroke: #1890ff !important;\r\n      stroke-width: 3 !important;\r\n    }\r\n  }\r\n}\r\n\r\n// 用户任务特殊样式\r\n:deep(.djs-element[data-element-id*=\"UserTask\"]) {\r\n  .djs-visual rect {\r\n    fill: #e8f4ff;\r\n    stroke: #2d8cf0;\r\n  }\r\n}\r\n\r\n// 网关特殊样式\r\n:deep(.djs-element[data-element-id*=\"Gateway\"]) {\r\n  .djs-visual polygon {\r\n    fill: #fff7e6;\r\n    stroke: #fa8c16;\r\n  }\r\n}\r\n\r\n// 事件特殊样式\r\n:deep(.djs-element[data-element-id*=\"Event\"]) {\r\n  .djs-visual circle {\r\n    fill: #f6ffed;\r\n    stroke: #52c41a;\r\n  }\r\n}\r\n\r\n// 隐藏默认的属性面板（我们使用自定义的右侧面板）\r\n:deep(.bio-properties-panel) {\r\n  display: none;\r\n}\r\n\r\n// 调整画布容器样式\r\n:deep(.djs-container) {\r\n  width: 100% !important;\r\n  height: 100% !important;\r\n  min-height: 70vh !important;\r\n  position: absolute !important;\r\n  top: 0 !important;\r\n  left: 0 !important;\r\n\r\n  .djs-canvas {\r\n    background: #fafafa;\r\n    width: 100% !important;\r\n    height: 100% !important;\r\n    min-height: 70vh !important;\r\n  }\r\n}\r\n\r\n// 连线样式优化\r\n:deep(.djs-connection) {\r\n  .djs-visual path {\r\n    stroke: #666;\r\n    stroke-width: 2;\r\n    fill: none;\r\n  }\r\n\r\n  &.djs-selected .djs-visual path {\r\n    stroke: #1890ff;\r\n    stroke-width: 3;\r\n  }\r\n}\r\n\r\n// 工具提示样式\r\n:deep(.djs-tooltip) {\r\n  background: rgba(0, 0, 0, 0.8);\r\n  color: white;\r\n  padding: 4px 8px;\r\n  border-radius: 4px;\r\n  font-size: 12px;\r\n}\r\n</style>\r\n\r\n\r\n\r\n"]}]}