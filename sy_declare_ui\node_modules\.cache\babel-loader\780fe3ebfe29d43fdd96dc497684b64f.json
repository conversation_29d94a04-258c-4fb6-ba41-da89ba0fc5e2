{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\api\\base\\gateway\\rateLimit.js", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\api\\base\\gateway\\rateLimit.js", "mtime": 1752737748399}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\.babelrc", "mtime": 1752737748316}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "rateLimitPath", "listPage", "params", "url", "method", "addRateLimit", "data", "updateRateLimit", "removeRateLimit", "id", "getApiDetails", "parentId", "addApiDetails", "_ref", "apiIds", "join"], "sources": ["D:/Users/<USER>/Desktop/dev/sy_declare_ui/src/api/base/gateway/rateLimit.js"], "sourcesContent": ["import request from '@/libs/request'\r\n\r\nconst rateLimitPath = \"/base/rateLimit\";\r\n\r\n/**\r\n * 获取分页数据\r\n */\r\nconst listPage = (params) => {\r\n  return request({\r\n    url: rateLimitPath + '/listPage',\r\n    params,\r\n    method: 'get'\r\n  })\r\n}\r\n/**\r\n * 添加数据\r\n */\r\nconst addRateLimit = (data) => {\r\n  return request({\r\n    url: rateLimitPath + '/addRateLimit',\r\n    data,\r\n    method: 'post'\r\n  })\r\n}\r\n/**\r\n * 添加数据\r\n */\r\nconst updateRateLimit = (data) => {\r\n  return request({\r\n    url: rateLimitPath + '/updateRateLimit',\r\n    data,\r\n    method: 'post'\r\n  })\r\n}\r\n\r\n/**\r\n * 删除\r\n */\r\nconst removeRateLimit = (id) => {\r\n  const data = {\"id\": id}\r\n  return request({\r\n    url: rateLimitPath + '/removeRateLimit',\r\n    data,\r\n    method: 'post'\r\n  })\r\n}\r\n\r\n/**\r\n * 查询策略已绑定API列表\r\n * @param id\r\n */\r\nconst getApiDetails = (id) => {\r\n  const params = {\r\n    parentId: id\r\n  }\r\n  return request({\r\n    url: rateLimitPath + '/getApiDetails',\r\n    params,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n/**\r\n * 绑定API\r\n * @param policyId\r\n * @param apiIds\r\n */\r\nconst addApiDetails = ({parentId, apiIds}) => {\r\n  const data = {\r\n    parentId: parentId,\r\n    apiIds: apiIds.join(',')\r\n  }\r\n  return request({\r\n    url: rateLimitPath + '/addApiDetails',\r\n    data,\r\n    method: 'post'\r\n  })\r\n}\r\nexport default {\r\n  listPage,\r\n  addRateLimit,\r\n  updateRateLimit,\r\n  removeRateLimit,\r\n  getApiDetails,\r\n  addApiDetails\r\n}\r\n"], "mappings": ";AAAA,OAAOA,OAAO,MAAM,gBAAgB;AAEpC,IAAMC,aAAa,GAAG,iBAAiB;;AAEvC;AACA;AACA;AACA,IAAMC,QAAQ,GAAG,SAAXA,QAAQA,CAAIC,MAAM,EAAK;EAC3B,OAAOH,OAAO,CAAC;IACbI,GAAG,EAAEH,aAAa,GAAG,WAAW;IAChCE,MAAM,EAANA,MAAM;IACNE,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;AACD;AACA;AACA;AACA,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAIC,IAAI,EAAK;EAC7B,OAAOP,OAAO,CAAC;IACbI,GAAG,EAAEH,aAAa,GAAG,eAAe;IACpCM,IAAI,EAAJA,IAAI;IACJF,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;AACD;AACA;AACA;AACA,IAAMG,eAAe,GAAG,SAAlBA,eAAeA,CAAID,IAAI,EAAK;EAChC,OAAOP,OAAO,CAAC;IACbI,GAAG,EAAEH,aAAa,GAAG,kBAAkB;IACvCM,IAAI,EAAJA,IAAI;IACJF,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA,IAAMI,eAAe,GAAG,SAAlBA,eAAeA,CAAIC,EAAE,EAAK;EAC9B,IAAMH,IAAI,GAAG;IAAC,IAAI,EAAEG;EAAE,CAAC;EACvB,OAAOV,OAAO,CAAC;IACbI,GAAG,EAAEH,aAAa,GAAG,kBAAkB;IACvCM,IAAI,EAAJA,IAAI;IACJF,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA,IAAMM,aAAa,GAAG,SAAhBA,aAAaA,CAAID,EAAE,EAAK;EAC5B,IAAMP,MAAM,GAAG;IACbS,QAAQ,EAAEF;EACZ,CAAC;EACD,OAAOV,OAAO,CAAC;IACbI,GAAG,EAAEH,aAAa,GAAG,gBAAgB;IACrCE,MAAM,EAANA,MAAM;IACNE,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,IAAMQ,aAAa,GAAG,SAAhBA,aAAaA,CAAAC,IAAA,EAA2B;EAAA,IAAtBF,QAAQ,GAAAE,IAAA,CAARF,QAAQ;IAAEG,MAAM,GAAAD,IAAA,CAANC,MAAM;EACtC,IAAMR,IAAI,GAAG;IACXK,QAAQ,EAAEA,QAAQ;IAClBG,MAAM,EAAEA,MAAM,CAACC,IAAI,CAAC,GAAG;EACzB,CAAC;EACD,OAAOhB,OAAO,CAAC;IACbI,GAAG,EAAEH,aAAa,GAAG,gBAAgB;IACrCM,IAAI,EAAJA,IAAI;IACJF,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;AACD,eAAe;EACbH,QAAQ,EAARA,QAAQ;EACRI,YAAY,EAAZA,YAAY;EACZE,eAAe,EAAfA,eAAe;EACfC,eAAe,EAAfA,eAAe;EACfE,aAAa,EAAbA,aAAa;EACbE,aAAa,EAAbA;AACF,CAAC"}]}