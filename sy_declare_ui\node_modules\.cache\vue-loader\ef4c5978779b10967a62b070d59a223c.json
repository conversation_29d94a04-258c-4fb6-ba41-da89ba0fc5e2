{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\base\\workflow\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\base\\workflow\\index.vue", "mtime": 1754364328606}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAo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file": "index.vue", "sourceRoot": "src/view/module/base/workflow", "sourcesContent": ["<template>\r\n  <div class=\"workflow-list\">\r\n    <Card>\r\n      <!-- 搜索区域 -->\r\n      <Form ref=\"searchForm\" :model=\"searchForm\" inline class=\"search-form\">\r\n        <FormItem>\r\n          <Input\r\n            v-model=\"searchForm.workflowName\"\r\n            placeholder=\"请输入工作流名称\"\r\n            style=\"width: 200px\"\r\n            @on-enter=\"handleSearch\"\r\n          />\r\n        </FormItem>\r\n        <FormItem>\r\n          <Select\r\n            v-model=\"searchForm.execute\"\r\n            placeholder=\"请选择状态\"\r\n            style=\"width: 120px\"\r\n            clearable\r\n          >\r\n            <Option :value=\"1\">启用</Option>\r\n            <Option :value=\"0\">停用</Option>\r\n          </Select>\r\n        </FormItem>\r\n        <FormItem>\r\n          <Button type=\"primary\" @click=\"handleSearch\">查询</Button>\r\n          <Button @click=\"handleReset\" style=\"margin-left: 8px\">重置</Button>\r\n        </FormItem>\r\n      </Form>\r\n\r\n      <!-- 操作按钮 -->\r\n      <div class=\"action-buttons\">\r\n        <Button type=\"primary\" icon=\"md-add\" @click=\"handleAdd\">新增流程</Button>\r\n        <div class=\"right-buttons\">\r\n          <Button type=\"warning\" icon=\"md-rocket\" @click=\"handleElementManage\" style=\"margin-right: 8px\">上新字段管理</Button>\r\n          <Button type=\"success\" icon=\"md-settings\" @click=\"handleFunctionManage\">流程功能</Button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 数据表格 -->\r\n      <Table\r\n        :columns=\"columns\"\r\n        :data=\"tableData\"\r\n        :loading=\"loading\"\r\n        stripe\r\n        :max-height=\"600\"\r\n      >\r\n        <template v-slot:status=\"{ row }\">\r\n          <Badge v-if=\"row.execute === 1\" status=\"success\" text=\"启用\" />\r\n          <Badge v-else status=\"error\" text=\"停用\" />\r\n        </template>\r\n\r\n        <template v-slot:deployStatus=\"{ row }\">\r\n          <Badge v-if=\"row.deployStatus === 1\" status=\"success\" text=\"已部署\" />\r\n          <Badge v-else status=\"error\" text=\"未部署\" />\r\n        </template>\r\n\r\n        <template v-slot:nodeCount=\"{ row }\">\r\n          <Tag color=\"blue\">{{ row.nodeCount || 0 }}个节点</Tag>\r\n        </template>\r\n\r\n        <template v-slot:action=\"{ row }\">\r\n          <Button v-if=\"row.deployStatus === 0\" type=\"error\" size=\"small\" @click=\"handleDeploy(row)\" style=\"margin-right: 4px;\">部署</Button>\r\n          <Button type=\"primary\" size=\"small\" @click=\"handleEdit(row)\" style=\"margin-right: 4px;\">编辑</Button>\r\n<!--          <Button-->\r\n<!--            :type=\"row.execute === 1 ? 'warning' : 'success'\"-->\r\n<!--            size=\"small\"-->\r\n<!--            @click=\"handleToggleStatus(row)\"-->\r\n<!--            style=\"margin-right: 4px;\"-->\r\n<!--          >-->\r\n<!--            {{ row.execute === 1 ? '停用' : '启用' }}-->\r\n<!--          </Button>-->\r\n<!--          <Button type=\"error\" size=\"small\" @click=\"handleDelete(row)\">删除</Button>-->\r\n        </template>\r\n      </Table>\r\n\r\n      <!-- 分页 -->\r\n      <div class=\"pagination-wrapper\">\r\n        <Page\r\n          :total=\"pageInfo.total\"\r\n          :current=\"pageInfo.page\"\r\n          :page-size=\"pageInfo.limit\"\r\n          show-elevator\r\n          show-sizer\r\n          show-total\r\n          @on-change=\"handlePageChange\"\r\n          @on-page-size-change=\"handlePageSizeChange\"\r\n        />\r\n      </div>\r\n    </Card>\r\n\r\n    <!-- 上新字段管理弹窗 -->\r\n    <Modal\r\n      v-model=\"elementManageModal\"\r\n      title=\"上新字段管理\"\r\n      width=\"800\"\r\n      :mask-closable=\"false\"\r\n      @on-cancel=\"handleElementModalCancel\"\r\n    >\r\n      <div class=\"element-manage-content\">\r\n        <!-- 分类标签 -->\r\n        <Tabs v-model=\"activeElementType\" @on-click=\"handleElementTypeChange\">\r\n          <TabPane label=\"基础信息字段\" name=\"1\">\r\n            <div class=\"element-list\">\r\n              <!-- 添加按钮 -->\r\n              <div class=\"add-element-btn\">\r\n                <Button type=\"dashed\" icon=\"md-add\" @click=\"handleAddElement\" long>\r\n                  添加基础信息字段\r\n                </Button>\r\n              </div>\r\n              <!-- 字段列表 -->\r\n              <Table\r\n                :columns=\"elementColumns\"\r\n                :data=\"basicElementList\"\r\n                :loading=\"false\"\r\n                size=\"small\"\r\n                stripe\r\n              ></Table>\r\n            </div>\r\n          </TabPane>\r\n          <TabPane label=\"标准信息字段\" name=\"2\">\r\n            <div class=\"element-list\">\r\n              <!-- 添加按钮 -->\r\n              <div class=\"add-element-btn\">\r\n                <Button type=\"dashed\" icon=\"md-add\" @click=\"handleAddElement\" long>\r\n                  添加标准信息字段\r\n                </Button>\r\n              </div>\r\n              <!-- 字段列表 -->\r\n              <Table\r\n                :columns=\"elementColumns\"\r\n                :data=\"standardElementList\"\r\n                :loading=\"false\"\r\n                size=\"small\"\r\n                stripe\r\n              ></Table>\r\n            </div>\r\n          </TabPane>\r\n        </Tabs>\r\n      </div>\r\n      <div slot=\"footer\">\r\n        <Button @click=\"handleElementModalCancel\">关闭</Button>\r\n      </div>\r\n    </Modal>\r\n\r\n    <!-- 添加/编辑字段弹窗 -->\r\n    <Modal\r\n      v-model=\"elementFormModal\"\r\n      :title=\"elementFormTitle\"\r\n      width=\"500\"\r\n      :mask-closable=\"false\"\r\n      @on-cancel=\"handleElementFormCancel\"\r\n    >\r\n      <Form ref=\"elementForm\" :model=\"elementForm\" :rules=\"elementFormRules\" :label-width=\"100\">\r\n        <FormItem label=\"字段名称\" prop=\"name\">\r\n          <Input v-model=\"elementForm.name\" placeholder=\"请输入字段名称\" />\r\n        </FormItem>\r\n        <FormItem label=\"字段英文\" prop=\"element\">\r\n          <Input v-model=\"elementForm.element\" placeholder=\"请输入字段英文标识\" />\r\n        </FormItem>\r\n        <FormItem label=\"字段类型\" prop=\"type\">\r\n          <RadioGroup v-model=\"elementForm.type\">\r\n            <Radio :label=\"1\">基础信息</Radio>\r\n            <Radio :label=\"2\">标准信息</Radio>\r\n          </RadioGroup>\r\n        </FormItem>\r\n      </Form>\r\n      <div slot=\"footer\">\r\n        <Button @click=\"handleElementFormCancel\">取消</Button>\r\n        <Button type=\"primary\" :loading=\"elementFormLoading\" @click=\"handleElementFormSubmit\">\r\n          {{ elementForm.id ? '更新' : '添加' }}\r\n        </Button>\r\n      </div>\r\n    </Modal>\r\n\r\n    <!-- 流程功能管理弹窗 -->\r\n    <Modal v-model=\"functionModalVisible\" title=\"流程功能管理\" width=\"80%\" :mask-closable=\"false\">\r\n      <div class=\"function-manage\">\r\n        <!-- 功能操作按钮 -->\r\n        <div class=\"function-actions\">\r\n          <Button type=\"primary\" icon=\"md-add\" @click=\"handleFunctionAdd\" style=\"margin-right: 4px;\">新增功能</Button>\r\n          <Button icon=\"md-refresh\" @click=\"loadFunctionData\">刷新</Button>\r\n        </div>\r\n\r\n        <!-- 功能列表表格 -->\r\n        <Table\r\n          :columns=\"functionColumns\"\r\n          :data=\"functionData\"\r\n          :loading=\"functionLoading\"\r\n          stripe\r\n          :max-height=\"400\"\r\n          style=\"margin-top: 16px;\"\r\n        >\r\n          <template v-slot:status=\"{ row }\">\r\n            <Badge v-if=\"row.status === 0\" status=\"success\" text=\"正常\" />\r\n            <Badge v-else status=\"error\" text=\"删除\" />\r\n          </template>\r\n\r\n          <template v-slot:action=\"{ row }\">\r\n            <Button type=\"primary\" size=\"small\" @click=\"handleFunctionEdit(row)\" style=\"margin-right: 4px;\">编辑</Button>\r\n            <Button type=\"error\" size=\"small\" @click=\"handleFunctionDelete(row)\">删除</Button>\r\n          </template>\r\n        </Table>\r\n      </div>\r\n\r\n      <div slot=\"footer\">\r\n        <Button @click=\"functionModalVisible = false\">关闭</Button>\r\n      </div>\r\n    </Modal>\r\n\r\n    <!-- 功能编辑弹窗 -->\r\n    <Modal v-model=\"functionEditModalVisible\" :title=\"functionEditMode === 'add' ? '新增功能' : '编辑功能'\" @on-ok=\"handleFunctionSave\">\r\n      <Form :model=\"functionForm\" :rules=\"functionRules\" ref=\"functionForm\" :label-width=\"80\">\r\n        <FormItem label=\"功能KEY\" prop=\"key\">\r\n          <Input v-model=\"functionForm.key\" placeholder=\"请输入功能KEY\" :disabled=\"functionEditMode === 'edit'\" />\r\n        </FormItem>\r\n        <FormItem label=\"功能名称\" prop=\"name\">\r\n          <Input v-model=\"functionForm.name\" placeholder=\"请输入功能名称\" />\r\n        </FormItem>\r\n      </Form>\r\n    </Modal>\r\n\r\n    <!-- BPMN XML查看弹窗 -->\r\n    <Modal v-model=\"bpmnViewModalVisible\" title=\"BPMN XML内容\" width=\"80%\" :mask-closable=\"false\">\r\n      <div class=\"bpmn-xml-content\">\r\n        <div class=\"xml-header\">\r\n          <span class=\"workflow-name\">{{ currentBpmnWorkflow.workflowName }}</span>\r\n          <Button type=\"primary\" size=\"small\" @click=\"copyBpmnXml\" style=\"float: right;\">\r\n            <Icon type=\"md-copy\" />\r\n            复制XML\r\n          </Button>\r\n        </div>\r\n        <div class=\"xml-viewer\">\r\n          <pre><code>{{ currentBpmnXml }}</code></pre>\r\n        </div>\r\n      </div>\r\n      <div slot=\"footer\">\r\n        <Button @click=\"bpmnViewModalVisible = false\">关闭</Button>\r\n      </div>\r\n    </Modal>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport workflowApi from '@/api/base/workflow'\r\nimport funElementApi from '@/api/base/funElement'\r\n\r\nexport default {\r\n  name: 'WorkflowList',\r\n  data() {\r\n    return {\r\n      // 搜索表单\r\n      searchForm: {\r\n        workflowName: '',\r\n        execute: null\r\n      },\r\n\r\n      // 表格列定义\r\n      columns: [\r\n        {\r\n          title: '序号',\r\n          type: 'index',\r\n          width: 50,\r\n          align: 'center'\r\n        },\r\n        {\r\n          title: '工作流Key',\r\n          key: 'workflowKey',\r\n          width: 150,\r\n          align: 'center'\r\n        },\r\n        {\r\n          title: '工作流名称',\r\n          key: 'workflowName',\r\n          minWidth: 150\r\n        },\r\n        {\r\n          title: 'BPMN文件',\r\n          key: 'bpmnXml',\r\n          width: 120,\r\n          align: 'center',\r\n          render: (h, params) => {\r\n            if (params.row.bpmnXml) {\r\n              return h('Button', {\r\n                props: {\r\n                  type: 'text',\r\n                  size: 'small'\r\n                },\r\n                on: {\r\n                  click: () => {\r\n                    this.handleViewBpmn(params.row)\r\n                  }\r\n                }\r\n              }, '查看文件')\r\n            } else {\r\n              return h('span', '无文件')\r\n            }\r\n          }\r\n        },\r\n        {\r\n          title: '状态',\r\n          key: 'execute',\r\n          width: 80,\r\n          align: 'center',\r\n          slot: 'status'\r\n        },\r\n        {\r\n          title: '部署状态',\r\n          key: 'deployStatus',\r\n          width: 150,\r\n          align: 'center',\r\n          slot: 'deployStatus'\r\n        },\r\n        // {\r\n        //   title: '创建人',\r\n        //   key: 'createUserName',\r\n        //   width: 140,\r\n        //   align: 'center'\r\n        // },\r\n        // {\r\n        //   title: '更新人',\r\n        //   key: 'updateUserName',\r\n        //   width: 140,\r\n        //   align: 'center'\r\n        // },\r\n        {\r\n          title: '创建时间',\r\n          key: 'createTime',\r\n          width: 160,\r\n          align: 'center'\r\n        },\r\n        {\r\n          title: '更新时间',\r\n          key: 'updateTime',\r\n          width: 160,\r\n          align: 'center'\r\n        },\r\n        {\r\n          title: '操作',\r\n          key: 'action',\r\n          width: 280,\r\n          align: 'center',\r\n          slot: 'action'\r\n        }\r\n      ],\r\n\r\n      // 表格数据\r\n      tableData: [],\r\n\r\n      // 加载状态\r\n      loading: false,\r\n\r\n      // 分页信息\r\n      pageInfo: {\r\n        page: 1,\r\n        limit: 10,\r\n        total: 0\r\n      },\r\n      currentWorkflow: {},\r\n\r\n      // BPMN查看相关\r\n      bpmnViewModalVisible: false,\r\n      currentBpmnWorkflow: {},\r\n      currentBpmnXml: '',\r\n\r\n      // 上新字段管理相关数据\r\n      elementManageModal: false,\r\n      activeElementType: '1', // 1: 基础信息, 2: 标准信息\r\n      basicElementList: [],\r\n      standardElementList: [],\r\n      elementFormModal: false,\r\n      elementFormLoading: false,\r\n      elementForm: {\r\n        id: null,\r\n        name: '',\r\n        element: '',\r\n        type: 1,\r\n        funType: 10001 // 上新功能类型枚举\r\n      },\r\n      // 表格列定义\r\n      elementColumns: [\r\n        {\r\n          title: '序号',\r\n          type: 'index',\r\n          width: 60,\r\n          align: 'center'\r\n        },\r\n        {\r\n          title: '字段名称',\r\n          key: 'name',\r\n          minWidth: 120\r\n        },\r\n        {\r\n          title: '字段英文',\r\n          key: 'element',\r\n          minWidth: 150,\r\n          render: (h, params) => {\r\n            return h('code', {\r\n              style: {\r\n                background: '#f5f5f5',\r\n                padding: '2px 6px',\r\n                borderRadius: '3px',\r\n                fontSize: '12px',\r\n                fontFamily: 'Courier New, monospace'\r\n              }\r\n            }, params.row.element)\r\n          }\r\n        },\r\n        {\r\n          title: '创建时间',\r\n          key: 'createTime',\r\n          width: 150,\r\n          render: (h, params) => {\r\n            return h('span', params.row.createTime ? params.row.createTime : '-')\r\n          }\r\n        },\r\n        {\r\n          title: '操作',\r\n          key: 'action',\r\n          width: 200,\r\n          align: 'center',\r\n          render: (h, params) => {\r\n            return h('div', [\r\n              h('Button', {\r\n                props: {\r\n                  type: 'primary',\r\n                  size: 'small'\r\n                },\r\n                style: {\r\n                  marginRight: '5px'\r\n                },\r\n                on: {\r\n                  click: () => {\r\n                    this.handleEditElement(params.row)\r\n                  }\r\n                }\r\n              }, [\r\n                '编辑'\r\n              ]),\r\n              h('Button', {\r\n                props: {\r\n                  type: 'error',\r\n                  size: 'small'\r\n                },\r\n                on: {\r\n                  click: () => {\r\n                    this.handleDeleteElement(params.row)\r\n                  }\r\n                }\r\n              }, [\r\n                '删除'\r\n              ])\r\n            ])\r\n          }\r\n        }\r\n      ],\r\n      elementFormRules: {\r\n        name: [\r\n          { required: true, message: '请输入字段名称', trigger: 'blur' }\r\n        ],\r\n        element: [\r\n          { required: true, message: '请输入字段英文标识', trigger: 'blur' },\r\n          { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '字段英文必须以字母开头，只能包含字母、数字和下划线', trigger: 'blur' }\r\n        ],\r\n        type: [\r\n          { required: true, type: 'number', message: '请选择字段类型', trigger: 'change' }\r\n        ]\r\n      },\r\n\r\n      // 流程功能管理相关\r\n      functionModalVisible: false,\r\n      functionEditModalVisible: false,\r\n      functionEditMode: 'add', // add | edit\r\n      functionLoading: false,\r\n      functionData: [],\r\n      functionForm: {\r\n        id: null,\r\n        key: '',\r\n        name: ''\r\n      },\r\n      functionRules: {\r\n        key: [\r\n          { required: true, message: '请输入功能KEY', trigger: 'blur' },\r\n          { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '功能KEY必须以字母开头，只能包含字母、数字和下划线', trigger: 'blur' }\r\n        ],\r\n        name: [\r\n          { required: true, message: '请输入功能名称', trigger: 'blur' }\r\n        ]\r\n      },\r\n      functionColumns: [\r\n        {\r\n          title: '序号',\r\n          type: 'index',\r\n          width: 60,\r\n          align: 'center'\r\n        },\r\n        {\r\n          title: '功能KEY',\r\n          key: 'key',\r\n          minWidth: 150\r\n        },\r\n        {\r\n          title: '功能名称',\r\n          key: 'name',\r\n          minWidth: 150\r\n        },\r\n        // {\r\n        //   title: '创建人',\r\n        //   key: 'createUserName',\r\n        //   width: 140,\r\n        //   align: 'center'\r\n        // },\r\n        // {\r\n        //   title: '更新人',\r\n        //   key: 'updateUserName',\r\n        //   width: 140,\r\n        //   align: 'center'\r\n        // },\r\n        {\r\n          title: '创建时间',\r\n          key: 'createTime',\r\n          width: 160,\r\n          align: 'center'\r\n        },\r\n        {\r\n          title: '更新时间',\r\n          key: 'updateTime',\r\n          width: 160,\r\n          align: 'center'\r\n        },\r\n        {\r\n          title: '操作',\r\n          key: 'action',\r\n          width: 150,\r\n          align: 'center',\r\n          slot: 'action'\r\n        }\r\n      ]\r\n    }\r\n  },\r\n\r\n  computed: {\r\n    // 表单标题\r\n    elementFormTitle() {\r\n      return this.elementForm.id ? '编辑字段' : '添加字段'\r\n    }\r\n  },\r\n\r\n  mounted() {\r\n    this.loadTableData()\r\n  },\r\n\r\n  methods: {\r\n    // 加载表格数据\r\n    async loadTableData() {\r\n      this.loading = true\r\n      try {\r\n        const params = {\r\n          page: this.pageInfo.page,\r\n          size: this.pageInfo.limit,\r\n          workflowName: this.searchForm.workflowName,\r\n          execute: this.searchForm.execute\r\n        }\r\n\r\n        const response = await workflowApi.getWorkflowPage(params)\r\n        if (response.code === 0) {\r\n          this.tableData = response.data.records || []\r\n          this.pageInfo.total = Number(response.data.total) || 0\r\n        } else {\r\n          this.$Message.error(response.message || '获取工作流列表失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取工作流列表失败:', error)\r\n        this.$Message.error('获取工作流列表失败')\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    // 搜索\r\n    handleSearch() {\r\n      console.log('搜索条件:', this.searchForm)\r\n      this.pageInfo.page = 1\r\n      this.loadTableData()\r\n    },\r\n\r\n    // 重置搜索\r\n    handleReset() {\r\n      this.searchForm = {\r\n        workflowName: '',\r\n        execute: null\r\n      }\r\n      this.handleSearch()\r\n    },\r\n\r\n    // 刷新\r\n    handleRefresh() {\r\n      this.loadTableData()\r\n    },\r\n\r\n    // 新增流程\r\n    handleAdd() {\r\n      this.$router.push('/base/workflow/add')\r\n    },\r\n\r\n    // 查看BPMN文件\r\n    handleViewBpmn(row) {\r\n      if (row.bpmnXml) {\r\n        this.currentBpmnWorkflow = row\r\n        this.currentBpmnXml = row.bpmnXml\r\n        this.bpmnViewModalVisible = true\r\n      } else {\r\n        this.$Message.warning('该工作流暂无BPMN文件')\r\n      }\r\n    },\r\n\r\n    // 复制BPMN XML内容\r\n    copyBpmnXml() {\r\n      if (navigator.clipboard && navigator.clipboard.writeText) {\r\n        navigator.clipboard.writeText(this.currentBpmnXml).then(() => {\r\n          this.$Message.success('BPMN XML已复制到剪贴板')\r\n        }).catch(() => {\r\n          this.$Message.error('复制失败，请手动复制')\r\n        })\r\n      } else {\r\n        // 兼容旧浏览器\r\n        const textArea = document.createElement('textarea')\r\n        textArea.value = this.currentBpmnXml\r\n        document.body.appendChild(textArea)\r\n        textArea.select()\r\n        try {\r\n          document.execCommand('copy')\r\n          this.$Message.success('BPMN XML已复制到剪贴板')\r\n        } catch (err) {\r\n          this.$Message.error('复制失败，请手动复制')\r\n        }\r\n        document.body.removeChild(textArea)\r\n      }\r\n    },\r\n\r\n    // 编辑工作流定义\r\n    handleEdit(row) {\r\n      this.$router.push(`/base/workflow/add/${row.id}`)\r\n    },\r\n\r\n    // 删除工作流定义\r\n    handleDelete(row) {\r\n      this.$Modal.confirm({\r\n        title: '确认删除',\r\n        content: `确定要删除\"${row.workflowName}\"工作流吗？删除后不可恢复。`,\r\n        onOk: async () => {\r\n          try {\r\n            const response = await workflowApi.deleteWorkflow(row)\r\n            if (response.code === 0) {\r\n              this.$Message.success(response.message || '工作流删除成功')\r\n              // 重新加载数据\r\n              this.loadTableData()\r\n            } else {\r\n              this.$Message.error(response.message || '删除失败')\r\n            }\r\n          } catch (error) {\r\n            console.error('删除工作流失败:', error)\r\n            this.$Message.error('删除失败，请重试')\r\n          }\r\n        }\r\n      })\r\n    },\r\n\r\n    // 切换启用/停用状态\r\n    async handleToggleStatus(row) {\r\n      try {\r\n        const newStatus = row.execute === 1 ? 0 : 1\r\n        const statusText = newStatus === 1 ? '启用' : '停用'\r\n\r\n        const updateData = {\r\n          id: row.id,\r\n          execute: newStatus\r\n        }\r\n\r\n        const response = await workflowApi.updateWorkflow(updateData)\r\n        if (response.code === 0) {\r\n          this.$Message.success(`${statusText}成功`)\r\n          // 更新本地数据\r\n          row.execute = newStatus\r\n        } else {\r\n          this.$Message.error(response.message || `${statusText}失败`)\r\n        }\r\n      } catch (error) {\r\n        console.error('切换状态失败:', error)\r\n        this.$Message.error('操作失败，请重试')\r\n      }\r\n    },\r\n\r\n    // ========== 上新字段管理相关方法 ==========\r\n\r\n    // 上新字段管理\r\n    handleElementManage() {\r\n      this.elementManageModal = true\r\n      this.activeElementType = '1'\r\n      this.loadElementData()\r\n    },\r\n\r\n    // 加载字段数据\r\n    async loadElementData() {\r\n      try {\r\n        const response = await funElementApi.getList({\r\n          funType: 10001 // 上新功能类型\r\n        })\r\n\r\n        if (response.code === 0) {\r\n          const allElements = response.data || []\r\n          // 按类型分组\r\n          this.basicElementList = allElements.filter(item => item.type === 1)\r\n          this.standardElementList = allElements.filter(item => item.type === 2)\r\n        } else {\r\n          this.$Message.error(response.message || '加载字段数据失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('加载字段数据失败:', error)\r\n        this.$Message.error('加载字段数据失败')\r\n      }\r\n    },\r\n\r\n    // 切换字段类型\r\n    handleElementTypeChange(name) {\r\n      this.activeElementType = name\r\n    },\r\n\r\n    // 添加字段\r\n    handleAddElement() {\r\n      this.elementForm = {\r\n        id: null,\r\n        name: '',\r\n        element: '',\r\n        type: parseInt(this.activeElementType),\r\n        funType: 10001\r\n      }\r\n      this.elementFormModal = true\r\n\r\n      // 清除表单验证状态\r\n      this.$nextTick(() => {\r\n        if (this.$refs.elementForm) {\r\n          this.$refs.elementForm.clearValidate()\r\n        }\r\n      })\r\n    },\r\n\r\n    // 编辑字段\r\n    handleEditElement(item) {\r\n      this.elementForm = {\r\n        id: item.id,\r\n        name: item.name,\r\n        element: item.element,\r\n        type: item.type,\r\n        funType: item.funType\r\n      }\r\n      this.elementFormModal = true\r\n\r\n      // 清除表单验证状态\r\n      this.$nextTick(() => {\r\n        if (this.$refs.elementForm) {\r\n          this.$refs.elementForm.clearValidate()\r\n        }\r\n      })\r\n    },\r\n\r\n    // 删除字段\r\n    handleDeleteElement(item) {\r\n      this.$Modal.confirm({\r\n        title: '确认删除',\r\n        content: `确定要删除字段\"${item.name}\"吗？`,\r\n        onOk: async () => {\r\n          try {\r\n            const response = await funElementApi.delete(item.id)\r\n            if (response.code === 0) {\r\n              this.$Message.success('删除成功')\r\n              this.loadElementData()\r\n            } else {\r\n              this.$Message.error(response.message || '删除失败')\r\n            }\r\n          } catch (error) {\r\n            console.error('删除字段失败:', error)\r\n            this.$Message.error('删除失败')\r\n          }\r\n        }\r\n      })\r\n    },\r\n\r\n    // 关闭字段管理弹窗\r\n    handleElementModalCancel() {\r\n      this.elementManageModal = false\r\n    },\r\n\r\n    // 提交字段表单\r\n    async handleElementFormSubmit() {\r\n      console.log('提交表单，当前表单数据:', this.elementForm)\r\n      this.$refs.elementForm.validate(async (valid) => {\r\n        console.log('表单验证结果:', valid)\r\n        if (valid) {\r\n          this.elementFormLoading = true\r\n          try {\r\n            const isEdit = !!this.elementForm.id\r\n            let response\r\n\r\n            if (isEdit) {\r\n              response = await funElementApi.update(this.elementForm.id, this.elementForm)\r\n            } else {\r\n              response = await funElementApi.create(this.elementForm)\r\n            }\r\n\r\n            if (response.code === 0) {\r\n              this.$Message.success(isEdit ? '更新成功' : '添加成功')\r\n              this.elementFormModal = false\r\n              this.loadElementData()\r\n            } else {\r\n              this.$Message.error(response.message || '操作失败')\r\n            }\r\n          } catch (error) {\r\n            console.error('保存字段失败:', error)\r\n            this.$Message.error('操作失败')\r\n          } finally {\r\n            this.elementFormLoading = false\r\n          }\r\n        }\r\n      })\r\n    },\r\n\r\n    // 取消字段表单\r\n    handleElementFormCancel() {\r\n      this.elementFormModal = false\r\n      // 重置表单数据和验证状态\r\n      this.$nextTick(() => {\r\n        if (this.$refs.elementForm) {\r\n          this.$refs.elementForm.resetFields()\r\n        }\r\n      })\r\n    },\r\n\r\n    // 工作流部署按钮\r\n    handleDeploy(row) {\r\n      if (!row.bpmnXml) {\r\n        this.$Message.warning('该工作流没有BPMN文件，无法部署')\r\n        return\r\n      }\r\n      this.$Modal.confirm({\r\n        title: '部署确认',\r\n        content: `确定要部署 \"${row.workflowName}\" 到工作流引擎吗？`,\r\n        onOk: async () => {\r\n          try {\r\n            this.$Message.loading('正在部署工作流...')\r\n            const response = await workflowApi.erpDeployWorkflow(row)\r\n\r\n            if (response.code === 0) {\r\n              this.$Message.success('工作流部署成功！')\r\n              console.log('部署结果:', response.data)\r\n\r\n              // 可以在这里更新表格数据，标记为已部署\r\n              this.loadTableData()\r\n            } else {\r\n              this.$Message.error(response.message || '工作流部署失败')\r\n            }\r\n          } catch (error) {\r\n            console.error('工作流部署失败:', error)\r\n            this.$Message.error('工作流部署失败: ' + (error.message || '未知错误'))\r\n          }\r\n        }\r\n      })\r\n    },\r\n\r\n    // 分页变更\r\n    handlePageChange(page) {\r\n      this.pageInfo.page = Number(page)\r\n      this.loadTableData()\r\n    },\r\n\r\n    // 页面大小变更\r\n    handlePageSizeChange(pageSize) {\r\n      this.pageInfo.limit = Number(pageSize)\r\n      this.pageInfo.page = 1\r\n      this.loadTableData()\r\n    },\r\n\r\n    // ========== 流程功能管理相关方法 ==========\r\n\r\n    // 打开流程功能管理弹窗\r\n    handleFunctionManage() {\r\n      this.functionModalVisible = true\r\n      this.loadFunctionData()\r\n    },\r\n\r\n    // 加载功能数据\r\n    async loadFunctionData() {\r\n      this.functionLoading = true\r\n\r\n      try {\r\n        const response = await workflowApi.getFunctionTypeList()\r\n        if (response.code === 0) {\r\n          this.functionData = response.data.records || []\r\n        } else {\r\n          this.$Message.error(response.message || '获取功能类型列表失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取功能类型列表失败:', error)\r\n        this.$Message.error('获取功能类型列表失败')\r\n      } finally {\r\n        this.functionLoading = false\r\n      }\r\n    },\r\n\r\n    // 新增功能\r\n    handleFunctionAdd() {\r\n      this.functionEditMode = 'add'\r\n      this.functionForm = {\r\n        id: null,\r\n        key: '',\r\n        name: ''\r\n      }\r\n      this.functionEditModalVisible = true\r\n      this.$nextTick(() => {\r\n        this.$refs.functionForm.resetFields()\r\n      })\r\n    },\r\n\r\n    // 编辑功能\r\n    handleFunctionEdit(row) {\r\n      this.functionEditMode = 'edit'\r\n      this.functionForm = {\r\n        id: row.id,\r\n        key: row.key,\r\n        name: row.name\r\n      }\r\n      this.functionEditModalVisible = true\r\n    },\r\n\r\n    // 保存功能\r\n    handleFunctionSave() {\r\n      this.$refs.functionForm.validate(async (valid) => {\r\n        if (valid) {\r\n          try {\r\n            let response\r\n            if (this.functionEditMode === 'add') {\r\n              // 新增功能\r\n              response = await workflowApi.addFunctionType(this.functionForm)\r\n            } else {\r\n              // 编辑功能\r\n              response = await workflowApi.updateFunctionType(this.functionForm)\r\n            }\r\n\r\n            if (response.code === 0) {\r\n              this.$Message.success(response.message || (this.functionEditMode === 'add' ? '功能新增成功' : '功能编辑成功'))\r\n              this.functionEditModalVisible = false\r\n              // 重新加载数据\r\n              this.loadFunctionData()\r\n            } else {\r\n              this.$Message.error(response.message || '操作失败')\r\n            }\r\n          } catch (error) {\r\n            console.error('保存功能失败:', error)\r\n            this.$Message.error('操作失败，请重试')\r\n          }\r\n        }\r\n      })\r\n    },\r\n\r\n    // 删除功能\r\n    handleFunctionDelete(row) {\r\n      this.$Modal.confirm({\r\n        title: '确认删除',\r\n        content: `确定要删除功能\"${row.name}\"吗？此操作不可恢复。`,\r\n        onOk: async () => {\r\n          try {\r\n            const response = await workflowApi.deleteFunctionType(row.id)\r\n            if (response.code === 0) {\r\n              this.$Message.success(response.message || '功能删除成功')\r\n              // 重新加载数据\r\n              this.loadFunctionData()\r\n            } else {\r\n              this.$Message.error(response.message || '删除失败')\r\n            }\r\n          } catch (error) {\r\n            console.error('删除功能失败:', error)\r\n            this.$Message.error('删除失败，请重试')\r\n          }\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n\r\n\r\n<style lang=\"less\" scoped>\r\n.workflow-list {\r\n  padding: 16px;\r\n\r\n  .search-form {\r\n    margin-bottom: 16px;\r\n\r\n    .ivu-form-item {\r\n      margin-bottom: 0;\r\n      margin-right: 16px;\r\n    }\r\n  }\r\n\r\n  .action-buttons {\r\n    margin-bottom: 16px;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    gap: 8px;\r\n\r\n    .right-buttons {\r\n      display: flex;\r\n      gap: 8px;\r\n    }\r\n  }\r\n\r\n  .pagination-wrapper {\r\n    margin-top: 16px;\r\n    text-align: right;\r\n  }\r\n\r\n  .workflow-view {\r\n    .workflow-info {\r\n      margin-bottom: 16px;\r\n\r\n      .info-item {\r\n        margin-bottom: 12px;\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        .info-label {\r\n          font-weight: 500;\r\n          color: #515a6e;\r\n          min-width: 80px;\r\n        }\r\n\r\n        .info-value {\r\n          color: #17233d;\r\n        }\r\n      }\r\n    }\r\n\r\n    .workflow-preview {\r\n      .preview-canvas {\r\n        position: relative;\r\n        width: 100%;\r\n        height: 400px;\r\n        background: #f8f9fa;\r\n        border: 1px solid #e8eaec;\r\n        border-radius: 4px;\r\n        overflow: hidden;\r\n\r\n        .preview-node {\r\n          position: absolute;\r\n          width: 50px;\r\n          height: 25px;\r\n          background: white;\r\n          border: 1px solid #dcdee2;\r\n          border-radius: 3px;\r\n          display: flex;\r\n          flex-direction: column;\r\n          align-items: center;\r\n          justify-content: center;\r\n          font-size: 10px;\r\n          user-select: none;\r\n\r\n          &.node-start {\r\n            background: #52c41a;\r\n            color: white;\r\n            border-color: #52c41a;\r\n          }\r\n\r\n          &.node-end {\r\n            background: #f5222d;\r\n            color: white;\r\n            border-color: #f5222d;\r\n          }\r\n\r\n          &.node-approval {\r\n            background: #1890ff;\r\n            color: white;\r\n            border-color: #1890ff;\r\n          }\r\n\r\n          &.node-condition {\r\n            background: #fa8c16;\r\n            color: white;\r\n            border-color: #fa8c16;\r\n            transform: rotate(45deg);\r\n\r\n            .node-title {\r\n              transform: rotate(-45deg);\r\n            }\r\n          }\r\n\r\n          &.node-task {\r\n            background: #f0f0f0;\r\n            color: #333;\r\n          }\r\n\r\n          .node-title {\r\n            font-size: 8px;\r\n            font-weight: 500;\r\n            text-align: center;\r\n            line-height: 1;\r\n          }\r\n\r\n          i {\r\n            font-size: 10px;\r\n            margin-bottom: 2px;\r\n          }\r\n        }\r\n\r\n        .preview-connections {\r\n          position: absolute;\r\n          top: 0;\r\n          left: 0;\r\n          width: 100%;\r\n          height: 100%;\r\n          pointer-events: none;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // 流程功能管理样式\r\n  .function-manage {\r\n    .function-actions {\r\n      display: flex;\r\n      gap: 8px;\r\n      margin-bottom: 16px;\r\n    }\r\n  }\r\n}\r\n\r\n// 全局样式覆盖\r\n:deep(.ivu-card-body) {\r\n  padding: 16px;\r\n}\r\n\r\n:deep(.ivu-table-wrapper) {\r\n  border: 1px solid #e8eaec;\r\n}\r\n\r\n// BPMN XML查看弹窗样式\r\n.bpmn-xml-content {\r\n  .xml-header {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    margin-bottom: 16px;\r\n    padding-bottom: 12px;\r\n    border-bottom: 1px solid #e8eaec;\r\n\r\n    .workflow-name {\r\n      font-size: 16px;\r\n      font-weight: 600;\r\n      color: #2d8cf0;\r\n    }\r\n  }\r\n\r\n  .xml-viewer {\r\n    max-height: 500px;\r\n    overflow: auto;\r\n    background: #f8f9fa;\r\n    border: 1px solid #e8eaec;\r\n    border-radius: 4px;\r\n    padding: 16px;\r\n\r\n    pre {\r\n      margin: 0;\r\n      white-space: pre-wrap;\r\n      word-wrap: break-word;\r\n      font-family: 'Courier New', 'Monaco', 'Menlo', monospace;\r\n      font-size: 12px;\r\n      line-height: 1.5;\r\n      color: #333;\r\n\r\n      code {\r\n        background: none;\r\n        padding: 0;\r\n        border: none;\r\n        font-size: inherit;\r\n        color: inherit;\r\n      }\r\n    }\r\n\r\n    // 滚动条样式\r\n    &::-webkit-scrollbar {\r\n      width: 8px;\r\n      height: 8px;\r\n    }\r\n\r\n    &::-webkit-scrollbar-track {\r\n      background: #f1f1f1;\r\n      border-radius: 4px;\r\n    }\r\n\r\n    &::-webkit-scrollbar-thumb {\r\n      background: #c1c1c1;\r\n      border-radius: 4px;\r\n\r\n      &:hover {\r\n        background: #a8a8a8;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 上新字段管理样式\r\n  .element-manage-content {\r\n    min-height: 400px;\r\n  }\r\n\r\n  .element-list {\r\n    padding: 16px 0;\r\n  }\r\n\r\n  .add-element-btn {\r\n    margin-bottom: 16px;\r\n  }\r\n}\r\n\r\n\r\n</style>\r\n"]}]}