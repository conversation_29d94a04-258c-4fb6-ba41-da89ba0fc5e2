{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\custom\\base\\customClass\\index.vue?vue&type=style&index=0&id=abaf3ab0&lang=less&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\custom\\base\\customClass\\index.vue", "mtime": 1754360258640}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1752744819993}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQoudHJlZURpdiB7DQogIG1heC1oZWlnaHQ6IDQ1MHB4Ow0KICBvdmVyZmxvdy15OiBhdXRvOw0KfQ0KDQouc3VwZXJDbGFzcyB7DQogIC5pdnUtcG9wdGlwLWJvZHkgew0KICAgIHBhZGRpbmc6IDAgMCAwIDVweDsNCiAgfQ0KfQ0KLm1vZGVsQm94ew0KICAuaXZ1LW1vZGFsLWJvZHl7DQogICAgcGFkZGluZzowOw0KICB9DQp9DQouY3VzdG9tQ2xhc3Mgew0KICAuc3BlY2lhbEJhY2tncm91bmQgew0KICAgIGJhY2tncm91bmQ6ICNFMEZGRkY7DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAulBA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/view/module/custom/base/customClass", "sourcesContent": ["<!--\r\n@create date 2020-07-09\r\n@desc 报关类目\r\n-->\r\n<template>\r\n  <div class=\"customClass\">\r\n      <Card :shadow=\"true\">\r\n        <Form ref=\"searchForm\" :model=\"searchForm\" inline @keydown.native.enter.prevent=\"handleSearch\">\r\n          <FormItem prop=\"className\">\r\n            <Input type=\"text\" v-model=\"searchForm.className\" placeholder=\"请输入类目名称\"/>\r\n          </FormItem>\r\n          <FormItem prop=\"categoryName\">\r\n            <Input type=\"text\" v-model=\"searchForm.categoryName\" placeholder=\"请输入产品型号\"/>\r\n          </FormItem>\r\n          <FormItem prop=\"hsCode\">\r\n            <Input type=\"text\" v-model=\"searchForm.hsCode\" placeholder=\"请输入报关海关编码\"/>\r\n          </FormItem>\r\n          <FormItem>\r\n            <Button type=\"primary\" @click=\"handleSearch\" :loading=\"loading\">查询</Button>\r\n            <Button style=\"margin-left:10px\" @click=\"handleReset\">重置</Button>\r\n          </FormItem>\r\n        </Form>\r\n        <div style=\"margin-bottom: 10px\">\r\n          <div style=\"float:left\">\r\n            <Upload ref=\"uploadClassFileRef\" name=\"importFile\" :action=\"importClassURl\" :max-size=\"10240\"\r\n                    :on-success=\"handleImportClassSuccess\" :format=\"['xls', 'xlsx']\" :show-upload-list=\"false\" :on-format-error=\"handleImportFormatError\"\r\n                    :on-error=\"handleImportError\" :headers=\"loginInfo\" :on-exceeded-size=\"handleMaxSize\">\r\n              <Button class=\"search-btn\" type=\"primary\">导入类目</Button>\r\n            </Upload></div>\r\n          <Button class=\"search-btn\" style=\"margin-left:10px;\" @click=\"classAdd\">添加类目</Button>\r\n          <Button class=\"search-btn\" style=\"margin-left:10px;\" @click=\"classExport\">导出类目</Button>\r\n          <div style=\"float:left\">\r\n            <Upload ref=\"uploadClearanceFileRef\" name=\"importFile\" :action=\"importClearanceURl\" :max-size=\"10240\"\r\n                    :on-success=\"handleImportClearanceSuccess\" :format=\"['xls', 'xlsx']\" :show-upload-list=\"false\" :on-format-error=\"handleImportFormatError\"\r\n                    :on-error=\"handleImportError\" :headers=\"loginInfo\" :on-exceeded-size=\"handleMaxSize\">\r\n              <Button class=\"search-btn\" style=\"margin-left:10px;\">导入清关资料</Button>\r\n            </Upload></div>\r\n          <Button class=\"search-btn\" style=\"margin-left:10px;\" @click=\"clearanceExport\">导出清关资料</Button>\r\n        </div>\r\n        <tree-table ref=\"treeTableRef\" expand-key=\"className\" :expand-type=\"false\" :selectable=\"false\" :columns=\"column\" :data=\"data\"\r\n                    @radio-click=\"loadClassChild\" @clickRow=\"loadClassChild\" :border=\"true\" :row-class-name=\"rowClassName\">\r\n          <template v-slot:action=\"scope\">\r\n            <Button size=\"small\" type=\"info\" @click=\"classLook(scope)\" style=\"margin:0 2px\">查看</Button>\r\n            <Button size=\"small\" type=\"info\" @click=\"classEdit(scope)\" v-if=\"scope.row.parentId>0\" style=\"margin:0 2px\">编辑</Button>\r\n            <Button size=\"small\" type=\"info\" @click=\"classRemove(scope)\" style=\"margin:0 2px\">删除</Button>\r\n            <Button size=\"small\" type=\"info\" @click=\"declareEdit(scope)\" v-if=\"scope.row.parentId>0\" style=\"margin:0 2px\">申报要素</Button>\r\n            <Button size=\"small\" type=\"info\" @click=\"clearanceEdit(scope)\" style=\"margin:0 2px\">清关资料</Button>\r\n            <Button size=\"small\" type=\"info\" @click=\"lookLog(scope)\" style=\"margin:0 2px\">日志</Button>\r\n          </template>\r\n          <template v-slot:clearanceElement=\"scope\">\r\n            <Button size=\"small\" type=\"info\" @click=\"getClearanceElement(scope)\" style=\"margin:0 2px\">清关信息</Button>\r\n          </template>\r\n        </tree-table>\r\n      </Card>\r\n\r\n    <!-- 报关类目 模块 修改新增model -->\r\n    <Modal :width=\"680\" v-model=\"classModelVisible\" :mask-closable=\"false\" :title=\"classTitle\" @on-cancel=\"cancelForm\">\r\n      <Spin size=\"large\" v-if=\"spinShow\" :fix=\"true\">加载中...</Spin>\r\n      <Form ref=\"form\" :model=\"form\" label-position=\"left\" :rules=\"ruleValidate\" :label-width=\"100\" inline>\r\n        <FormItem label=\"上级目录\" prop=\"parentName\">\r\n          <Poptip placement=\"right-start\" width=\"230\" class=\"superClass\" title=\"上级目录\">\r\n            <Input v-model=\"form.parentName\" :readonly=\"true\" placeholder=\"请选择\" style=\"width:200px;\"></Input>\r\n            <div class=\"treeDiv\" slot=\"content\">\r\n              <Tree :data=\"treeData\" @on-select-change=\"selectParent\"></Tree>\r\n            </div>\r\n          </Poptip>\r\n        </FormItem>\r\n        <FormItem label=\"类目名称\" prop=\"className\">\r\n          <Input v-model=\"form.className\" placeholder=\"请输入报关类目名称\" style=\"width:200px;\"></Input>\r\n        </FormItem>\r\n        <FormItem label=\"产品型号\" prop=\"categoryName\">\r\n          <div class=\"setClass\" style=\"width:200px;\">\r\n            <treeselect v-model=\"form.categoryName\"\r\n                        :options=\"spuList\"\r\n                        :defaultExpandLevel=\"1\"\r\n                        :autoLoadRootOptions=\"true\"\r\n                        noResultsText=\"暂无数据\"\r\n                        placeholder=\"请选择产品型号\"/>\r\n          </div>\r\n        </FormItem>\r\n        <FormItem label=\"中文报关名\" prop=\"customNameCn\">\r\n          <Input v-model=\"form.customNameCn\" placeholder=\"中文报关名\" style=\"width:200px;\"></Input>\r\n        </FormItem>\r\n        <FormItem label=\"英文报关名\" prop=\"customNameEn\">\r\n          <Input v-model=\"form.customNameEn\" placeholder=\"英文报关名\" style=\"width:200px;\"></Input>\r\n        </FormItem>\r\n        <FormItem label=\"报关单位\" prop=\"unit\">\r\n          <Input v-model=\"form.unit\" placeholder=\"请输入报关类目名称\" style=\"width:200px;\"></Input>\r\n        </FormItem>\r\n        <FormItem label=\"报关海关编码\" prop=\"hsCode\">\r\n          <Input v-model=\"form.hsCode\" placeholder=\"请输入报关类目名称\" style=\"width:200px;\"></Input>\r\n        </FormItem>\r\n        <FormItem label=\"材质\" prop=\"material\">\r\n          <Input v-model=\"form.material\" placeholder=\"请输入材质\" style=\"width:200px;\"></Input>\r\n        </FormItem>\r\n        <FormItem prop=\"purpose\" label=\"用途\">\r\n          <Input v-model=\"form.purpose\" type=\"textarea\" :autosize=\"{minRows: 1,maxRows: 3}\"\r\n                 placeholder=\"请输入用途\" style=\"width:200px;\"/>\r\n        </FormItem>\r\n      </Form>\r\n      <div slot=\"footer\">\r\n        <Button type=\"default\" @click=\"cancelForm\">取消</Button>&nbsp;\r\n        <Button type=\"primary\" @click=\"handleSubmit()\" :loading=\"saving\">提交</Button>\r\n      </div>\r\n    </Modal>\r\n\r\n    <!-- 申报要素 -->\r\n    <Modal v-model=\"declareModelVisible\" class=\"modelBox\" :title=\"declareTitle\" width=\"600\" @on-cancel=\"cancelDeclare\">\r\n      <Spin :fix=\"true\" v-if=\"spinShow\">加载中...</Spin>\r\n      <Button class=\"search-btn\" type=\"primary\" size=\"small\" style=\"margin-left:15px\" @click=\"addDeclare()\" v-if=\"!disabled\">添加</Button>\r\n      <Table :border=\"true\" :columns=\"declareColumn\" :data=\"declareData\" :loading=\"loading\">\r\n        <template v-slot:decKey=\"{index}\">\r\n          <Select v-model=\"declareData[index].decKey\" transfer placeholder=\"请选择\" :disabled=\"disabled\">\r\n            <Option v-for=\"(item) in declareTypeList\" :value=\"item['value']\" :key=\"item['value']\">{{item['value']}}</Option>\r\n          </Select>\r\n        </template>\r\n        <template v-slot:decContent=\"{index}\">\r\n          <Input v-model=\"declareData[index].content\" type=\"textarea\" :autosize=\"{minRows: 1,maxRows: 4}\" placeholder=\"请输入\" :disabled=\"disabled\"/>\r\n        </template>\r\n        <template v-slot:decAction=\"{index}\">\r\n          <a href=\"javascript:void(0)\" v-if=\"!disabled\" @click=\"delDeclare(index)\">删除</a>\r\n        </template>\r\n      </Table>\r\n      <div slot=\"footer\">\r\n        <Button  type=\"primary\" :loading=\"saving\" @click=\"saveDeclare()\" v-if=\"!disabled\">保存</Button>\r\n        <Button @click=\"cancelDeclare\">取消</Button>\r\n      </div>\r\n    </Modal>\r\n\r\n    <!-- 清关资料 -->\r\n    <Modal v-model=\"clearanceModelVisible\" class=\"modelBox\" :title=\"clearanceTitle\" width=\"900\" @on-cancel=\"cancelClearance\">\r\n      <Spin :fix=\"true\" v-if=\"spinShow\">加载中...</Spin>\r\n      <Button class=\"search-btn\" type=\"primary\" size=\"small\" style=\"margin-left:15px\" @click=\"addClearance()\" v-if=\"!disabled\">添加</Button>\r\n      <Table :border=\"true\" :columns=\"clearanceColumn\" :data=\"clearanceData\" :loading=\"loading\">\r\n        <template v-slot:country=\"{index}\">\r\n          <Select v-model=\"clearanceData[index].country\" transfer placeholder=\"请选择\" :disabled=\"disabled\">\r\n            <Option v-for=\"(item,index) in countryList\" :value=\"item['two_code']\" :key=\"index\">{{ item['name_cn'] }}</Option>\r\n          </Select>\r\n        </template>\r\n        <template v-slot:hsCode=\"{index}\">\r\n          <Input v-model=\"clearanceData[index].hsCode\" type=\"text\" placeholder=\"请输入\" :disabled=\"disabled\"/>\r\n        </template>\r\n        <template v-slot:price=\"{index}\">\r\n          <Input v-model=\"clearanceData[index].price\" type=\"text\" placeholder=\"请输入\" :disabled=\"disabled\"/>\r\n        </template>\r\n        <template v-slot:currency=\"{index}\">\r\n          <Select v-model=\"clearanceData[index].currency\" transfer placeholder=\"请选择\" :disabled=\"disabled\">\r\n            <Option v-for=\"(item) in currencyList\" :value=\"item['id']\" :key=\"item['id']\">{{item['name']}}</Option>\r\n          </Select>\r\n        </template>\r\n        <template v-slot:clearanceAction=\"{index}\">\r\n          <a href=\"javascript:void(0)\" v-if=\"!disabled\" @click=\"delClearance(index)\">删除</a>\r\n        </template>\r\n      </Table>\r\n      <div slot=\"footer\">\r\n        <Button  type=\"primary\" :loading=\"saving\" @click=\"saveClearance()\" v-if=\"!disabled\">保存</Button>\r\n        <Button @click=\"cancelClearance\">取消</Button>\r\n      </div>\r\n    </Modal>\r\n    <!-- 报关类目明细查看 -->\r\n    <CategoryView ref=\"categoryViewRef\" :modelViewVisible=\"classViewVisible\" :onCancel=\"()=>classViewVisible=false\" :allData=\"this.allData\" :currencyList=\"currencyList\" :countryList=\"countryList\"/>\r\n    <!-- 日志模块 -->\r\n    <LogModel ref=\"logModelRef\" :logVisible=\"logVisible\" :onCancel=\"()=>logVisible=false\"/>\r\n  </div>\r\n</template>\r\n<script>\r\nimport CustomClass from \"@/api/custom/customClass\";\r\nimport {listAllSpu} from '@/api/basf/product.js'\r\nimport CommonApi from \"@/api/base/commonApi\";\r\nimport Currency from \"@/api/basf/currency\";\r\nimport CategoryView from \"@/view/module/custom/base/customClass/indexView.vue\";\r\nimport LogModel from \"@/view/module/base/bussinessLog/logModel.vue\";\r\nimport {getToken, getUrl} from \"@/libs/util\";\r\nexport default {\r\n  name: 'category',\r\n  components: {LogModel, CategoryView},\r\n  data() {\r\n    return {\r\n      searchForm: {\r\n        className: '',\r\n        categoryName: '',\r\n        hsCode: '',\r\n      },\r\n      currentId: null,//当前操作的数据ID\r\n      column: [\r\n        {title: '类目名称',key: 'className', minWidth: 180, align: 'left',resizable:true,render:(_,{row})=>(<span v-copytext={row.className}>{row.className}</span>)},\r\n        {title: '产品型号', key: 'categoryName',minWidth: 120, align: 'center',resizable:true,render:(_,{row})=>(<span v-copytext={row.categoryName}>{row.categoryName}</span>)},\r\n        {title: '中文报关名', key: 'customNameCn',minWidth: 120, align: 'center',resizable:true,render:(_,{row})=>(<span v-copytext={row.customNameCn}>{row.customNameCn}</span>)},\r\n        {title: '英文报关名', key: 'customNameEn',minWidth: 120, align: 'center',resizable:true,render:(_,{row})=>(<span v-copytext={row.customNameEn}>{row.customNameEn}</span>)},\r\n        {title: '报关海关编码',key: 'hsCode',minWidth: 120, align: 'center',resizable:true,render:(_,{row})=>(<span v-copytext={row.hsCode}>{row.hsCode}</span>)},\r\n        {title: '材质',key: 'material',minWidth: 120, align: 'center',resizable:true,render:(_,{row})=>(<span v-copytext={row.material}>{row.material}</span>)},\r\n        {title: '用途',key: 'purpose',minWidth: 120, align: 'center',resizable:true,render:(_,{row})=>(<span v-copytext={row.purpose}>{row.purpose}</span>)},\r\n        {title: '报关单位',key: 'unit',minWidth: 120, align: 'center',resizable:true,render:(_,{row})=>(<span v-copytext={row.unit}>{row.unit}</span>)},\r\n        {title: '清关数据',key: 'clearanceElement',width: 120, align: 'center',resizable:true,type: 'template',template: 'clearanceElement'},\r\n        {title: '操作',width: 200, type: 'template',template: 'action'}\r\n      ],\r\n      data: [],\r\n      allData: [],//包含明细类目的list数据格式\r\n      loading: false,\r\n      saving: false,\r\n      clickNode: {},\r\n      // 新增编辑\r\n      classModelVisible: false,\r\n      form: {parentName: null, parentId: null, className: null, customNameCn: null, customNameEn: null, material: null, purpose: null, unit: null, hsCode: null,},\r\n      disabled:false,\r\n      spinShow: false,\r\n      classTitle: '',\r\n      treeData:[],\r\n      ruleValidate: {\r\n        className: [\r\n          {required: true, message: '请输入类目名称', trigger: 'blur'}\r\n        ],\r\n      },\r\n      //查看明细\r\n      classViewVisible:false,\r\n      //申报要素\r\n      declareModelVisible:false,\r\n      declareColumn:[{title: '类型',key: 'decKey', minWidth: 120, align: 'center',slot:'decKey'},\r\n        {title: '内容', key: 'content',minWidth: 120, align: 'center',slot:'decContent'},\r\n        {title: '操作',key: 'action',width: 100, align: 'center',slot:'decAction'}],\r\n      declareData:[],\r\n      declareTitle:'',\r\n\r\n      //清关资料\r\n      clearanceModelVisible:false,\r\n      clearanceColumn:[{title: '国家',key: 'country', minWidth: 120, align: 'center',slot:'country'},\r\n        {title: '清关编码', key: 'hsCode',minWidth: 120, align: 'center',slot:'hsCode'},\r\n        {title: '清关价格', key: 'price',minWidth: 120, align: 'center',slot:'price'},\r\n        {title: '清关币种', key: 'currency',minWidth: 120, align: 'center',slot:'currency'},\r\n        {title: '操作',key: 'action',width: 100, align: 'center',slot:'clearanceAction'}],\r\n      clearanceData:[],\r\n      clearanceTitle:'',\r\n\r\n      logVisible:false,\r\n      //导入类目\r\n      importClassURl: getUrl() + \"/base/customClass/importClassFile\",\r\n      //导入清关资料\r\n      importClearanceURl: getUrl() + \"/base/customClass/importClearanceFile\",\r\n      loginInfo: {\r\n        Accept: 'application/json,text/plain, */*, */*',\r\n        mode: 'cors',\r\n        Authorization: 'Bearer ' + getToken()\r\n      },\r\n      //型号列表\r\n      spuList: [],\r\n      //申报要素类型\r\n      declareTypeList:[],\r\n      //币种\r\n      currencyList:[],\r\n      //国家\r\n      countryList:[],\r\n      //日志类型\r\n      refType:null,\r\n    }\r\n  },\r\n  mounted() {\r\n    this.listAllSpu();//获取产品类目List\r\n    this.listDeclareTypeList();//获取产品类目List\r\n    this.handleCurrency();//获取所有币种\r\n    this.getCountryList();//获取所有国家\r\n    this.getLogRefType();//获取日志类型\r\n    this.handleSearch();//获取所有数据\r\n  },\r\n  methods: {\r\n    //查询，重置\r\n    handleSearch() {\r\n      CustomClass.listTree(this.searchForm).then(res => {\r\n        if (res['code'] === 0) {\r\n          this.data = res.data;\r\n          this.treeData = res.data;//如果有新增的数据,需要重新打开页签才能加载到\r\n          this.allData = [];\r\n          this.setTitle(this.treeData)\r\n          let declareTypeObj = {};\r\n          let index = 0;\r\n          this.declareTypeList.forEach(item=>declareTypeObj[item['value']] = 'dec'+ index++)\r\n          this.setDeclarationElement(this.data,declareTypeObj)\r\n        }\r\n      })\r\n    },\r\n    setTitle(data){\r\n      if(data && data.length>0){\r\n        data.forEach(item => {\r\n          this.allData.push(item);\r\n          item['title'] = item['className'];\r\n          this.setTitle(item['children']);\r\n        })\r\n      }\r\n    },\r\n    setDeclarationElement(dataList,declareTypeObj){\r\n      if(!dataList || dataList.length<=0){\r\n        return;\r\n      }\r\n      dataList.forEach(item=>{\r\n        if(item['declarationElementList'] !=null && item['declarationElementList'].length !==0){\r\n          item['declarationElementList'].forEach(declarationElement=>{\r\n            let decKey = declareTypeObj[declarationElement['decKey']];\r\n            if(decKey !=null){\r\n              item[decKey] = declarationElement['content'];\r\n            }\r\n          })\r\n        }\r\n        this.setDeclarationElement(item['children'],declareTypeObj);\r\n      })\r\n    },\r\n    handleReset() {\r\n      this.$refs['searchForm'].resetFields();\r\n    },\r\n\r\n    loadClassChild(params) {\r\n      this.clickNode = params.row || {};\r\n      this.currentId = params.row.id;\r\n    },\r\n    rowClassName(row) {\r\n      const { clickNode } = this;\r\n      if(clickNode.id === row.id) return 'specialBackground';\r\n    },\r\n    //关联类目\r\n    selectParent(row) {\r\n      this.form.parentName = row[0].className;\r\n      this.form.level = row[0].level + 1;\r\n      this.form.parentId = row[0].id\r\n    },\r\n\r\n    //导入类目数据和清关资料数据\r\n    handleImportClassSuccess(res){\r\n      this.$refs['uploadClassFileRef'].clearFiles();\r\n      this.handleImportSuccess(res);\r\n    },\r\n    handleImportClearanceSuccess(res){\r\n      this.$refs['uploadClearanceFileRef'].clearFiles();\r\n      this.handleImportSuccess(res);\r\n    },\r\n    handleImportSuccess(res) {\r\n      if (res['code'] === 0) {\r\n        this.$Message.success('导入成功');\r\n        this.handleSearch();\r\n      } else {\r\n        this.$Message.warning(res['message']);\r\n      }\r\n    },\r\n    handleImportFormatError(file) {\r\n      //格式验证失败的钩子\r\n      this.$Modal.error({\r\n        title: '文件格式不正确',\r\n        content: '文件 ' + file.name + '格式不正确，支持上传的文件类型：xls,xlsx',\r\n        okText: '确认'\r\n      });\r\n    },\r\n    handleImportError(err, file) {\r\n      this.$Message.warning(file.message);\r\n    },\r\n    handleMaxSize() {\r\n      this.$Message.warning('图片大小不能超过2M.');\r\n    },\r\n    classExport(){\r\n      let params = {...this.searchForm};\r\n      params['fileName']=\"报关类目_\"+new Date().getExportFormat()+\".xls\";\r\n      this.loading=true;\r\n      CustomClass.download(params,()=>{this.loading=false})\r\n    },\r\n    clearanceExport(){\r\n      let params = {...this.searchForm};\r\n      params['fileName']=\"报关类目_\"+new Date().getExportFormat()+\".xls\";\r\n      this.loading=true;\r\n      CustomClass.downloadClearance(params,()=>{this.loading=false})\r\n    },\r\n\r\n    // 新增修改类目\r\n    classAdd() {\r\n      this.classModelVisible = true;\r\n      this.classTitle = \"新增类目\";\r\n      this.handleResetForm();\r\n    },\r\n    classEdit(scope) {\r\n      this.classModelVisible = true;\r\n      this.classTitle = \"编辑类目\";\r\n      this.handleResetForm();\r\n      this.loadModel(scope.row.id,false);\r\n    },\r\n    loadModel(id){\r\n      this.spinShow = true;\r\n      CustomClass.getBy({\"id\": id}).then(res => {\r\n        if (res['code'] === 0) {\r\n          this.form = res.data;\r\n          this.allData.forEach(item => {\r\n            if (item['id'] === this.form.parentId) {\r\n              this.form.parentName = item['className'];\r\n            }\r\n          })\r\n          if(!this.form.categoryName){\r\n            this.form.categoryName=null;\r\n          }\r\n        }\r\n      }).finally(() => {\r\n        this.spinShow = false;\r\n      })\r\n    },\r\n    cancelForm() {\r\n      this.classModelVisible = false;\r\n      this.handleResetForm();\r\n    },\r\n    addDeclare(){\r\n      this.declareData.push({decKey:'',content:''})\r\n    },\r\n    delDeclare(index){\r\n      this.declareData.splice(index,1);\r\n    },\r\n    handleResetForm() {\r\n      this.$refs[\"form\"].resetFields();\r\n      this.form['parentId'] = null;\r\n      this.form['id'] = null;\r\n      this.form['level'] = null;\r\n    },\r\n    handleSubmit() {\r\n      this.$refs['form'].validate((valid) => {\r\n        if (valid) {\r\n          this.saving = true;\r\n          CustomClass.saveCustomClass(this.form).then(res => {\r\n            if (res['code'] === 0) {\r\n              this.handleSearch();\r\n              this.cancelForm();\r\n              this.currentId = null;\r\n              this.$Message.success('保存成功');\r\n            }\r\n          }).finally(() => {\r\n            this.saving = false;\r\n          })\r\n        }\r\n      })\r\n    },\r\n    //删除报关类目\r\n    classRemove(scope) {\r\n      this.$Modal.confirm({\r\n        title: '提示！',\r\n        content: '您确定删除这条数据吗？',\r\n        onOk: () => {\r\n          this.saving = true;\r\n          CustomClass.remove({id: scope.row.id}).then(res => {\r\n            if (res['code'] === 0) {\r\n              this.currentId = null;\r\n              this.$Message.success('删除成功');\r\n              this.handleSearch();\r\n            }\r\n          }).finally(() => {\r\n            this.saving = false\r\n          })\r\n        }\r\n      });\r\n    },\r\n    //查看报关类目\r\n    classLook(scope){\r\n      const { categoryViewRef} = this.$refs;\r\n      if (categoryViewRef) {\r\n        categoryViewRef.setDefault(scope.row.id);\r\n      }\r\n      this.classViewVisible = true;\r\n    },\r\n    //新增/修改/删除申报要素\r\n    declareEdit(scope){\r\n      this.declareTitle = \"维护申报要素类目:\"+scope.row.className;\r\n      this.disabled = false;\r\n      if(this.declareColumn.length <=2){\r\n        this.declareColumn.push({title: '操作',key: 'action',minWidth: 120, align: 'center',slot:'decAction'});\r\n      }\r\n      this.currentId = scope.row.id;\r\n      this.declareModelVisible= true;\r\n      this.loading=true;\r\n      CustomClass.getDeclareElement({\"id\":this.currentId}).then(res=>{\r\n        this.declareData = res['data'];\r\n      }).finally(()=>{this.loading=false;})\r\n    },\r\n    cancelDeclare(){\r\n      this.declareModelVisible = false;\r\n      this.currentId = null;\r\n    },\r\n    saveDeclare(){\r\n      this.declareData.forEach(item=>item['parentId']=this.currentId);\r\n      this.saving=true;\r\n      (this.declareData.length>0?CustomClass.saveDeclareElement(this.declareData):CustomClass.delDeclareElement({\"id\":this.currentId}))\r\n        .then(res=>{\r\n          if (res['code'] === 0) {\r\n            this.handleSearch();\r\n            this.cancelDeclare();\r\n            this.$Message.success('处理成功');\r\n          }\r\n        }).finally(()=>{this.saving=false;})\r\n    },\r\n\r\n    //新增/修改/删除清关资料\r\n    clearanceEdit(scope){\r\n      this.clearanceTitle = \"维护清关信息:\"+scope.row.className;\r\n      this.disabled = false;\r\n      if(this.clearanceColumn.length <=4){\r\n        this.clearanceColumn.push({title: '操作',key: 'action',minWidth: 120, align: 'center',slot:'clearanceAction'});\r\n      }\r\n      this.loadClearanceElement(scope);\r\n    },\r\n    getClearanceElement(scope){\r\n      this.clearanceTitle = \"查看清关信息:\"+scope.row.className;\r\n      this.disabled = true;\r\n      if(this.clearanceColumn.length >=5){\r\n        this.clearanceColumn.splice(4,1);\r\n      }\r\n      this.loadClearanceElement(scope);\r\n    },\r\n    loadClearanceElement(scope){\r\n      this.currentId = scope.row.id;\r\n      this.clearanceModelVisible= true;\r\n      this.loading=true;\r\n      CustomClass.getClearanceElement({\"id\":this.currentId}).then(res=>{\r\n        this.clearanceData = res['data'];\r\n      }).finally(()=>{this.loading=false;})\r\n    },\r\n    cancelClearance(){\r\n      this.clearanceModelVisible = false;\r\n      this.currentId = null;\r\n    },\r\n    addClearance(){\r\n      this.clearanceData.push({})\r\n    },\r\n    delClearance(index){\r\n      this.clearanceData.splice(index,1);\r\n    },\r\n    saveClearance(){\r\n      this.clearanceData.forEach(item=>item['parentId']=this.currentId);\r\n      this.saving=true;\r\n      (this.clearanceData.length>0?CustomClass.saveClearanceElement(this.clearanceData):CustomClass.delClearanceElement({\"id\":this.currentId}))\r\n        .then(res=>{\r\n          if (res['code'] === 0) {\r\n            this.handleSearch();\r\n            this.cancelClearance();\r\n            this.$Message.success('保存成功');\r\n          }\r\n        }).finally(()=>{this.saving=false;})\r\n    },\r\n\r\n    //日志\r\n    lookLog(scope) {\r\n      const {logModelRef} = this.$refs;\r\n      if (logModelRef) {\r\n        logModelRef.setDefault(scope.row.id, this.refType);\r\n      }\r\n      this.logVisible = true;\r\n    },\r\n    getLogRefType() {\r\n      CustomClass.getLogRefType().then(res => {\r\n        if (res['code'] === 0) {\r\n          this.refType = res.data;\r\n        }\r\n      })\r\n    },\r\n    //加载数据\r\n    listAllSpu() {\r\n      listAllSpu({}).then((res) => {\r\n        if (res['code'] === 0) {\r\n          this.spuList = [{\"spu\":\"服饰\",\"spuName\":\"服饰\",children:res.data.filter(item=>item['spuName'].indexOf(\"#\")<0)}];\r\n          this.diGuiTree(this.spuList)\r\n        }\r\n      })\r\n    },\r\n    diGuiTree(item) {  //递归便利树结构\r\n      item.forEach(item => {\r\n        item.id = item['spu'];\r\n        item.label = item['spuName'];\r\n        !item['children'] || item['children'].length === 0 ? delete item.children : this.diGuiTree(item.children);\r\n      })\r\n    },\r\n    listDeclareTypeList(){\r\n      CommonApi.ListDictionaryValueBy(\"declare_element\").then(res=>{\r\n        if(res && res['code'] ===0){\r\n          this.declareTypeList = res['data']||[];\r\n          let index = 0;\r\n          this.declareTypeList.forEach(item=>{\r\n            this.column.splice(-2,0, {title: item['value'],key: 'dec'+index++,minWidth: 120, align: 'center',resizable:true,render:null});\r\n          })\r\n          this.handleSearch();\r\n        }\r\n      })\r\n    },\r\n    handleCurrency() {\r\n      Currency.getAll().then(res => {\r\n        this.currencyList = res.data;\r\n      });\r\n    },\r\n    //获取目的国家\r\n    getCountryList() {\r\n      CommonApi.ListDictionaryValueBy(\"base_country\").then(res=>{\r\n        if(res && res['code'] ===0){\r\n          let data = res['data']\r\n          if(data){\r\n            this.countryList = data.map(item=>JSON.parse(item.value));\r\n          }\r\n        }\r\n      })\r\n    },\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\">\r\n.treeDiv {\r\n  max-height: 450px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.superClass {\r\n  .ivu-poptip-body {\r\n    padding: 0 0 0 5px;\r\n  }\r\n}\r\n.modelBox{\r\n  .ivu-modal-body{\r\n    padding:0;\r\n  }\r\n}\r\n.customClass {\r\n  .specialBackground {\r\n    background: #E0FFFF;\r\n  }\r\n}\r\n</style>\r\n"]}]}