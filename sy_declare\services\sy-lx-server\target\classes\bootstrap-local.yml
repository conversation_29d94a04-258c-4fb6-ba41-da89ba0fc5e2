spring:
  cloud:
    nacos:
      config:
        server-addr: ***********:8848
        namespace: sy_dev
      discovery:
        server-addr: ***********:8848
        namespace: sy_dev
xxl:
  job:
    admin:
      addresses: http://***********:7777/
    executor:
      appname: ${spring.application.name}
      ip:
      port: 9703
      logpath: D:/data/${spring.application.name}/xxl-job/jobhandler
      logretentiondays: -1
    accessToken:
    i18n:

