{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\store\\module\\app.js", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\store\\module\\app.js", "mtime": 1752737748504}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\.babelrc", "mtime": 1752737748316}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["getBreadCrumbList", "setTagNavListInLocalstorage", "getMenuByRouter", "getTagNavListFromLocalstorage", "getHomeRoute", "getNextRoute", "routeHasExist", "routeEqual", "getRouteTitleHandled", "localSave", "localRead", "beforeClose", "publicApi", "router", "routers", "config", "routes", "homeName", "closePage", "state", "route", "nextRoute", "tagNavList", "filter", "item", "push", "breadCrumbList", "homeRoute", "local", "errorList", "hasReadErrorPage", "productID", "productFlag", "isTop", "allSites", "getters", "menuList", "rootState", "user", "access", "errorCount", "length", "getIsTop", "mutations", "setBreadCrumb", "setIsTop", "setTagNavList", "list", "tagList", "_toConsumableArray", "name", "shift", "homeTagIndex", "findIndex", "homeTag", "splice", "unshift", "setProductID", "id", "setproductFlag", "val", "closeTag", "tag", "meta", "beforeCloseName", "Promise", "then", "close", "addTag", "_ref", "_ref$type", "type", "setLocal", "lang", "addError", "error", "setHasReadErrorLoggerStatus", "status", "arguments", "undefined", "setAllSites", "actions", "addErrorLog", "_ref2", "info", "commit", "window", "location", "href", "includes", "_rootState$user", "token", "userId", "userName", "data", "_objectSpread", "time", "Date", "parse", "getSiteOps", "_ref3", "params", "isAll", "platformName", "resolve", "reject", "sitesOps", "v", "getAllSites", "res", "catch", "err"], "sources": ["D:/Users/<USER>/Desktop/dev/sy_declare_ui/src/store/module/app.js"], "sourcesContent": ["import {\r\n  getBreadCrumbList,\r\n  setTagNavListInLocalstorage,\r\n  getMenuByRouter,\r\n  getTagNavListFromLocalstorage,\r\n  getHomeRoute,\r\n  getNextRoute,\r\n  routeHasExist,\r\n  routeEqual,\r\n  getRouteTitleHandled,\r\n  localSave,\r\n  localRead\r\n} from '@/libs/util'\r\nimport beforeClose from '@/router/before-close'\r\nimport { publicApi } from '@/api/system/data'\r\nimport router from '@/router'\r\nimport routers from '@/router/routers'\r\nimport config from '@/config'\r\nimport routes from '../../router/routers'\r\n\r\nconst { homeName } = config\r\n\r\nconst closePage = (state, route) => {\r\n  const nextRoute = getNextRoute(state.tagNavList, route)\r\n  state.tagNavList = state.tagNavList.filter(item => {\r\n    return !routeEqual(item, route)\r\n  })\r\n  router.push(nextRoute)\r\n}\r\n\r\nexport default {\r\n  state: {\r\n    breadCrumbList: [],\r\n    tagNavList: [],\r\n    homeRoute: getHomeRoute(routers, homeName),\r\n    local: localRead('local'),\r\n    errorList: [],\r\n    hasReadErrorPage: false,\r\n    productID:\"\",\r\n    productFlag:false,\r\n    isTop: (localRead('layout')==='top'),\r\n    allSites: [] // 所有站点选项\r\n  },\r\n  getters: {\r\n    menuList: (state, getters, rootState) => getMenuByRouter(routes, rootState.user.access),\r\n    errorCount: state => state.errorList.length,\r\n    getIsTop: state => state.isTop\r\n  },\r\n  mutations: {\r\n    setBreadCrumb (state, route) {\r\n      state.breadCrumbList = getBreadCrumbList(route, state.homeRoute)\r\n    },\r\n    setIsTop(state,isTop){\r\n      state.isTop = isTop\r\n    },\r\n    setTagNavList (state, list) {\r\n      let tagList = []\r\n      if (list) {\r\n        tagList = [...list]\r\n      } else {\r\n        tagList = getTagNavListFromLocalstorage() || []\r\n      }\r\n      if (tagList[0] && tagList[0].name !== homeName) tagList.shift()\r\n      let homeTagIndex = tagList.findIndex(item => item.name === homeName)\r\n      if (homeTagIndex > 0) {\r\n        let homeTag = tagList.splice(homeTagIndex, 1)[0]\r\n        tagList.unshift(homeTag)\r\n      }\r\n      state.tagNavList = tagList\r\n      setTagNavListInLocalstorage([...tagList])\r\n    },\r\n    setProductID(state,id){\r\n      state.productID = id;\r\n    },\r\n    setproductFlag(state,val){\r\n      state.productFlag = val;\r\n    },\r\n    closeTag (state, route) {\r\n      let tag = state.tagNavList.filter(item => routeEqual(item, route))\r\n      route = tag[0] ? tag[0] : null\r\n      if (!route) return\r\n      if (route.meta && route.meta.beforeCloseName && route.meta.beforeCloseName in beforeClose) {\r\n        new Promise(beforeClose[route.meta.beforeCloseName]).then(close => {\r\n          if (close) {\r\n            closePage(state, route)\r\n          }\r\n        })\r\n      } else {\r\n        closePage(state, route)\r\n      }\r\n    },\r\n    addTag (state, { route, type = 'unshift' }) {\r\n      let router = getRouteTitleHandled(route)\r\n      if (!routeHasExist(state.tagNavList, router)) {\r\n        if (type === 'push') {\r\n          state.tagNavList.push(router)\r\n        } else {\r\n          if (router.name === homeName) {\r\n            state.tagNavList.unshift(router)\r\n          } else {\r\n            state.tagNavList.splice(1, 0, router)\r\n          }\r\n        }\r\n        setTagNavListInLocalstorage([...state.tagNavList])\r\n      }\r\n    },\r\n    setLocal (state, lang) {\r\n      localSave('local', lang)\r\n      state.local = lang\r\n    },\r\n    addError (state, error) {\r\n      state.errorList.push(error)\r\n    },\r\n    setHasReadErrorLoggerStatus (state, status = true) {\r\n      state.hasReadErrorPage = status\r\n    },\r\n\r\n    setAllSites (state, allSites) {\r\n      state.allSites = allSites\r\n    },\r\n  },\r\n  actions: {\r\n    addErrorLog ({ commit, rootState }, info) {\r\n      if (!window.location.href.includes('error_logger_page')) commit('setHasReadErrorLoggerStatus', false)\r\n      const { user: { token, userId, userName } } = rootState\r\n      let data = {\r\n        ...info,\r\n        time: Date.parse(new Date()),\r\n        token,\r\n        userId,\r\n        userName\r\n      }\r\n      commit('addError', data)\r\n      /* saveErrorLogger(info).then(() => {\r\n        commit('addError', data)\r\n      }) */\r\n    },\r\n\r\n    // 获取所有站点\r\n    getSiteOps ({ state, commit }, params = {}) {\r\n      const { isAll } = params; // platformName平台名称，isAll是否返回全部站点\r\n      const platformName = params.platformName || 'Amazon';\r\n      return new Promise((resolve, reject) => {\r\n        if(state.allSites && state.allSites.length > 0) {\r\n          const sitesOps = isAll ? state.allSites : state.allSites.filter(v => v.platformName === platformName);\r\n          resolve(sitesOps);\r\n        } else { // 如果还没有站点选项，就请求接口获取\r\n          publicApi.getAllSites().then(res => {\r\n            if (res && res['code'] === 0) {\r\n              const allSites = res.data || [];\r\n              commit('setAllSites', allSites); // 缓存站点信息\r\n              const sitesOps = isAll ? allSites : allSites.filter(v => v.platformName === platformName);\r\n              resolve(sitesOps)\r\n            }\r\n          }).catch(err => {\r\n            reject(err)\r\n          })\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";;;;;;;;;;AAAA,SACEA,iBAAiB,EACjBC,2BAA2B,EAC3BC,eAAe,EACfC,6BAA6B,EAC7BC,YAAY,EACZC,YAAY,EACZC,aAAa,EACbC,UAAU,EACVC,oBAAoB,EACpBC,SAAS,EACTC,SAAS,QACJ,aAAa;AACpB,OAAOC,WAAW,MAAM,uBAAuB;AAC/C,SAASC,SAAS,QAAQ,mBAAmB;AAC7C,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,OAAO,MAAM,kBAAkB;AACtC,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,MAAM,MAAM,sBAAsB;AAEzC,IAAQC,QAAQ,GAAKF,MAAM,CAAnBE,QAAQ;AAEhB,IAAMC,SAAS,GAAG,SAAZA,SAASA,CAAIC,KAAK,EAAEC,KAAK,EAAK;EAClC,IAAMC,SAAS,GAAGhB,YAAY,CAACc,KAAK,CAACG,UAAU,EAAEF,KAAK,CAAC;EACvDD,KAAK,CAACG,UAAU,GAAGH,KAAK,CAACG,UAAU,CAACC,MAAM,CAAC,UAAAC,IAAI,EAAI;IACjD,OAAO,CAACjB,UAAU,CAACiB,IAAI,EAAEJ,KAAK,CAAC;EACjC,CAAC,CAAC;EACFP,MAAM,CAACY,IAAI,CAACJ,SAAS,CAAC;AACxB,CAAC;AAED,eAAe;EACbF,KAAK,EAAE;IACLO,cAAc,EAAE,EAAE;IAClBJ,UAAU,EAAE,EAAE;IACdK,SAAS,EAAEvB,YAAY,CAACU,OAAO,EAAEG,QAAQ,CAAC;IAC1CW,KAAK,EAAElB,SAAS,CAAC,OAAO,CAAC;IACzBmB,SAAS,EAAE,EAAE;IACbC,gBAAgB,EAAE,KAAK;IACvBC,SAAS,EAAC,EAAE;IACZC,WAAW,EAAC,KAAK;IACjBC,KAAK,EAAGvB,SAAS,CAAC,QAAQ,CAAC,KAAG,KAAM;IACpCwB,QAAQ,EAAE,EAAE,CAAC;EACf,CAAC;;EACDC,OAAO,EAAE;IACPC,QAAQ,EAAE,SAAAA,SAACjB,KAAK,EAAEgB,OAAO,EAAEE,SAAS;MAAA,OAAKnC,eAAe,CAACc,MAAM,EAAEqB,SAAS,CAACC,IAAI,CAACC,MAAM,CAAC;IAAA;IACvFC,UAAU,EAAE,SAAAA,WAAArB,KAAK;MAAA,OAAIA,KAAK,CAACU,SAAS,CAACY,MAAM;IAAA;IAC3CC,QAAQ,EAAE,SAAAA,SAAAvB,KAAK;MAAA,OAAIA,KAAK,CAACc,KAAK;IAAA;EAChC,CAAC;EACDU,SAAS,EAAE;IACTC,aAAa,WAAAA,cAAEzB,KAAK,EAAEC,KAAK,EAAE;MAC3BD,KAAK,CAACO,cAAc,GAAG1B,iBAAiB,CAACoB,KAAK,EAAED,KAAK,CAACQ,SAAS,CAAC;IAClE,CAAC;IACDkB,QAAQ,WAAAA,SAAC1B,KAAK,EAACc,KAAK,EAAC;MACnBd,KAAK,CAACc,KAAK,GAAGA,KAAK;IACrB,CAAC;IACDa,aAAa,WAAAA,cAAE3B,KAAK,EAAE4B,IAAI,EAAE;MAC1B,IAAIC,OAAO,GAAG,EAAE;MAChB,IAAID,IAAI,EAAE;QACRC,OAAO,GAAAC,kBAAA,CAAOF,IAAI,CAAC;MACrB,CAAC,MAAM;QACLC,OAAO,GAAG7C,6BAA6B,CAAC,CAAC,IAAI,EAAE;MACjD;MACA,IAAI6C,OAAO,CAAC,CAAC,CAAC,IAAIA,OAAO,CAAC,CAAC,CAAC,CAACE,IAAI,KAAKjC,QAAQ,EAAE+B,OAAO,CAACG,KAAK,CAAC,CAAC;MAC/D,IAAIC,YAAY,GAAGJ,OAAO,CAACK,SAAS,CAAC,UAAA7B,IAAI;QAAA,OAAIA,IAAI,CAAC0B,IAAI,KAAKjC,QAAQ;MAAA,EAAC;MACpE,IAAImC,YAAY,GAAG,CAAC,EAAE;QACpB,IAAIE,OAAO,GAAGN,OAAO,CAACO,MAAM,CAACH,YAAY,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAChDJ,OAAO,CAACQ,OAAO,CAACF,OAAO,CAAC;MAC1B;MACAnC,KAAK,CAACG,UAAU,GAAG0B,OAAO;MAC1B/C,2BAA2B,CAAAgD,kBAAA,CAAKD,OAAO,CAAC,CAAC;IAC3C,CAAC;IACDS,YAAY,WAAAA,aAACtC,KAAK,EAACuC,EAAE,EAAC;MACpBvC,KAAK,CAACY,SAAS,GAAG2B,EAAE;IACtB,CAAC;IACDC,cAAc,WAAAA,eAACxC,KAAK,EAACyC,GAAG,EAAC;MACvBzC,KAAK,CAACa,WAAW,GAAG4B,GAAG;IACzB,CAAC;IACDC,QAAQ,WAAAA,SAAE1C,KAAK,EAAEC,KAAK,EAAE;MACtB,IAAI0C,GAAG,GAAG3C,KAAK,CAACG,UAAU,CAACC,MAAM,CAAC,UAAAC,IAAI;QAAA,OAAIjB,UAAU,CAACiB,IAAI,EAAEJ,KAAK,CAAC;MAAA,EAAC;MAClEA,KAAK,GAAG0C,GAAG,CAAC,CAAC,CAAC,GAAGA,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI;MAC9B,IAAI,CAAC1C,KAAK,EAAE;MACZ,IAAIA,KAAK,CAAC2C,IAAI,IAAI3C,KAAK,CAAC2C,IAAI,CAACC,eAAe,IAAI5C,KAAK,CAAC2C,IAAI,CAACC,eAAe,IAAIrD,WAAW,EAAE;QACzF,IAAIsD,OAAO,CAACtD,WAAW,CAACS,KAAK,CAAC2C,IAAI,CAACC,eAAe,CAAC,CAAC,CAACE,IAAI,CAAC,UAAAC,KAAK,EAAI;UACjE,IAAIA,KAAK,EAAE;YACTjD,SAAS,CAACC,KAAK,EAAEC,KAAK,CAAC;UACzB;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACLF,SAAS,CAACC,KAAK,EAAEC,KAAK,CAAC;MACzB;IACF,CAAC;IACDgD,MAAM,WAAAA,OAAEjD,KAAK,EAAAkD,IAAA,EAA+B;MAAA,IAA3BjD,KAAK,GAAAiD,IAAA,CAALjD,KAAK;QAAAkD,SAAA,GAAAD,IAAA,CAAEE,IAAI;QAAJA,IAAI,GAAAD,SAAA,cAAG,SAAS,GAAAA,SAAA;MACtC,IAAIzD,MAAM,GAAGL,oBAAoB,CAACY,KAAK,CAAC;MACxC,IAAI,CAACd,aAAa,CAACa,KAAK,CAACG,UAAU,EAAET,MAAM,CAAC,EAAE;QAC5C,IAAI0D,IAAI,KAAK,MAAM,EAAE;UACnBpD,KAAK,CAACG,UAAU,CAACG,IAAI,CAACZ,MAAM,CAAC;QAC/B,CAAC,MAAM;UACL,IAAIA,MAAM,CAACqC,IAAI,KAAKjC,QAAQ,EAAE;YAC5BE,KAAK,CAACG,UAAU,CAACkC,OAAO,CAAC3C,MAAM,CAAC;UAClC,CAAC,MAAM;YACLM,KAAK,CAACG,UAAU,CAACiC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE1C,MAAM,CAAC;UACvC;QACF;QACAZ,2BAA2B,CAAAgD,kBAAA,CAAK9B,KAAK,CAACG,UAAU,CAAC,CAAC;MACpD;IACF,CAAC;IACDkD,QAAQ,WAAAA,SAAErD,KAAK,EAAEsD,IAAI,EAAE;MACrBhE,SAAS,CAAC,OAAO,EAAEgE,IAAI,CAAC;MACxBtD,KAAK,CAACS,KAAK,GAAG6C,IAAI;IACpB,CAAC;IACDC,QAAQ,WAAAA,SAAEvD,KAAK,EAAEwD,KAAK,EAAE;MACtBxD,KAAK,CAACU,SAAS,CAACJ,IAAI,CAACkD,KAAK,CAAC;IAC7B,CAAC;IACDC,2BAA2B,WAAAA,4BAAEzD,KAAK,EAAiB;MAAA,IAAf0D,MAAM,GAAAC,SAAA,CAAArC,MAAA,QAAAqC,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,IAAI;MAC/C3D,KAAK,CAACW,gBAAgB,GAAG+C,MAAM;IACjC,CAAC;IAEDG,WAAW,WAAAA,YAAE7D,KAAK,EAAEe,QAAQ,EAAE;MAC5Bf,KAAK,CAACe,QAAQ,GAAGA,QAAQ;IAC3B;EACF,CAAC;EACD+C,OAAO,EAAE;IACPC,WAAW,WAAAA,YAAAC,KAAA,EAAyBC,IAAI,EAAE;MAAA,IAA3BC,MAAM,GAAAF,KAAA,CAANE,MAAM;QAAEhD,SAAS,GAAA8C,KAAA,CAAT9C,SAAS;MAC9B,IAAI,CAACiD,MAAM,CAACC,QAAQ,CAACC,IAAI,CAACC,QAAQ,CAAC,mBAAmB,CAAC,EAAEJ,MAAM,CAAC,6BAA6B,EAAE,KAAK,CAAC;MACrG,IAAAK,eAAA,GAA8CrD,SAAS,CAA/CC,IAAI;QAAIqD,KAAK,GAAAD,eAAA,CAALC,KAAK;QAAEC,MAAM,GAAAF,eAAA,CAANE,MAAM;QAAEC,QAAQ,GAAAH,eAAA,CAARG,QAAQ;MACvC,IAAIC,IAAI,GAAAC,aAAA,CAAAA,aAAA,KACHX,IAAI;QACPY,IAAI,EAAEC,IAAI,CAACC,KAAK,CAAC,IAAID,IAAI,CAAC,CAAC,CAAC;QAC5BN,KAAK,EAALA,KAAK;QACLC,MAAM,EAANA,MAAM;QACNC,QAAQ,EAARA;MAAQ,EACT;MACDR,MAAM,CAAC,UAAU,EAAES,IAAI,CAAC;MACxB;AACN;AACA;IACI,CAAC;IAED;IACAK,UAAU,WAAAA,WAAAC,KAAA,EAAkC;MAAA,IAA9BjF,KAAK,GAAAiF,KAAA,CAALjF,KAAK;QAAEkE,MAAM,GAAAe,KAAA,CAANf,MAAM;MAAA,IAAIgB,MAAM,GAAAvB,SAAA,CAAArC,MAAA,QAAAqC,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC,CAAC;MACxC,IAAQwB,KAAK,GAAKD,MAAM,CAAhBC,KAAK,CAAY,CAAC;MAC1B,IAAMC,YAAY,GAAGF,MAAM,CAACE,YAAY,IAAI,QAAQ;MACpD,OAAO,IAAItC,OAAO,CAAC,UAACuC,OAAO,EAAEC,MAAM,EAAK;QACtC,IAAGtF,KAAK,CAACe,QAAQ,IAAIf,KAAK,CAACe,QAAQ,CAACO,MAAM,GAAG,CAAC,EAAE;UAC9C,IAAMiE,QAAQ,GAAGJ,KAAK,GAAGnF,KAAK,CAACe,QAAQ,GAAGf,KAAK,CAACe,QAAQ,CAACX,MAAM,CAAC,UAAAoF,CAAC;YAAA,OAAIA,CAAC,CAACJ,YAAY,KAAKA,YAAY;UAAA,EAAC;UACrGC,OAAO,CAACE,QAAQ,CAAC;QACnB,CAAC,MAAM;UAAE;UACP9F,SAAS,CAACgG,WAAW,CAAC,CAAC,CAAC1C,IAAI,CAAC,UAAA2C,GAAG,EAAI;YAClC,IAAIA,GAAG,IAAIA,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;cAC5B,IAAM3E,QAAQ,GAAG2E,GAAG,CAACf,IAAI,IAAI,EAAE;cAC/BT,MAAM,CAAC,aAAa,EAAEnD,QAAQ,CAAC,CAAC,CAAC;cACjC,IAAMwE,SAAQ,GAAGJ,KAAK,GAAGpE,QAAQ,GAAGA,QAAQ,CAACX,MAAM,CAAC,UAAAoF,CAAC;gBAAA,OAAIA,CAAC,CAACJ,YAAY,KAAKA,YAAY;cAAA,EAAC;cACzFC,OAAO,CAACE,SAAQ,CAAC;YACnB;UACF,CAAC,CAAC,CAACI,KAAK,CAAC,UAAAC,GAAG,EAAI;YACdN,MAAM,CAACM,GAAG,CAAC;UACb,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ;EACF;AACF,CAAC"}]}