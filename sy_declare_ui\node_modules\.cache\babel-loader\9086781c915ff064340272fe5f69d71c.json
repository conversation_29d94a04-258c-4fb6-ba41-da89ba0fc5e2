{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\api\\system\\authority_1.js", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\api\\system\\authority_1.js", "mtime": 1752737748419}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\.babelrc", "mtime": 1752737748316}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "authorityPath", "getAuthorityForUser", "userId", "params", "url", "method", "getAuthorityForRole", "roleId", "grantAuthorityForUser", "_ref", "authorityIds", "data", "join", "grantAuthorityForRole", "_ref2", "getAuthorityMenu", "menuName", "getAuthorityForAction", "actionId", "getAllApi", "status", "grantAuthorityForAction", "_ref3"], "sources": ["D:/Users/<USER>/Desktop/dev/sy_declare_ui/src/api/system/authority_1.js"], "sourcesContent": ["import request from '@/libs/request'\r\nconst authorityPath = \"/base/authority\";\r\n/**\r\n * 获取用户已分配权限\r\n * @param userId\r\n */\r\nconst getAuthorityForUser = (userId) => {\r\n    const params = {\r\n        userId: userId\r\n    }\r\n    return request({\r\n        url: authorityPath+'/user',\r\n        params,\r\n        method: 'get'\r\n    })\r\n}\r\n\r\n/**\r\n * 获取角色已分配权限\r\n * @param roleId\r\n */\r\nconst getAuthorityForRole = (roleId) => {\r\n    const params = {\r\n        roleId: roleId\r\n    }\r\n    return request({\r\n        url: authorityPath+'/role',\r\n        params,\r\n        method: 'get'\r\n    })\r\n}\r\n\r\n/**\r\n * 用户授权\r\n */\r\nconst grantAuthorityForUser = ({ userId, authorityIds }) => {\r\n    const data = {\r\n        userId: userId,\r\n        authorityIds: authorityIds.join(',')\r\n    }\r\n    return request({\r\n        url: authorityPath+'/grantUser',\r\n        data,\r\n        method: 'post'\r\n    })\r\n}\r\n\r\n/**\r\n * 角色授权\r\n * @param userId\r\n */\r\nconst grantAuthorityForRole = ({ roleId, authorityIds }) => {\r\n    const data = {\r\n        roleId: roleId,\r\n        authorityIds: authorityIds.join(',')\r\n    }\r\n    return request({\r\n        url: authorityPath+'/grantRole',\r\n        data,\r\n        method: 'post'\r\n    })\r\n}\r\n\r\n/**\r\n * 获取菜单和操作权限列表\r\n * @param menuName\r\n */\r\nexport const getAuthorityMenu = (menuName) => {\r\n    const params = {\r\n        menuName: menuName\r\n    }\r\n    return request({\r\n        url: 'base/authority/menu',\r\n        params,\r\n        method: 'get'\r\n    })\r\n}\r\n\r\n\r\n/**\r\n * 获取操作分配的权限\r\n * @param actionId\r\n */\r\nconst getAuthorityForAction = (actionId) => {\r\n    const params = {\r\n        actionId: actionId\r\n    }\r\n    return request({\r\n        url: authorityPath+'/action',\r\n        params,\r\n        method: 'get'\r\n    })\r\n}\r\n/**\r\n * 获取所有权限,status=-1或空是表示所有，0表示启用，1表示停用\r\n */\r\nconst getAllApi = (status) => {\r\n    const params = {\r\n        status: status\r\n    }\r\n    return request({\r\n        url: authorityPath+'/getAllApi',\r\n        params,\r\n        method: 'get'\r\n    })\r\n}\r\n/**\r\n * 角色授权\r\n * @param userId\r\n */\r\nconst grantAuthorityForAction = ({ actionId, authorityIds }) => {\r\n    const data = {\r\n        actionId: actionId,\r\n        authorityIds: authorityIds.join(',')\r\n    }\r\n    return request({\r\n        url: authorityPath+'/grantAction',\r\n        data,\r\n        method: 'post'\r\n    })\r\n}\r\nexport default {\r\n    getAuthorityForUser,\r\n    grantAuthorityForUser,\r\n    getAuthorityForRole,\r\n    grantAuthorityForRole,\r\n    getAuthorityMenu,\r\n    getAuthorityForAction,\r\n    grantAuthorityForAction,\r\n    getAllApi\r\n}\r\n"], "mappings": ";AAAA,OAAOA,OAAO,MAAM,gBAAgB;AACpC,IAAMC,aAAa,GAAG,iBAAiB;AACvC;AACA;AACA;AACA;AACA,IAAMC,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAIC,MAAM,EAAK;EACpC,IAAMC,MAAM,GAAG;IACXD,MAAM,EAAEA;EACZ,CAAC;EACD,OAAOH,OAAO,CAAC;IACXK,GAAG,EAAEJ,aAAa,GAAC,OAAO;IAC1BG,MAAM,EAANA,MAAM;IACNE,MAAM,EAAE;EACZ,CAAC,CAAC;AACN,CAAC;;AAED;AACA;AACA;AACA;AACA,IAAMC,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAIC,MAAM,EAAK;EACpC,IAAMJ,MAAM,GAAG;IACXI,MAAM,EAAEA;EACZ,CAAC;EACD,OAAOR,OAAO,CAAC;IACXK,GAAG,EAAEJ,aAAa,GAAC,OAAO;IAC1BG,MAAM,EAANA,MAAM;IACNE,MAAM,EAAE;EACZ,CAAC,CAAC;AACN,CAAC;;AAED;AACA;AACA;AACA,IAAMG,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAAC,IAAA,EAAiC;EAAA,IAA3BP,MAAM,GAAAO,IAAA,CAANP,MAAM;IAAEQ,YAAY,GAAAD,IAAA,CAAZC,YAAY;EACjD,IAAMC,IAAI,GAAG;IACTT,MAAM,EAAEA,MAAM;IACdQ,YAAY,EAAEA,YAAY,CAACE,IAAI,CAAC,GAAG;EACvC,CAAC;EACD,OAAOb,OAAO,CAAC;IACXK,GAAG,EAAEJ,aAAa,GAAC,YAAY;IAC/BW,IAAI,EAAJA,IAAI;IACJN,MAAM,EAAE;EACZ,CAAC,CAAC;AACN,CAAC;;AAED;AACA;AACA;AACA;AACA,IAAMQ,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAAC,KAAA,EAAiC;EAAA,IAA3BP,MAAM,GAAAO,KAAA,CAANP,MAAM;IAAEG,YAAY,GAAAI,KAAA,CAAZJ,YAAY;EACjD,IAAMC,IAAI,GAAG;IACTJ,MAAM,EAAEA,MAAM;IACdG,YAAY,EAAEA,YAAY,CAACE,IAAI,CAAC,GAAG;EACvC,CAAC;EACD,OAAOb,OAAO,CAAC;IACXK,GAAG,EAAEJ,aAAa,GAAC,YAAY;IAC/BW,IAAI,EAAJA,IAAI;IACJN,MAAM,EAAE;EACZ,CAAC,CAAC;AACN,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,IAAMU,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIC,QAAQ,EAAK;EAC1C,IAAMb,MAAM,GAAG;IACXa,QAAQ,EAAEA;EACd,CAAC;EACD,OAAOjB,OAAO,CAAC;IACXK,GAAG,EAAE,qBAAqB;IAC1BD,MAAM,EAANA,MAAM;IACNE,MAAM,EAAE;EACZ,CAAC,CAAC;AACN,CAAC;;AAGD;AACA;AACA;AACA;AACA,IAAMY,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAIC,QAAQ,EAAK;EACxC,IAAMf,MAAM,GAAG;IACXe,QAAQ,EAAEA;EACd,CAAC;EACD,OAAOnB,OAAO,CAAC;IACXK,GAAG,EAAEJ,aAAa,GAAC,SAAS;IAC5BG,MAAM,EAANA,MAAM;IACNE,MAAM,EAAE;EACZ,CAAC,CAAC;AACN,CAAC;AACD;AACA;AACA;AACA,IAAMc,SAAS,GAAG,SAAZA,SAASA,CAAIC,MAAM,EAAK;EAC1B,IAAMjB,MAAM,GAAG;IACXiB,MAAM,EAAEA;EACZ,CAAC;EACD,OAAOrB,OAAO,CAAC;IACXK,GAAG,EAAEJ,aAAa,GAAC,YAAY;IAC/BG,MAAM,EAANA,MAAM;IACNE,MAAM,EAAE;EACZ,CAAC,CAAC;AACN,CAAC;AACD;AACA;AACA;AACA;AACA,IAAMgB,uBAAuB,GAAG,SAA1BA,uBAAuBA,CAAAC,KAAA,EAAmC;EAAA,IAA7BJ,QAAQ,GAAAI,KAAA,CAARJ,QAAQ;IAAER,YAAY,GAAAY,KAAA,CAAZZ,YAAY;EACrD,IAAMC,IAAI,GAAG;IACTO,QAAQ,EAAEA,QAAQ;IAClBR,YAAY,EAAEA,YAAY,CAACE,IAAI,CAAC,GAAG;EACvC,CAAC;EACD,OAAOb,OAAO,CAAC;IACXK,GAAG,EAAEJ,aAAa,GAAC,cAAc;IACjCW,IAAI,EAAJA,IAAI;IACJN,MAAM,EAAE;EACZ,CAAC,CAAC;AACN,CAAC;AACD,eAAe;EACXJ,mBAAmB,EAAnBA,mBAAmB;EACnBO,qBAAqB,EAArBA,qBAAqB;EACrBF,mBAAmB,EAAnBA,mBAAmB;EACnBO,qBAAqB,EAArBA,qBAAqB;EACrBE,gBAAgB,EAAhBA,gBAAgB;EAChBE,qBAAqB,EAArBA,qBAAqB;EACrBI,uBAAuB,EAAvBA,uBAAuB;EACvBF,SAAS,EAATA;AACJ,CAAC"}]}