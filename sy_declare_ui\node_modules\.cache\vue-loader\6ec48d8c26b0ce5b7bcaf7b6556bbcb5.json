{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\custom\\base\\vatNo\\index.vue?vue&type=style&index=0&id=e38d14aa&lang=less&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\custom\\base\\vatNo\\index.vue", "mtime": 1754360258641}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1752744819993}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQoud2lkdGhDbGFzcyB7DQogIHdpZHRoOiAzNTBweA0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AA2RA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/view/module/custom/base/vatNo", "sourcesContent": ["<!--\r\n@create date 2020-12-14\r\n<AUTHOR>\r\n@desc 清关税号维护表\r\n-->\r\n<template>\r\n  <Card>\r\n    <Form ref=\"formInfo\" :model=\"formInfo\" inline :label-width=\"60\">\r\n      <FormItem prop=\"number1\" label=\"税号\">\r\n        <Input v-model=\"formInfo.vatNo\" placeholder=\"请输入税号搜索\"/>\r\n      </FormItem>\r\n      <FormItem prop=\"shopIds\" label=\"店铺\">\r\n        <ShopSelect v-model=\"formInfo.shopIds\" placeholder=\"店铺\" width=\"205px\" valueField=\"id\"/>\r\n      </FormItem>\r\n      <FormItem label=\"目的国\" prop=\"country\">\r\n        <Select v-model=\"formInfo.country\" filterable clearable placeholder=\"请选择目的国\" style=\"width:150px\">\r\n          <Option v-for=\"(item,index) in countryList\" :value=\"item['two_code']\" :key=\"index\">{{ item['name_cn'] }}\r\n          </Option>\r\n        </Select>\r\n      </FormItem>\r\n      <FormItem>\r\n        <Button type=\"primary\" @click=\"handleSearch()\">查询</Button>\r\n        <Button @click=\"handleReset()\" style=\"margin-left:10px\">重置</Button>\r\n      </FormItem>\r\n    </Form>\r\n    <div style=\"margin-bottom:10px\">\r\n      <div style=\"float:left\">\r\n        <Upload ref=\"uploadFileRef\" name=\"importFile\" :action=\"importURl\" :max-size=\"10240\"\r\n                :on-success=\"handleImportSuccess\" :format=\"['xls', 'xlsx']\" :show-upload-list=\"false\"\r\n                :on-format-error=\"handleImportFormatError\"\r\n                :on-error=\"handleImportError\" :headers=\"loginInfo\" :on-exceeded-size=\"handleMaxSize\">\r\n          <Button class=\"search-btn\" type=\"primary\">导入</Button>\r\n        </Upload>\r\n      </div>\r\n      <Button style=\"margin-left:10px;\" @click=\"addVatNo\" :loading=\"saving\">新增</Button>\r\n      <Button @click=\"templateExport\" style=\"margin-left:10px;\" :loading=\"saving\">导入模板</Button>\r\n      <Button @click=\"vatNoExport\" style=\"margin-left:10px;\" :loading=\"saving\">导出</Button>\r\n    </div>\r\n    <Table :border=\"true\" :columns=\"columns\" :data=\"data\" :loading=\"loading\">\r\n      <template v-slot:country=\"{row}\">\r\n        <span v-for=\"item in countryList\" v-if=\"item['two_code'] === row.country\">{{ item['name_cn'] }}</span>\r\n      </template>\r\n      <template v-slot:shopName=\"{row}\">\r\n        <span v-for=\"item in shopList\" v-if=\"item['id'] === row.shopId\">{{ item['name'] }}</span>\r\n      </template>\r\n      <template v-slot:action=\"{row}\">\r\n        <Button size=\"small\" type=\"info\" @click=\"editVatNo(row)\" style=\"margin:0 2px\">编辑</Button>\r\n        <Button size=\"small\" type=\"info\" @click=\"delVatNo(row)\" style=\"margin:0 2px\">删除</Button>\r\n      </template>\r\n    </Table>\r\n    <Page :total=\"pageInfo.total \" :current=\"pageInfo.page\" :page-size=\"pageInfo.limit\" :show-elevator=\"true\"\r\n          :show-sizer=\"true\"\r\n          :show-total=\"true\" @on-change=\"handlePage\" @on-page-size-change='handlePageSize' :transfer=\"true\"></Page>\r\n    <Modal :width=\"530\" v-model=\"modal\" :title=\"title\" @on-cancel=\"cancelForm\">\r\n      <Spin :fix=\"true\" v-if=\"spinShow\">加载中...</Spin>\r\n      <Form ref=\"vatNoForm\" :model=\"vatNoForm\" inline label-position=\"right\" :label-width=\"110\">\r\n        <FormItem label=\"店铺\" prop=\"shopId\" :rules=\"{required: true, message: '不能为空', trigger: 'change'}\">\r\n          <Select v-model=\"vatNoForm.shopId\" filterable clearable placeholder=\"网店\" class=\"widthClass\">\r\n            <Option v-for=\"(item,index) in shopList\" :value=\"item['id']\" :key=\"index\">{{ item['name'] }}\r\n            </Option>\r\n          </Select>\r\n        </FormItem>\r\n        <FormItem label=\"目的国\" prop=\"country\" :rules=\"{required: true, message: '不能为空', trigger: 'blur'}\">\r\n          <Select v-model=\"vatNoForm.country\" filterable clearable placeholder=\"请选择目的国\" class=\"widthClass\">\r\n            <Option v-for=\"(item,index) in countryList\" :value=\"item['two_code']\" :key=\"index\">{{ item['name_cn'] }}\r\n            </Option>\r\n          </Select>\r\n        </FormItem>\r\n        <FormItem label=\"税号\" prop=\"vatNo\" :rules=\"{required: true, message: '不能为空', trigger: 'blur'}\">\r\n          <Input v-model.trim=\"vatNoForm.vatNo\" :readonly=\"disabled\" placeholder=\"请输入\" class=\"widthClass\"/>\r\n        </FormItem>\r\n        <FormItem label=\"税号公司\" prop=\"company\">\r\n          <Input v-model.trim=\"vatNoForm.company\" :readonly=\"disabled\" placeholder=\"请输入\" class=\"widthClass\"/>\r\n        </FormItem>\r\n        <FormItem label=\"地址\" prop=\"address\">\r\n          <Input v-model.trim=\"vatNoForm.address\" :readonly=\"disabled\" placeholder=\"请输入\" class=\"widthClass\"/>\r\n        </FormItem>\r\n        <FormItem label=\"EORI\" prop=\"eori\">\r\n          <Input v-model.trim=\"vatNoForm.eori\" :readonly=\"disabled\" placeholder=\"请输入\" class=\"widthClass\"/>\r\n        </FormItem>\r\n      </Form>\r\n      <div slot=\"footer\">\r\n        <Button type=\"primary\" :disabled=\"disabled\" :loading=\"saving\" @click=\"saveVatNo\">保存</Button>\r\n        <Button @click=\"cancelForm\">取消</Button>\r\n      </div>\r\n    </Modal>\r\n  </Card>\r\n</template>\r\n<script>\r\nimport CommonApi from \"@/api/base/commonApi\";\r\nimport VatNo from \"@/api/custom/vatNo\";\r\nimport UploadImg from \"@/view/module/custom/company/common/uploadImg.vue\";\r\nimport Shop from \"@/api/basf/shop\";\r\nimport ShopSelect from \"@/components/shopSelect/index.vue\";\r\nimport {getToken, getUrl} from \"@/libs/util\";\r\nimport ClearanceLink from \"@/api/custom/clearanceLink\";\r\nexport default {\r\n  components: {UploadImg,ShopSelect},\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      saving: false,\r\n      modal: false,\r\n      spinShow: false,\r\n      disabled: false,\r\n      title: '',\r\n      formInfo: {\r\n        vatNo: '',\r\n        shopIds: '',\r\n        country: ''\r\n      },\r\n      pageInfo: {\r\n        total: 0,\r\n        page: 1,\r\n        limit: 10\r\n      },\r\n      loginInfo: {\r\n        Accept: 'application/json,text/plain, */*, */*',\r\n        mode: 'cors',\r\n        Authorization: 'Bearer ' + getToken()\r\n      },\r\n      importURl: getUrl() + \"/base/vatNo/importFile\",\r\n      vatNoForm: {eori:null,shopId:null,country:null,company:null,vatNo:null,address:null},\r\n      countryList: [],\r\n      shopList:[],\r\n      data: [],\r\n      columns: [\r\n        {title: '网店名称', key: 'shopId', minWidth: 100, align: 'center',slot:'shopName'},\r\n        {title: '目的国', key: 'country', minWidth: 100, align: 'center',slot:'country'},\r\n        {title: '税号', key: 'vatNo', minWidth: 100, align: 'center'},\r\n        {title: '税号公司', key: 'company', minWidth: 150, align: 'center'},\r\n        {title: '地址', key: 'address', minWidth: 300, align: 'center'},\r\n        {title: 'EORI', key: 'eori', minWidth: 100, align: 'center'},\r\n        {title: '操作', key: 'action', width: 150, align: 'center', slot: 'action'}],\r\n    }\r\n  },\r\n  mounted() {\r\n    this.handleSearch();\r\n    this.getCountryList();\r\n    this.getAllShop();\r\n  },\r\n  methods: {\r\n    getAllShop() {\r\n      Shop.getAll().then(res => {\r\n        if (res && res['code'] === 0) {\r\n          this.shopList = res.data;\r\n        }\r\n      })\r\n    },\r\n    handleSearch() {\r\n      this.loading = true;\r\n      let params = {...this.formInfo, ...this.pageInfo}\r\n      const getStr = value => value && Array.isArray(value) ? value.join(\",\") : undefined;\r\n      params['shopIds'] = getStr(params['shopIds']);\r\n      VatNo.listPage(params).then(res => {\r\n        if (res['code'] === 0) {\r\n          this.data = res.data.records;\r\n          this.pageInfo.total = Number(res.data.total)\r\n        }\r\n      }).finally(() => {\r\n        this.loading = false;\r\n      })\r\n    },\r\n    handleReset() {\r\n      this.$refs['formInfo'].resetFields();\r\n    },\r\n    delVatNo(row) {\r\n      this.$Modal.confirm({\r\n        title: '提示',\r\n        content: '您确认要删除这条数据吗？',\r\n        onOk: () => {\r\n          VatNo.remove({id: row.id}).then(res => {\r\n            if (res['code'] === 0) {\r\n              this.$Message.success('删除成功!');\r\n              this.handleSearch();\r\n            }\r\n          })\r\n        },\r\n      })\r\n    },\r\n    editVatNo(row) {\r\n      this.title = \"添加清关税号\";\r\n      this.modal = true;\r\n      this.disabled = false;\r\n      this.resetForm();\r\n      this.vatNoForm = Object.assign({},row);\r\n    },\r\n    addVatNo() {\r\n      this.title = \"添加清关税号\";\r\n      this.modal = true;\r\n      this.disabled = false;\r\n      this.resetForm();\r\n    },\r\n    saveVatNo() {\r\n      this.$refs['vatNoForm'].validate((valid) => {\r\n        if (valid) {\r\n          this.saving = true;\r\n          VatNo.saveVatNo(this.vatNoForm).then(res => {\r\n            if (res['code'] === 0) {\r\n              this.saving = false;\r\n              this.$Message.success('保存成功!');\r\n              this.resetForm();\r\n              this.modal = false;\r\n              this.handleSearch();\r\n            }\r\n          }).finally(() => {\r\n            this.saving = false;\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleImportSuccess(res) {\r\n      this.$refs['uploadFileRef'].clearFiles();\r\n      if (res['code'] === 0) {\r\n        this.$Message.success('导入成功');\r\n        this.handleSearch();\r\n      } else {\r\n        this.$Message.warning(res['message']);\r\n      }\r\n    },\r\n    handleImportFormatError(file) {\r\n      //格式验证失败的钩子\r\n      this.$Modal.error({\r\n        title: '文件格式不正确',\r\n        content: '文件 ' + file.name + '格式不正确，支持上传的文件类型：xls,xlsx',\r\n        okText: '确认'\r\n      });\r\n    },\r\n    handleImportError(err, file) {\r\n      this.$Message.warning(file.message);\r\n    },\r\n    handleMaxSize() {\r\n      this.$Message.warning('图片大小不能超过2M.');\r\n    },\r\n    templateExport(){\r\n      this.loading = true;\r\n      VatNo.downloadTemplate({\"fileName\":\"清关税号导入模板.xls\"}, () => {\r\n        this.loading = false\r\n      })\r\n    },\r\n    vatNoExport() {\r\n      this.loading = true;\r\n      let params = {...this.formInfo}\r\n      const getStr = value => value && Array.isArray(value) ? value.join(\",\") : undefined;\r\n      params['shopIds'] = getStr(params['shopIds']);\r\n      params['fileName'] = \"清关税号_\" + new Date().getExportFormat() + \".xls\";\r\n      this.loading = true;\r\n      VatNo.download(params, () => {\r\n        this.loading = false\r\n      })\r\n    },\r\n    //获取目的国家\r\n    getCountryList() {\r\n      CommonApi.ListDictionaryValueBy(\"base_country\").then(res => {\r\n        if (res && res['code'] === 0) {\r\n          let data = res['data']\r\n          if (data) {\r\n            this.countryList = data.map(item => JSON.parse(item.value));\r\n          }\r\n        }\r\n      })\r\n    },\r\n    handlePage(page) {\r\n      this.pageInfo.page = page;\r\n      this.handleSearch();\r\n    },\r\n    handlePageSize(size) {\r\n      this.pageInfo.page = 1;\r\n      this.pageInfo.limit = size;\r\n      this.handleSearch();\r\n    },\r\n    cancelForm() {\r\n      this.modal = false;\r\n      this.resetForm();\r\n    },\r\n    resetForm() {\r\n      this.$refs['vatNoForm'].resetFields();\r\n      this.vatNoForm = {};\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\">\r\n.widthClass {\r\n  width: 350px\r\n}\r\n</style>\r\n"]}]}