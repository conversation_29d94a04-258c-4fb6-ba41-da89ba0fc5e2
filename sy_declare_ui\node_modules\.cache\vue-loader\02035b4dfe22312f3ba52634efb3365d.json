{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\error-page\\401.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\error-page\\401.vue", "mtime": 1752737748506}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQppbXBvcnQgZXJyb3I0MDEgZnJvbSAnQC9hc3NldHMvaW1hZ2VzL2Vycm9yLXBhZ2UvZXJyb3ItNDAxLnN2ZycNCmltcG9ydCBlcnJvckNvbnRlbnQgZnJvbSAnLi9lcnJvci1jb250ZW50LnZ1ZScNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogJ2Vycm9yXzQwMScsDQogIGNvbXBvbmVudHM6IHsNCiAgICBlcnJvckNvbnRlbnQNCiAgfSwNCiAgZGF0YSAoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIHNyYzogZXJyb3I0MDENCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["401.vue"], "names": [], "mappings": ";AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "401.vue", "sourceRoot": "src/view/error-page", "sourcesContent": ["<template>\r\n  <error-content code=\"401\" desc=\"Oh~~您没有浏览这个页面的权限~\" :src=\"src\"/>\r\n</template>\r\n\r\n<script>\r\nimport error401 from '@/assets/images/error-page/error-401.svg'\r\nimport errorContent from './error-content.vue'\r\nexport default {\r\n  name: 'error_401',\r\n  components: {\r\n    errorContent\r\n  },\r\n  data () {\r\n    return {\r\n      src: error401\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}