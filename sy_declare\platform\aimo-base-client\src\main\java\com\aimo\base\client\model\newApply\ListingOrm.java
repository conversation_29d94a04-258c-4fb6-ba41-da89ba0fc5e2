package com.aimo.base.client.model.newApply;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.aimo.common.mybatis.base.entity.UpdateEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> 销售对照表
 */
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@Data
@TableName("bussiness_listing_orm")
public class ListingOrm extends UpdateEntity<ListingOrm> {


    @TableField(exist = false)
    @Excel(name = "序号", width = 10)
    private Integer index;

    @ApiModelProperty(value = "网店ID")
    private Long shopId;
    @ApiModelProperty(value = "网店名称")
    @TableField(exist = false)
    @Excel(name = "网店名称", width = 20, isImportField = "网店名称")
    private String shopName;

    @ApiModelProperty(value = "物料ID")
    private Long productId;

    @ApiModelProperty(value = "物料编码")
    @Excel(name = "物料编码", width = 20, groupName="基础信息",orderNum = "02", isImportField = "物料编码")
    @TableField(exist = false)
    private String productCode;

    @ApiModelProperty(value = "物料型号")
    @Excel(name = "物料型号", width = 20,groupName="基础信息",orderNum = "03")
    @TableField(exist = false)
    private String productName;

    @ApiModelProperty(value = "物料规格")
    @Excel(name = "物料规格", width = 20,groupName="基础信息",orderNum = "04")
    @TableField(exist = false)
    private String productSpec;

    @ApiModelProperty(value = "物料颜色")
    @Excel(name = "物料颜色", width = 20,groupName="基础信息",orderNum = "05")
    @TableField(exist = false)
    private String productColor;

    @ApiModelProperty(value = "物料尺码")
    @Excel(name = "物料尺码", width = 20,groupName="基础信息",orderNum = "06")
    @TableField(exist = false)
    private String productSize;

    @ApiModelProperty(value = "物料内长")
    @Excel(name = "物料内长", width = 20,groupName="基础信息",orderNum = "07")
    @TableField(exist = false)
    private String productInseam;

    @ApiModelProperty(value = "配送方式")
    @Excel(name = "配送方式", width = 20,groupName="基础信息",orderNum = "08")
    private String fulfillmentType;

    @ApiModelProperty(value = "UPC码")
    @TableField(exist = false)
    @Excel(name = "UPC码", width = 20,groupName="基础信息",orderNum = "09")
    private String upcCode;

    @ApiModelProperty(value = "运营人员ID")
    private Long sellerId;

    @ApiModelProperty(value = "运营人员")
    @TableField(exist = false)
    @Excel(name = "运营人员", width = 20,groupName="基础信息",orderNum = "10", isImportField="运营人员")
    private String sellerName;

    @Excel(name = "Listing简称", width = 20, groupName="基础信息",orderNum = "11", isImportField = "Listing简称")
    private String listingTitle;

    @ApiModelProperty(value = "父asin")
    @Excel(name = "父asin", width = 20,groupName="基础信息",orderNum = "12")
    private String parentAsin;

    @ApiModelProperty(value = "销售SKU")
    @Excel(name = "销售SKU", width = 20,groupName="基础信息",orderNum = "13", isImportField = "销售SKU")
    private String sellerSku;

    @ApiModelProperty(value = "fnsku")
    @Excel(name = "fnsku/gtin", width = 20,groupName="基础信息",orderNum = "14", isImportField = "fnsku/gtin")
    private String fnsku;

    @ApiModelProperty(value = "asin")
    @Excel(name = "asin", width = 20,groupName="基础信息",orderNum = "15")
    private String asin;

    @ApiModelProperty(value = "销售类目")
    @Excel(name = "销售类目", width = 20, groupName="基础信息",orderNum = "16", isImportField = "销售类目")
    private String className;

    @ApiModelProperty(value = "主对照表ID")
    private Long mainId;

    @ApiModelProperty(value = "主对照表")
    @TableField(exist = false)
    private ListingOrm listingOrm;

    @ApiModelProperty(value = "主网店名称")
    @Excel(name = "主网店名称", width = 20, groupName="替换信息",orderNum = "17", isImportField = "主网店名称")
    @TableField(exist = false)
    private String shopNameMain;

    @ApiModelProperty(value = "主销售SKU")
    @Excel(name = "主销售SKU", width = 20, groupName="替换信息",orderNum = "18", isImportField = "主销售SKU")
    @TableField(exist = false)
    private String sellerSkuMain;

    @ApiModelProperty(value = "主fnsku/gtin")
    @Excel(name = "主fnsku/gtin", width = 20, groupName="替换信息",orderNum = "19", isImportField = "主fnsku/gtin")
    @TableField(exist = false)
    private String fnskuMain;

    @ApiModelProperty(value = "Listing定位")
    @Excel(name = "Listing定位", width = 20, groupName="标准信息",orderNum = "20")
    private String listingLoc;

    @ApiModelProperty(value = "sku定位")
    @Excel(name = "sku定位", width = 20, groupName="标准信息",orderNum = "21", isImportField = "sku定位")
    private String skuLoc;

    @ApiModelProperty(value = "供给属性")
    @Excel(name = "供给属性", width = 20, groupName="标准信息",orderNum = "22", isImportField = "供给属性")
    private String supplyDesc;

    @ApiModelProperty(value = "备注")
    @Excel(name = "备注", width = 20, groupName = "其他信息",orderNum = "23", isImportField = "备注")
    private String remark;

    @ApiModelProperty(value = "使用状态")
    @Excel(name = "使用状态", width = 20, groupName = "其他信息",orderNum = "24",replace = {"无效_1", "有效_0"})
    private Integer status = 0;

    @ApiModelProperty(value = "同步领星状态")
    private Integer syncLx = 0;

    @ApiModelProperty(value = "同步用友状态")
    private Integer syncYs = 0;

    @ApiModelProperty(value = "预测ID")
    private Long predictionAsinId;

    @Override
    public String toString() {
        return "ListingOrm{" +
                "sellerId=" + sellerId +
                ", sellerName='" + sellerName + '\'' +
                ", shopId=" + shopId +
                ", shopName='" + shopName + '\'' +
                ", listingTitle='" + listingTitle + '\'' +
                ", parentAsin='" + parentAsin + '\'' +
                ", sellerSku='" + sellerSku + '\'' +
                ", fnsku='" + fnsku + '\'' +
                ", asin='" + asin + '\'' +
                ", listingLoc='" + listingLoc + '\'' +
                ", skuLoc='" + skuLoc + '\'' +
                ", supplyDesc='" + supplyDesc + '\'' +
                ", fulfillmentType='" + fulfillmentType + '\'' +
                ", className='" + className + '\'' +
                ", mainId=" + mainId +
                ", upcCode='" + upcCode + '\'' +
                ", productId=" + productId +
                ", remark='" + remark + '\'' +
                ", status=" + status +
                ", predictionAsinId=" + predictionAsinId +
                '}';
    }

    public ListingOrm(Long shopId, String sellerSku, String fnsku) {
        this.shopId = shopId;
        this.sellerSku = sellerSku;
        this.fnsku = fnsku;
    }
    public ListingOrm(String shopName, String sellerSku, String fnsku) {
        this.shopName = shopName;
        this.sellerSku = sellerSku;
        this.fnsku = fnsku;
    }
}
