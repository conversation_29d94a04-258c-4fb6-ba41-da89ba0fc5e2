{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\newApply\\newApply\\newApply\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\newApply\\newApply\\newApply\\index.vue", "mtime": 1754365176808}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\.babelrc", "mtime": 1752737748316}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["autoTableHeight", "isEmpty", "ShopSelect", "SpuSelect", "Multiple", "personSelect", "NewApply", "Workflow", "NewApplyEdit", "getToken", "getUrl", "name", "components", "data", "h", "$createElement", "buttons", "type", "text", "title", "function", "lookData", "editData", "delData", "repealData", "loading", "sellerArr", "currentRow", "personVisible", "yesNoOps", "key", "multiValuesSellerSku", "popVisibleSellerSku", "popContentSellerSku", "undefined", "multiValuesProductCode", "popVisibleProductCode", "popContentProductCode", "importURl", "path", "loginInfo", "Accept", "mode", "Authorization", "selectData", "statusList", "date", "searchForm", "startDate", "endDate", "shops", "spus", "status", "page", "limit", "tableData", "columns", "width", "resizable", "render", "_", "_ref", "row", "value", "_ref2", "min<PERSON><PERSON><PERSON>", "_ref3", "_ref4", "slot", "_ref5", "_ref6", "_ref7", "align", "className", "_ref8", "_ref9", "popColumns", "_ref10", "statusText", "editVisible", "editTitle", "reviewModalVisible", "reviewLoading", "reviewForm", "id", "spu", "agree", "comment", "reviewFormRules", "required", "message", "trigger", "min", "max", "mounted", "handleSearch", "methods", "handleImportError", "err", "file", "$Message", "error", "handleImportSuccess", "res", "$refs", "clearFiles", "code", "handleMaxSize", "warning", "handleImportFormatError", "$Modal", "content", "okText", "open<PERSON>erson", "resetMultiple", "personSelectRef", "selectedIds", "sellers", "<PERSON><PERSON><PERSON><PERSON>", "filter", "v", "includes", "map", "nick<PERSON><PERSON>", "setSelectInfo", "info", "arguments", "length", "personArr", "clickMore", "_objectSpread", "handleDropDownClick", "clickRow", "button", "find", "btn", "isButtonDisabled", "getFilteredButtons", "reviewFlag", "getReviewStatusText", "getReviewStatusColor", "newApplyEditRef", "_this", "confirm", "onOk", "remove", "then", "$message", "finally", "_this2", "repeal", "closeDropdownSellerSku", "multipleRefSellerSkuRef", "trim", "replace", "split", "_toConsumableArray", "Set", "setValueArray", "closeDropdownProductCode", "multipleRefProductCodeRef", "getPara<PERSON>", "params", "getStr", "Array", "isArray", "join", "dateChange", "_this3", "listPage", "records", "for<PERSON>ach", "item", "detailList", "detail", "sellerSku", "total", "parseInt", "handleReset", "resetFields", "clearTxt", "handlePage", "current", "handlePageSize", "size", "onSelectChange", "selection", "downTemplate", "_this4", "handleDel", "_this5", "ids", "sellerSkuUsed", "success", "handleExport", "_this6", "Date", "getExportFormat", "exportFile", "addNewApply", "noticeSellerId", "_this7", "noticeSeller", "refreshStatus", "_this8", "handleApply", "_this9", "concat", "$set", "apply", "catch", "handleReview", "showReviewModal", "handleReviewSubmit", "_this10", "validate", "valid", "variables", "review", "handleReviewCancel"], "sources": ["src/view/module/newApply/newApply/newApply/index.vue"], "sourcesContent": ["<!--\r\n@create date 2025-02-07\r\n@desc 上新管理\r\n-->\r\n<template>\r\n  <div class=\"search-con-top salesRank\">\r\n    <Card :shadow=\"true\">\r\n      <div>\r\n        <Form ref=\"searchFormRef\" class=\"searchForm\" :model=\"searchForm\" :inline=\"true\" @submit.native.prevent>\r\n          <FormItem prop=\"date\">\r\n            <DatePicker type=\"daterange\" v-model=\"date\" placement=\"bottom-start\" @on-change=\"dateChange\"\r\n                        placeholder=\"申请开始日期-申请结束日期\" style=\"width: 200px\"></DatePicker>\r\n          </FormItem>\r\n          <FormItem prop=\"shop\">\r\n            <ShopSelect v-model=\"searchForm.shops\" placeholder=\"选择店铺\" width=\"205px\" valueField=\"id\"/>\r\n          </FormItem>\r\n          <FormItem prop=\"sellers\" class=\"sellerSelectItem\">\r\n            <Select multiple type=\"text\" v-model=\"searchForm.sellers\" placeholder=\"销售员\" filterable :max-tag-count=\"1\" style=\"width: 233px\" :transfer=\"true\" >\r\n              <Option v-for=\"item in sellerArr\" :value=\"item.id\" :key=\"item.id\">{{ item.nickName }}</Option>\r\n            </Select>\r\n            <Button type=\"dashed\" @click=\"openPerson\" style=\"margin-left: 3px\" size=\"default\">选择</Button>\r\n            <person-select :visible=\"personVisible\" :onCancel=\"()=>personVisible=false\"\r\n                           @setPerson=\"arr => (searchForm.sellers = arr.map(v => v.id))\" @setSelectInfo=\"setSelectInfo\"\r\n                           ref=\"personSelectRef\" groupName=\"operations_persons\" :multiple=\"true\" :isQuery=\"true\" />\r\n          </FormItem>\r\n          <FormItem prop=\"spu\">\r\n            <SpuSelect v-model=\"searchForm.spus\" placeholder=\"选择型号\" width=\"205px\"/>\r\n          </FormItem>\r\n          <FormItem prop=\"sellerSku\" class=\"multiClass\">\r\n            <div class=\"flex-h\">\r\n              <Multiple placeholder=\"请输入销售SKU(回车分隔)\" @changeValue=\"(values)=>{ multiValuesSellerSku = values || []; }\" ref=\"multipleRefSellerSkuRef\" style=\"height:32px;\"></Multiple>\r\n              <Button :visible=\"false\" @click=\"()=>{popVisibleSellerSku=true;}\">输入</Button>\r\n              <Dropdown trigger=\"custom\" :visible=\"popVisibleSellerSku\" style=\"margin-left: 3px\" :transfer=\"true\" transfer-class-name=\"orderBillDrop\">\r\n                <template #list>\r\n                  <DropdownMenu class=\"popContentClass\">\r\n                    <Input v-model=\"popContentSellerSku\" type=\"textarea\" :autosize=\"{minRows: 4,maxRows: 8}\" placeholder=\"请输入内容，回车或逗号分隔\" style=\"width: 260px\"/>\r\n                    <div style=\"text-align: right; padding-top: 3px\">\r\n                      <Button type=\"info\" size=\"small\" @click=\"closeDropdownSellerSku\">确定</Button>\r\n                    </div>\r\n                  </DropdownMenu>\r\n                </template>\r\n              </Dropdown>\r\n            </div>\r\n          </FormItem>\r\n          <FormItem prop=\"productCode\" class=\"multiClass\">\r\n            <div class=\"flex-h\">\r\n              <Multiple placeholder=\"请输入产品编码(回车分隔)\" @changeValue=\"(values)=>{ multiValuesProductCode = values || []; }\" ref=\"multipleRefProductCodeRef\" style=\"height:32px;\"></Multiple>\r\n              <Button :visible=\"false\" @click=\"()=>{popVisibleProductCode=true;}\">输入</Button>\r\n              <Dropdown trigger=\"custom\" :visible=\"popVisibleProductCode\" style=\"margin-left: 3px\" :transfer=\"true\" transfer-class-name=\"orderBillDrop\">\r\n                <template #list>\r\n                  <DropdownMenu class=\"popContentClass\">\r\n                    <Input v-model=\"popContentProductCode\" type=\"textarea\" :autosize=\"{minRows: 4,maxRows: 8}\" placeholder=\"请输入内容，回车或逗号分隔\" style=\"width: 260px\"/>\r\n                    <div style=\"text-align: right; padding-top: 3px\">\r\n                      <Button type=\"info\" size=\"small\" @click=\"closeDropdownProductCode\">确定</Button>\r\n                    </div>\r\n                  </DropdownMenu>\r\n                </template>\r\n              </Dropdown>\r\n            </div>\r\n          </FormItem>\r\n          <FormItem prop=\"status\" :clear=\"true\">\r\n            <Select type=\"text\" v-model=\"searchForm.status\" placeholder=\"状态\" style=\"width:160px\">\r\n              <Option v-for=\"(item,index) in statusList\" :value=\"item.key\" :key=\"index\">{{ item['value'] }}</Option>\r\n            </Select>\r\n          </FormItem>\r\n          <FormItem>\r\n            <Button type=\"primary\" @click=\"handleSearch()\">查询</Button>&nbsp;\r\n            <Button @click=\"handleReset()\">重置</Button>\r\n          </FormItem>\r\n        </Form>\r\n      </div>\r\n      <div style=\"margin-bottom: 10px;\" class=\"reportTable\">\r\n        <Button @click=\"addNewApply\" type=\"primary\"  :loading=\"loading\">上新申请</Button>\r\n        <div style=\"display:inline-block\">\r\n          <Upload ref=\"uploadFileRef\" name=\"importFile\" :action=\"importURl\" :max-size=\"10240\"\r\n                  :on-success=\"handleImportSuccess\"\r\n                  :format=\"['xls', 'xlsx']\"\r\n                  :show-upload-list=\"false\"\r\n                  :on-format-error=\"handleImportFormatError\"\r\n                  :on-error=\"handleImportError\" :headers=\"loginInfo\" :on-exceeded-size=\"handleMaxSize\">\r\n            <Button  class=\"buttonMargin\" :loading=\"loading\">上传文件</Button>\r\n          </Upload></div>\r\n        <Button @click=\"downTemplate\" class=\"buttonMargin\" :loading=\"loading\">下载模板</Button>\r\n        <Button @click=\"noticeSellerId\" class=\"buttonMargin\" :loading=\"loading\">通知运营</Button>\r\n        <Button @click=\"refreshStatus\" class=\"buttonMargin\" :loading=\"loading\">检测生成</Button>\r\n        <Button @click=\"handleExport\" class=\"buttonMargin\" :loading=\"loading\">导出</Button>\r\n        <Table :border=\"true\" ref=\"autoTableRef\" :max-height=\"autoTableHeight($refs.autoTableRef)\" :columns=\"columns\"\r\n               :data=\"tableData\" :loading=\"loading\" @on-selection-change=\"onSelectChange\">\r\n          <template v-slot:status=\"{row}\">\r\n            <span v-for=\"(item, index) in statusList\" :key=\"index\" v-copytext=\"item['value']\"\r\n                        v-if=\"item['key'] === row['status']\">{{ item['value'] }}</span>\r\n          </template>\r\n          <template v-slot:isUpc=\"{row}\">\r\n            <span v-for=\"(item, index) in yesNoOps\" :key=\"index\" v-copytext=\"item['name']\"\r\n                  v-if=\"item['key'] === row['isUpc']\">{{ item['name'] }}</span>\r\n          </template>\r\n          <template v-slot:reviewStatus=\"{row}\">\r\n            <Tag :color=\"getReviewStatusColor(row.reviewFlag, row.status)\">\r\n              {{ getReviewStatusText(row.reviewFlag, row.status) }}\r\n            </Tag>\r\n          </template>\r\n          <template v-slot:review=\"{row}\">\r\n            <Button\r\n              v-if=\"row.runReviewStatus === 1\"\r\n              type=\"success\"\r\n              size=\"small\"\r\n              @click=\"handleReview(row)\"\r\n              :loading=\"row.reviewLoading\">\r\n              审核\r\n            </Button>\r\n          </template>\r\n          <template v-slot:detailList=\"{ row }\">\r\n            <div class=\"requestDetailTd\">\r\n              <Row>\r\n                <i-col span=\"7\">销售SKU</i-col>\r\n                <i-col span=\"3\">产品编码</i-col>\r\n                <i-col span=\"6\">产品规格</i-col>\r\n                <i-col span=\"4\">UPC码</i-col>\r\n                <i-col span=\"4\">是否刊登</i-col>\r\n              </Row>\r\n              <Row v-for=\"(item, index) in (row.detailList ? row.detailList.filter((_, i) => i < 3) : [])\" :key=\"index\">\r\n                <i-col span=\"7\" v-copytext=\"item['sellerSku']\" :title=\"item['sellerSku']\" style=\"padding: 3px 0;\">{{item['sellerSku']}}</i-col>\r\n                <i-col span=\"3\" v-copytext=\"item['productCode']\" :title=\"item['productCode']\">{{ item['productCode'] }}</i-col>\r\n                <i-col span=\"6\" v-copytext=\"item['productSpec']\" :title=\"item['productSpec']\">{{ item['productSpec'] }}</i-col>\r\n                <i-col span=\"4\" v-copytext=\"item['upcCode']\" :title=\"item['upcCode']\">{{ item['upcCode'] }}</i-col>\r\n                <i-col span=\"4\" v-copytext=\"item['status']===1?'已刊登':'未刊登'\">{{ item['status']===1?'已刊登':'未刊登' }}</i-col>\r\n              </Row>\r\n              <div style=\"text-align: right; padding-top: 5px; padding-right: 5px\"\r\n                   v-if=\"row.detailList && row.detailList.length > 0\">\r\n                <Poptip\r\n                  title=\"明细信息\"\r\n                  content=\"content\"\r\n                  placement=\"left-end\"\r\n                  trigger=\"click\"\r\n                  :transfer=\"true\"\r\n                  :max-width=\"550\">\r\n                  <a @click=\"clickMore(row)\">查看更多</a>\r\n                  <div slot=\"content\" style=\"padding-bottom: 8px; max-height: 500px\">\r\n                    <Table\r\n                      :columns=\"popColumns\"\r\n                      :border=\"true\"\r\n                      :data=\"row.detailList || []\"\r\n                      size=\"small\"\r\n                      :max-height=\"420\"/>\r\n                  </div>\r\n                </Poptip>\r\n              </div>\r\n            </div>\r\n          </template>\r\n          <template v-slot:action=\"{ row }\">\r\n            <div>\r\n              <template v-for=\"(item, index) in getFilteredButtons(row).length > 3? getFilteredButtons(row).slice(0, 2): getFilteredButtons(row)\">\r\n                <a v-if=\"!isButtonDisabled(item, row)\"\r\n                   @click=\"item.function(row)\"\r\n                   style=\"margin-right: 10px\"\r\n                   :title=\"item.title\"\r\n                   :key=\"'btn-' + index\">{{item.text }}</a>\r\n                <span v-else\r\n                      style=\"margin-right: 10px; color: #c5c8ce; cursor: not-allowed;\"\r\n                      :title=\"item.title + '(已禁用)'\"\r\n                      :key=\"'disabled-btn-' + index\">{{item.text }}</span>\r\n              </template>\r\n              <Dropdown :transfer=\"true\" ref=\"dropdownRef\" @on-click=\"handleDropDownClick($event, row)\" transfer-class-name=\"inventory-manage-down-btns\" v-if=\"getFilteredButtons(row).length > 3\">\r\n                <a href=\"javascript:void(0)\"><span>更多</span><Icon type=\"ios-arrow-down\"></Icon></a>\r\n                <DropdownMenu slot=\"list\">\r\n                  <DropdownItem v-for=\"(item, index) in getFilteredButtons(row).slice(2)\"\r\n                                :name=\"item.type\"\r\n                                :key=\"index\"\r\n                                :disabled=\"isButtonDisabled(item, row)\">{{item.text}}</DropdownItem>\r\n                </DropdownMenu>\r\n              </Dropdown>\r\n            </div>\r\n          </template>\r\n        </Table>\r\n        <Page :total=\"searchForm.total\" size=\"small\" :current=\"searchForm.page\" :page-size=\"searchForm.limit\" :show-elevator=\"true\"\r\n              :show-sizer=\"true\" :show-total=\"true\" @on-change=\"handlePage\" @on-page-size-change='handlePageSize'></Page>\r\n      </div>\r\n      <NewApplyEdit ref=\"newApplyEditRef\" :visible=\"editVisible\" :title=\"editTitle\" :onCancel=\"(value)=>{this.editVisible=false;if(value){this.handleSearch()}}\"></NewApplyEdit>\r\n\r\n      <!-- 审核弹窗 -->\r\n      <Modal\r\n        v-model=\"reviewModalVisible\"\r\n        title=\"审核申请\"\r\n        :mask-closable=\"false\"\r\n        :closable=\"false\"\r\n        width=\"500\">\r\n        <Form ref=\"reviewForm\" :model=\"reviewForm\" :rules=\"reviewFormRules\" :label-width=\"80\">\r\n          <FormItem label=\"型号\">\r\n            <span>{{ reviewForm.spu }}</span>\r\n          </FormItem>\r\n          <FormItem label=\"审核结果\" prop=\"agree\">\r\n            <RadioGroup v-model=\"reviewForm.agree\">\r\n              <Radio label=\"true\">通过</Radio>\r\n              <Radio label=\"false\">驳回</Radio>\r\n            </RadioGroup>\r\n          </FormItem>\r\n          <FormItem label=\"审核意见\" prop=\"comment\">\r\n            <Input\r\n              v-model=\"reviewForm.comment\"\r\n              type=\"textarea\"\r\n              :autosize=\"{minRows: 3, maxRows: 6}\"\r\n              placeholder=\"请输入审核意见\"\r\n              maxlength=\"200\"\r\n              show-word-limit />\r\n          </FormItem>\r\n        </Form>\r\n        <div slot=\"footer\">\r\n          <Button @click=\"handleReviewCancel\">取消</Button>\r\n          <Button type=\"primary\" @click=\"handleReviewSubmit\" :loading=\"reviewLoading\">确定</Button>\r\n        </div>\r\n      </Modal>\r\n    </Card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {autoTableHeight, isEmpty} from \"@/libs/tools.js\";\r\nimport ShopSelect from \"@/components/shopSelect/index.vue\";\r\nimport SpuSelect from \"@/components/spu/index.vue\";\r\nimport Multiple from \"@/view/module/common/multipleInput.vue\";\r\nimport personSelect from \"_c/person-select-radio/index.vue\";\r\nimport NewApply from \"@/api/newApply/newApply\";\r\nimport Workflow from \"@/api/base/workflow\";\r\nimport NewApplyEdit from \"./edit.vue\";\r\nimport {getToken, getUrl} from \"@/libs/util\";\r\nexport default {\r\n  name: \"newApply\",\r\n  components: {personSelect, Multiple, ShopSelect,SpuSelect,NewApplyEdit},\r\n  data() {\r\n    const buttons = [{type: \"look\", text: \"查看\", title: \"点击查看\", function: this.lookData},\r\n                    {type: \"edit\", text: \"修改\", title: \"点击修改\", function: this.editData},\r\n                    {type: \"repeal\", text: \"作废\", title: \"点击作废\", function: this.delData},\r\n                    {type: \"remove\", text: \"删除\", title: \"点击删除\", function: this.repealData},]\r\n    return {\r\n      autoTableHeight,\r\n      buttons,\r\n      loading: false,\r\n      sellerArr:[],\r\n      currentRow:null,\r\n      personVisible:false,\r\n      yesNoOps:[{ key:1, name: \"是\" }, { key:0, name: \"否\" }],\r\n      multiValuesSellerSku:[],\r\n      popVisibleSellerSku:false,\r\n      popContentSellerSku: undefined,\r\n      multiValuesProductCode:[],\r\n      popVisibleProductCode:false,\r\n      popContentProductCode: undefined,\r\n      importURl: getUrl() + NewApply.path+ '/importFile',\r\n      loginInfo: {\r\n        Accept: 'application/json,text/plain, */*, */*',\r\n        mode: 'cors',\r\n        Authorization: 'Bearer ' + getToken()\r\n      },\r\n      selectData:[],\r\n      statusList:[{\"key\":-1,\"value\":\"全部\"},{\"key\":0,\"value\":\"已申请\"},{\"key\":1,\"value\":\"已刊登\"},{\"key\":2,\"value\":\"已作废\"},{\"key\":3,\"value\":\"部分刊登\"},{\"key\":5,\"value\":\"已驳回\"}, {\"key\":6,\"value\":\"已审核\"}],\r\n      date:[],\r\n      searchForm: {\r\n        startDate:null,\r\n        endDate:null,\r\n        shops: [],\r\n        spus:[],\r\n        status:null,\r\n        page:1,\r\n        limit:10\r\n      },\r\n      tableData: [], //表格数据\r\n      columns: [{type: 'selection',width: 55,},\r\n        {title: '申请日期',key: 'sheetDate',width: 100,resizable:true,render: (_, { row }) => (<span v-copytext={row['sheetDate']}>{row['sheetDate']}</span>)},\r\n        {title: '店铺',key: 'shopName',width: 100,resizable:true,render: (_, { row }) => (<span v-copytext={row['shopName']}>{row['shopName']}</span>)},\r\n        {title: '型号',key: 'spu',minWidth: 150,resizable:true,render: (_, { row }) => (<span v-copytext={row['spu']}>{row['spu']}</span>)},\r\n        {title: '销售人员',key: 'sellerName',minWidth: 100,resizable:true,render: (_, { row }) => (<span v-copytext={row['sellerName']}>{row['sellerName']}</span>)},\r\n        {title: '是否需要UPC',key: 'isUpc',minWidth: 120,resizable:true,slot:'isUpc'},\r\n        {title: '审核阶段',key: 'review',minWidth: 100,resizable:true,slot:'review'},\r\n        {title: '运输模式',key: 'fulfillmentType',minWidth: 100,resizable:true,render: (_, { row }) => (<span v-copytext={row['fulfillmentType']}>{row['fulfillmentType']}</span>)},\r\n        {title: 'Listing简称',key: 'listingTitle',minWidth: 200,resizable:true,render: (_, { row }) => (<span v-copytext={row['listingTitle']}>{row['listingTitle']}</span>)},\r\n        {title: 'sku定位',key: 'skuLoc',minWidth: 200,resizable:true,render: (_, { row }) => (<span v-copytext={row['skuLoc']}>{row['skuLoc']}</span>)},\r\n        {title: \"明细\", align: \"center\", minWidth: 400, key: \"detailList\",slot: 'detailList', className: \"requestDetailColumn\"},\r\n        {title: '使用状态',key: 'status',minWidth: 100,resizable:true,slot:'status'},\r\n        {title: '审批状态',key: 'reviewFlag',minWidth: 100,resizable:true,slot:'reviewStatus'},\r\n        {title: '创建人',key: 'createUserName',width: 100,resizable:true,render: (_, { row }) => (<span v-copytext={row['createUserName']}>{row['createUserName']}</span>)},\r\n        {title: '创建时间',key: 'createTime',width: 160,resizable:true,render:(_, { row }) => (<span v-copytext={row['createTime']}>{row['createTime']}</span>)},\r\n        {title: '操作',key: 'operate',width: 200,resizable:true,slot:\"action\"},\r\n      ],\r\n      popColumns: [\r\n        {title: \"销售SKU\", align: \"center\", minWidth: 300, key: \"sellerSku\",},\r\n        {title: \"产品编码\", align: \"center\", minWidth: 150, key: \"productCode\",},\r\n        {title: \"产品规格\", align: \"center\", minWidth: 300, key: \"productSpec\",},\r\n        {title: \"UPC码\", align: \"center\", minWidth: 150, key: \"upcCode\",},\r\n        {\r\n          title: \"是否刊登\",\r\n          align: \"center\",\r\n          minWidth: 150,\r\n          key: \"status\",\r\n          resizable: true,\r\n          render: (_, { row }) => {\r\n            const statusText = row.status === 1 ? '已刊登' : '未刊登';\r\n            return <span v-copytext={statusText}>{statusText}</span>;\r\n          }\r\n        }\r\n      ],\r\n      editVisible:false,\r\n      editTitle:\"上新管理-新增\",\r\n      // 审核弹窗相关\r\n      reviewModalVisible: false,\r\n      reviewLoading: false,\r\n      reviewForm: {\r\n        id: null,\r\n        spu: '',\r\n        agree: 'true', // 使用字符串，对应Radio的label\r\n        comment: ''\r\n      },\r\n      reviewFormRules: {\r\n        comment: [\r\n          { required: true, message: '请输入审核意见', trigger: 'blur' },\r\n          { min: 1, max: 200, message: '审核意见长度在1到200个字符', trigger: 'blur' }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  //组件初始化进行的操作\r\n  mounted() {\r\n    this.handleSearch();\r\n  },\r\n  methods: {\r\n    handleImportError (err, file) {\r\n      this.loading=false;\r\n      this.$Message.error(file.message);\r\n    },\r\n    handleImportSuccess (res) {\r\n      this.$refs['uploadFileRef'].clearFiles();\r\n      if (res.code === 0) {\r\n        this.handleSearch();\r\n      } else {\r\n        this.$Message.error(res.message);\r\n      }\r\n    },\r\n    handleMaxSize () {\r\n      this.$Message.warning('大小不能超过10M.');\r\n    },\r\n    handleImportFormatError (file) {\r\n      //格式验证失败的钩子\r\n      this.$Modal.error({\r\n        title: '文件格式不正确',\r\n        content: '文件 ' + file.name + '格式不正确，支持上传的文件类型：xls,xlsx',\r\n        okText: '确认'\r\n      });\r\n    },\r\n    openPerson(){\r\n      this.resetMultiple();\r\n      //打开人员选择\r\n      const { personSelectRef } = this.$refs;\r\n      const { sellerArr, searchForm } = this;\r\n      const selectedIds = searchForm.sellers || [];\r\n      if (personSelectRef) personSelectRef.setDefault(\r\n        sellerArr.filter(v => selectedIds.includes(v.id)).map(v=>({ name: v.nickName, id: v.id }))\r\n      );//给组件设置默认选中\r\n      this.personVisible = true;\r\n    },\r\n    // 人员选择相关\r\n    setSelectInfo(info={}){\r\n      this.sellerArr = info.personArr || [];\r\n    },\r\n    clickMore(row = {}) {\r\n      this.currentRow = {...row};\r\n    },\r\n    //操作\r\n    handleDropDownClick(name, row = {}) {\r\n      this.clickRow = { ...row };\r\n      // 检查按钮是否被禁用\r\n      const button = this.buttons.find(btn => btn.type === name);\r\n      if (button && this.isButtonDisabled(button, row)) {\r\n        this.$Message.warning('当前状态下该操作不可用');\r\n        return;\r\n      }\r\n\r\n      switch (name) {\r\n        case \"look\":\r\n          this.lookData(row);\r\n          break;\r\n        case \"edit\":\r\n          this.editData(row);\r\n          break;\r\n        case \"remove\":\r\n          this.delData(row);\r\n          break;\r\n        case \"repeal\":\r\n          this.repealData(row);\r\n          break;\r\n        default:\r\n      }\r\n    },\r\n    // 获取过滤后的按钮列表\r\n    getFilteredButtons() {\r\n      return this.buttons;\r\n    },\r\n    // 判断按钮是否应该被禁用\r\n    isButtonDisabled(button, row) {\r\n      // 当reviewFlag为1时，禁用修改、作废、删除按钮\r\n      if (row.reviewFlag === 1 && row.status !== 5 && row.status !== 6 && row.status !== 2) {\r\n        return ['edit', 'repeal'].includes(button.type);\r\n      }\r\n      return false;\r\n    },\r\n    // 获取审批状态文本\r\n    getReviewStatusText(reviewFlag, status) {\r\n      switch (reviewFlag) {\r\n        case 0:\r\n          return '无需审批';\r\n        case 1:\r\n          if (status === 5) {\r\n            return '已驳回';\r\n          } else if (status === 6) {\r\n            return '已审核';\r\n          } else if (status === 2) {\r\n            return '已作废';\r\n          }\r\n          return '正在审';\r\n        default:\r\n          return '未知状态';\r\n      }\r\n    },\r\n    // 获取审批状态颜色\r\n    getReviewStatusColor(reviewFlag) {\r\n      switch (reviewFlag) {\r\n        case 0:\r\n          return 'default';\r\n        case 1:\r\n          return 'orange';\r\n        default:\r\n          return 'default';\r\n      }\r\n    },\r\n    editData(row){\r\n      // 检查是否被禁用\r\n      // if (row.reviewFlag === 1) {\r\n      //   this.$Message.warning('当前记录正在审批流程中，无法修改');\r\n      //   return;\r\n      // }\r\n      this.editVisible = true;\r\n      const { newApplyEditRef } = this.$refs;\r\n      if (newApplyEditRef) newApplyEditRef.setDefault(row,'Edit');//给组件\r\n    },\r\n    lookData(row){\r\n      this.editVisible = true;\r\n      const { newApplyEditRef } = this.$refs;\r\n      if (newApplyEditRef) newApplyEditRef.setDefault(row,'View');//给组件\r\n    },\r\n    delData(row){\r\n      // 检查是否被禁用\r\n      // if (row.reviewFlag === 1) {\r\n      //   this.$Message.warning('当前记录正在审批流程中，无法删除');\r\n      //   return;\r\n      // }\r\n      this.loading=true;\r\n      this.$Modal.confirm({\r\n        title: '提示',\r\n        content: '您确认要删除这些数据吗？',\r\n        onOk: () => {\r\n          NewApply.remove({\"ids\":row.id}).then(res=>{\r\n            if(res && res['code'] === 0){\r\n              this.handleSearch();\r\n            }else{\r\n              this.$message.error(res['message']);\r\n            }\r\n          }).finally(()=>{this.loading=false;})\r\n        },\r\n      })\r\n    },\r\n    repealData(row){\r\n      // 检查是否被禁用\r\n      // if (row.reviewFlag === 1) {\r\n      //   this.$Message.warning('当前记录正在审批流程中，无法作废');\r\n      //   return;\r\n      // }\r\n      this.loading=true;\r\n      NewApply.repeal({\"ids\":row.id}).then(res=>{\r\n        if(res && res['code'] === 0){\r\n          this.handleSearch();\r\n        }else{\r\n          this.$message.error(res['message']);\r\n        }\r\n      }).finally(()=>{this.loading=false;})\r\n    },\r\n    closeDropdownSellerSku() { //关闭输入文本框\r\n      const { popContentSellerSku } = this;\r\n      const { multipleRefSellerSkuRef } = this.$refs;\r\n      this.popVisibleSellerSku = false;\r\n      if(!popContentSellerSku) return;\r\n      const content = popContentSellerSku ? popContentSellerSku.trim().replace(/，/g, \",\") : '';\r\n      this.multiValuesSellerSku = content.split('\\n').filter(v=>!!v);\r\n      this.multiValuesSellerSku = [...new Set(this.multiValuesSellerSku)];\r\n      if(multipleRefSellerSkuRef && multipleRefSellerSkuRef.setValueArray){\r\n        multipleRefSellerSkuRef.setValueArray(this.multiValuesSellerSku);\r\n      }\r\n    },\r\n    closeDropdownProductCode() { //关闭输入文本框\r\n      const { popContentProductCode } = this;\r\n      const { multipleRefProductCodeRef } = this.$refs;\r\n      this.popVisibleProductCode = false;\r\n      if(!popContentProductCode) return;\r\n      const content = popContentProductCode ? popContentProductCode.trim().replace(/，/g, \",\") : '';\r\n      this.multiValuesProductCode = content.split('\\n').filter(v=>!!v);\r\n      this.multiValuesProductCode = [...new Set(this.multiValuesProductCode)];\r\n      if(multipleRefProductCodeRef && multipleRefProductCodeRef.setValueArray){\r\n        multipleRefProductCodeRef.setValueArray(this.multiValuesProductCode);\r\n      }\r\n    },\r\n    getParam(){\r\n      const params = {\r\n        ...this.searchForm\r\n      };\r\n      delete params.shops;\r\n      const getStr = value => value && Array.isArray(value) ? value.join(\"&#&\") : undefined;\r\n      if (this.multiValuesSellerSku.length > 0){\r\n        params[\"sellerSkus\"] = getStr(this.multiValuesSellerSku);\r\n      }\r\n      if (this.multiValuesProductCode.length > 0){\r\n        params[\"productCodes\"] = getStr(this.multiValuesProductCode);\r\n      }\r\n      params[\"sellerIds\"] = getStr(this.searchForm.sellers);\r\n      params[\"shopIds\"] = getStr(this.searchForm.shops);\r\n      params[\"spus\"] = getStr(this.searchForm.spus);\r\n      return params;\r\n    },\r\n    dateChange(date) {\r\n      if (isEmpty(date)) {\r\n        this.searchForm.startDate = '';\r\n        this.searchForm.endDate = '';\r\n      } else {\r\n        this.searchForm.startDate = date[0];\r\n        this.searchForm.endDate = date[1];\r\n      }\r\n    },\r\n    //查询\r\n    handleSearch() {\r\n      const params = this.getParam();\r\n      this.loading = true\r\n      NewApply.listPage(params).then(res => {\r\n        if (res && res['code'] === 0) {\r\n          this.tableData = res.data.records;\r\n          // 如果tableData[i].reviewFlag === 1 && tableData[i].status !== 6,将tableData[i].detailList中的所有数据的sellerSku设为null\r\n          this.tableData.forEach((item) => {\r\n            if (item.reviewFlag === 1 && item.status !== 6) {\r\n              if (item.detailList && Array.isArray(item.detailList)) {\r\n                item.detailList.forEach(detail => {\r\n                  detail.sellerSku = null;\r\n                });\r\n              }\r\n            }\r\n          });\r\n          this.searchForm.total = parseInt(res.data.total);\r\n        }\r\n      }).finally(() => {\r\n        this.loading = false\r\n      })\r\n    },\r\n    handleReset() {\r\n      //重置验证\r\n      this.$refs['searchFormRef'].resetFields();\r\n      this.searchForm.shops=[];\r\n      this.searchForm.spus=[];\r\n      this.searchForm.startDate=null;\r\n      this.searchForm.endDate=null;\r\n      this.date = [];\r\n      this.resetMultiple(true);\r\n    },\r\n    resetMultiple(clearTxt = false) {\r\n      if (clearTxt === true) {\r\n        this.multiValuesSellerSku = [];\r\n        const { multipleRefSellerSkuRef } = this.$refs;\r\n        if (multipleRefSellerSkuRef && multipleRefSellerSkuRef.setValueArray) {\r\n          multipleRefSellerSkuRef.setValueArray([]);\r\n        }\r\n        this.multiValuesProductCode = [];\r\n        const { multipleRefProductCodeRef } = this.$refs;\r\n        if (multipleRefProductCodeRef && multipleRefProductCodeRef.setValueArray) {\r\n          multipleRefProductCodeRef.setValueArray([]);\r\n        }\r\n      }\r\n      this.popContentSellerSku = undefined;\r\n      this.popVisibleSellerSku = false;\r\n      this.popContentProductCode = undefined;\r\n      this.popVisibleProductCode = false;\r\n    },\r\n    handlePage(current) {\r\n      this.searchForm.page = current\r\n      this.handleSearch()\r\n    },\r\n    handlePageSize(size) {\r\n      this.searchForm.limit = size\r\n      this.handleSearch()\r\n    },\r\n    onSelectChange(selection){\r\n      this.selectData = selection;\r\n    },\r\n    downTemplate() {\r\n      this.loading = true;\r\n      let params = {};\r\n      params['fileName'] = \"上新申请导入模板.xls\";\r\n      NewApply.downTemplate(params, () => {\r\n        this.loading = false\r\n      });\r\n    },\r\n    handleDel(){\r\n      let ids = this.selectData.map(item => item['id']).join(',');\r\n      if (!ids) {\r\n        this.$message.error(\"请选择需要作废的记录\");\r\n        return;\r\n      }\r\n      let sellerSkuUsed = this.selectData.filter(item => item['status'] >= 2).map(item=>item['sellerSku']).join(',');\r\n      this.$Modal.confirm({\r\n        title: '提示',\r\n        content: '您确认要删除这些数据吗？'+(!!sellerSkuUsed?(\"并且存在已匹配的销售SKU\"+sellerSkuUsed):\"\"),\r\n        onOk: () => {\r\n          this.loading = true;\r\n          NewApply.remove({ids: ids}).then(res => {\r\n            if (res['code'] === 0) {\r\n              this.$Message.success('删除成功!');\r\n              this.handleSearch();\r\n            } else {\r\n              this.$Message.error(res['message']);\r\n            }\r\n          }).finally(()=>this.loading=false)\r\n        },\r\n      })\r\n    },\r\n    handleExport(){\r\n      this.loading = true;\r\n      let params = this.getParam();\r\n      params['fileName'] = \"上新申请_\" + new Date().getExportFormat() + \".xls\";\r\n      NewApply.exportFile(params, () => {\r\n        this.loading = false\r\n      });\r\n    },\r\n    addNewApply(){\r\n      this.editVisible=true;\r\n      const { newApplyEditRef } = this.$refs;\r\n      if (newApplyEditRef) newApplyEditRef.setDefault(null,'Add');//给组件\r\n    },\r\n    noticeSellerId(){\r\n      let ids = this.selectData.map(item => item['id']).join(',');\r\n      if (!ids) {\r\n        this.$message.error(\"请选择需要通知运营的记录\");\r\n        return;\r\n      }\r\n      this.loading = true;\r\n      NewApply.noticeSeller({ids: ids}).then(res => {\r\n        if (res['code'] === 0) {\r\n          this.$Message.success('通知成功!');\r\n        } else {\r\n          this.$Message.error(res['message']);\r\n        }\r\n      }).finally(()=>this.loading=false)\r\n    },\r\n    refreshStatus(){\r\n      let ids = this.selectData.map(item => item['id']).join(',');\r\n      this.loading = true;\r\n      NewApply.refreshStatus({ids: ids}).then(res => {\r\n        if (res['code'] === 0) {\r\n          this.$Message.success('运行成功!');\r\n        } else {\r\n          this.$Message.error(res['message']);\r\n        }\r\n      }).finally(()=>this.loading=false)\r\n    },\r\n    // 审批申请处理\r\n    handleApply(row) {\r\n      this.$Modal.confirm({\r\n        title: '确认申请',\r\n        content: `确定要为型号 \"${row.spu}\" 申请审批吗？`,\r\n        onOk: () => {\r\n          this.$set(row, 'reviewLoading', true);\r\n          // 这里调用审批申请的API\r\n          NewApply.apply({ id: row.id }).then(res => {\r\n            if (res && res.code === 0) {\r\n              this.$Message.success('审批申请提交成功！');\r\n              this.handleSearch(); // 刷新列表\r\n            } else {\r\n              this.$Message.error(res.message || '审批申请失败');\r\n            }\r\n          }).catch(err => {\r\n            this.$Message.error('审批申请失败：' + (err.message || '网络错误'));\r\n          }).finally(() => {\r\n            this.$set(row, 'reviewLoading', false);\r\n          });\r\n        }\r\n      });\r\n    },\r\n    // 审批审核处理\r\n    handleReview(row) {\r\n      // 显示审核弹窗\r\n      this.showReviewModal(row);\r\n    },\r\n\r\n    showReviewModal(row) {\r\n      this.reviewForm = {\r\n        id: row.id,\r\n        spu: row.spu,\r\n        agree: 'true', // 默认选择通过，使用字符串\r\n        comment: '' // 审核意见\r\n      };\r\n      this.reviewModalVisible = true;\r\n    },\r\n\r\n    handleReviewSubmit() {\r\n      this.$refs.reviewForm.validate((valid) => {\r\n        if (valid) {\r\n          this.reviewLoading = true;\r\n          const params = {\r\n            id: this.reviewForm.id,\r\n            variables: {\r\n              agree: this.reviewForm.agree === 'true', // 转换为布尔值\r\n              comment: this.reviewForm.comment\r\n            }\r\n          };\r\n\r\n          Workflow.review(params).then(res => {\r\n            if (res && res.code === 0) {\r\n              this.$Message.success('审批审核完成！');\r\n              this.reviewModalVisible = false;\r\n              this.handleSearch(); // 刷新列表\r\n            } else {\r\n              this.$Message.error(res.message || '审批审核失败');\r\n            }\r\n          }).catch(err => {\r\n            this.$Message.error('审批审核失败：' + (err.message || '网络错误'));\r\n          }).finally(() => {\r\n            this.reviewLoading = false;\r\n          });\r\n        }\r\n      });\r\n    },\r\n\r\n    handleReviewCancel() {\r\n      this.reviewModalVisible = false;\r\n      this.reviewForm = {\r\n        id: null,\r\n        spu: '',\r\n        agree: 'true', // 使用字符串\r\n        comment: ''\r\n      };\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style lang=\"less\">\r\n.search-con-top{\r\n  position: relative;\r\n  padding: 0;\r\n  //标签、sku、asin搜索项\r\n  .multiClass{\r\n    .flex-h{\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      margin-top:2px;\r\n    }\r\n  }\r\n}\r\n.widthClass {\r\n  width: 350px\r\n}\r\n.buttonMargin{\r\n  margin-left:15px;\r\n}\r\n.salesRank {\r\n  .reportTable {\r\n    .requestDetailColumn {\r\n      .ivu-table-cell {\r\n        padding-left: 2px;\r\n        padding-right: 2px;\r\n\r\n        .requestDetailTd {\r\n          padding: 0 0;\r\n\r\n          .ivu-row {\r\n            border-bottom: 1px solid #e8eaec;\r\n\r\n            &:last-child {\r\n              border-bottom: none;\r\n            }\r\n\r\n            .ivu-col {\r\n              border-right: 1px solid #e8eaec;\r\n              padding: 3px 2px;\r\n              white-space: nowrap;\r\n              overflow: hidden;\r\n              text-overflow: ellipsis;\r\n\r\n              &:last-child {\r\n                border-right: none;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;AAwNA,SAAAA,eAAA,EAAAC,OAAA;AACA,OAAAC,UAAA;AACA,OAAAC,SAAA;AACA,OAAAC,QAAA;AACA,OAAAC,YAAA;AACA,OAAAC,QAAA;AACA,OAAAC,QAAA;AACA,OAAAC,YAAA;AACA,SAAAC,QAAA,EAAAC,MAAA;AACA;EACAC,IAAA;EACAC,UAAA;IAAAP,YAAA,EAAAA,YAAA;IAAAD,QAAA,EAAAA,QAAA;IAAAF,UAAA,EAAAA,UAAA;IAAAC,SAAA,EAAAA,SAAA;IAAAK,YAAA,EAAAA;EAAA;EACAK,IAAA,WAAAA,KAAA;IAAA,IAAAC,CAAA,QAAAC,cAAA;IACA,IAAAC,OAAA;MAAAC,IAAA;MAAAC,IAAA;MAAAC,KAAA;MAAAC,QAAA,OAAAC;IAAA,GACA;MAAAJ,IAAA;MAAAC,IAAA;MAAAC,KAAA;MAAAC,QAAA,OAAAE;IAAA,GACA;MAAAL,IAAA;MAAAC,IAAA;MAAAC,KAAA;MAAAC,QAAA,OAAAG;IAAA,GACA;MAAAN,IAAA;MAAAC,IAAA;MAAAC,KAAA;MAAAC,QAAA,OAAAI;IAAA;IACA;MACAxB,eAAA,EAAAA,eAAA;MACAgB,OAAA,EAAAA,OAAA;MACAS,OAAA;MACAC,SAAA;MACAC,UAAA;MACAC,aAAA;MACAC,QAAA;QAAAC,GAAA;QAAAnB,IAAA;MAAA;QAAAmB,GAAA;QAAAnB,IAAA;MAAA;MACAoB,oBAAA;MACAC,mBAAA;MACAC,mBAAA,EAAAC,SAAA;MACAC,sBAAA;MACAC,qBAAA;MACAC,qBAAA,EAAAH,SAAA;MACAI,SAAA,EAAA5B,MAAA,KAAAJ,QAAA,CAAAiC,IAAA;MACAC,SAAA;QACAC,MAAA;QACAC,IAAA;QACAC,aAAA,cAAAlC,QAAA;MACA;MACAmC,UAAA;MACAC,UAAA;QAAA;QAAA;MAAA;QAAA;QAAA;MAAA;QAAA;QAAA;MAAA;QAAA;QAAA;MAAA;QAAA;QAAA;MAAA;QAAA;QAAA;MAAA;QAAA;QAAA;MAAA;MACAC,IAAA;MACAC,UAAA;QACAC,SAAA;QACAC,OAAA;QACAC,KAAA;QACAC,IAAA;QACAC,MAAA;QACAC,IAAA;QACAC,KAAA;MACA;MACAC,SAAA;MAAA;MACAC,OAAA;QAAAvC,IAAA;QAAAwC,KAAA;MAAA,GACA;QAAAtC,KAAA;QAAAW,GAAA;QAAA2B,KAAA;QAAAC,SAAA;QAAAC,MAAA,WAAAA,OAAAC,CAAA,EAAAC,IAAA;UAAA,IAAAC,GAAA,GAAAD,IAAA,CAAAC,GAAA;UAAA,OAAAhD,CAAA;YAAA;cAAAH,IAAA;cAAAoD,KAAA,EAAAD,GAAA;YAAA;UAAA,IAAAA,GAAA;QAAA;MAAA,GACA;QAAA3C,KAAA;QAAAW,GAAA;QAAA2B,KAAA;QAAAC,SAAA;QAAAC,MAAA,WAAAA,OAAAC,CAAA,EAAAI,KAAA;UAAA,IAAAF,GAAA,GAAAE,KAAA,CAAAF,GAAA;UAAA,OAAAhD,CAAA;YAAA;cAAAH,IAAA;cAAAoD,KAAA,EAAAD,GAAA;YAAA;UAAA,IAAAA,GAAA;QAAA;MAAA,GACA;QAAA3C,KAAA;QAAAW,GAAA;QAAAmC,QAAA;QAAAP,SAAA;QAAAC,MAAA,WAAAA,OAAAC,CAAA,EAAAM,KAAA;UAAA,IAAAJ,GAAA,GAAAI,KAAA,CAAAJ,GAAA;UAAA,OAAAhD,CAAA;YAAA;cAAAH,IAAA;cAAAoD,KAAA,EAAAD,GAAA;YAAA;UAAA,IAAAA,GAAA;QAAA;MAAA,GACA;QAAA3C,KAAA;QAAAW,GAAA;QAAAmC,QAAA;QAAAP,SAAA;QAAAC,MAAA,WAAAA,OAAAC,CAAA,EAAAO,KAAA;UAAA,IAAAL,GAAA,GAAAK,KAAA,CAAAL,GAAA;UAAA,OAAAhD,CAAA;YAAA;cAAAH,IAAA;cAAAoD,KAAA,EAAAD,GAAA;YAAA;UAAA,IAAAA,GAAA;QAAA;MAAA,GACA;QAAA3C,KAAA;QAAAW,GAAA;QAAAmC,QAAA;QAAAP,SAAA;QAAAU,IAAA;MAAA,GACA;QAAAjD,KAAA;QAAAW,GAAA;QAAAmC,QAAA;QAAAP,SAAA;QAAAU,IAAA;MAAA,GACA;QAAAjD,KAAA;QAAAW,GAAA;QAAAmC,QAAA;QAAAP,SAAA;QAAAC,MAAA,WAAAA,OAAAC,CAAA,EAAAS,KAAA;UAAA,IAAAP,GAAA,GAAAO,KAAA,CAAAP,GAAA;UAAA,OAAAhD,CAAA;YAAA;cAAAH,IAAA;cAAAoD,KAAA,EAAAD,GAAA;YAAA;UAAA,IAAAA,GAAA;QAAA;MAAA,GACA;QAAA3C,KAAA;QAAAW,GAAA;QAAAmC,QAAA;QAAAP,SAAA;QAAAC,MAAA,WAAAA,OAAAC,CAAA,EAAAU,KAAA;UAAA,IAAAR,GAAA,GAAAQ,KAAA,CAAAR,GAAA;UAAA,OAAAhD,CAAA;YAAA;cAAAH,IAAA;cAAAoD,KAAA,EAAAD,GAAA;YAAA;UAAA,IAAAA,GAAA;QAAA;MAAA,GACA;QAAA3C,KAAA;QAAAW,GAAA;QAAAmC,QAAA;QAAAP,SAAA;QAAAC,MAAA,WAAAA,OAAAC,CAAA,EAAAW,KAAA;UAAA,IAAAT,GAAA,GAAAS,KAAA,CAAAT,GAAA;UAAA,OAAAhD,CAAA;YAAA;cAAAH,IAAA;cAAAoD,KAAA,EAAAD,GAAA;YAAA;UAAA,IAAAA,GAAA;QAAA;MAAA,GACA;QAAA3C,KAAA;QAAAqD,KAAA;QAAAP,QAAA;QAAAnC,GAAA;QAAAsC,IAAA;QAAAK,SAAA;MAAA,GACA;QAAAtD,KAAA;QAAAW,GAAA;QAAAmC,QAAA;QAAAP,SAAA;QAAAU,IAAA;MAAA,GACA;QAAAjD,KAAA;QAAAW,GAAA;QAAAmC,QAAA;QAAAP,SAAA;QAAAU,IAAA;MAAA,GACA;QAAAjD,KAAA;QAAAW,GAAA;QAAA2B,KAAA;QAAAC,SAAA;QAAAC,MAAA,WAAAA,OAAAC,CAAA,EAAAc,KAAA;UAAA,IAAAZ,GAAA,GAAAY,KAAA,CAAAZ,GAAA;UAAA,OAAAhD,CAAA;YAAA;cAAAH,IAAA;cAAAoD,KAAA,EAAAD,GAAA;YAAA;UAAA,IAAAA,GAAA;QAAA;MAAA,GACA;QAAA3C,KAAA;QAAAW,GAAA;QAAA2B,KAAA;QAAAC,SAAA;QAAAC,MAAA,WAAAA,OAAAC,CAAA,EAAAe,KAAA;UAAA,IAAAb,GAAA,GAAAa,KAAA,CAAAb,GAAA;UAAA,OAAAhD,CAAA;YAAA;cAAAH,IAAA;cAAAoD,KAAA,EAAAD,GAAA;YAAA;UAAA,IAAAA,GAAA;QAAA;MAAA,GACA;QAAA3C,KAAA;QAAAW,GAAA;QAAA2B,KAAA;QAAAC,SAAA;QAAAU,IAAA;MAAA,EACA;MACAQ,UAAA,GACA;QAAAzD,KAAA;QAAAqD,KAAA;QAAAP,QAAA;QAAAnC,GAAA;MAAA,GACA;QAAAX,KAAA;QAAAqD,KAAA;QAAAP,QAAA;QAAAnC,GAAA;MAAA,GACA;QAAAX,KAAA;QAAAqD,KAAA;QAAAP,QAAA;QAAAnC,GAAA;MAAA,GACA;QAAAX,KAAA;QAAAqD,KAAA;QAAAP,QAAA;QAAAnC,GAAA;MAAA,GACA;QACAX,KAAA;QACAqD,KAAA;QACAP,QAAA;QACAnC,GAAA;QACA4B,SAAA;QACAC,MAAA,WAAAA,OAAAC,CAAA,EAAAiB,MAAA;UAAA,IAAAf,GAAA,GAAAe,MAAA,CAAAf,GAAA;UACA,IAAAgB,UAAA,GAAAhB,GAAA,CAAAV,MAAA;UACA,OAAAtC,CAAA;YAAA;cAAAH,IAAA;cAAAoD,KAAA,EAAAe;YAAA;UAAA,IAAAA,UAAA;QACA;MACA,EACA;MACAC,WAAA;MACAC,SAAA;MACA;MACAC,kBAAA;MACAC,aAAA;MACAC,UAAA;QACAC,EAAA;QACAC,GAAA;QACAC,KAAA;QAAA;QACAC,OAAA;MACA;MACAC,eAAA;QACAD,OAAA,GACA;UAAAE,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACA;EACAG,OAAA,WAAAA,QAAA;IACA,KAAAC,YAAA;EACA;EACAC,OAAA;IACAC,iBAAA,WAAAA,kBAAAC,GAAA,EAAAC,IAAA;MACA,KAAA1E,OAAA;MACA,KAAA2E,QAAA,CAAAC,KAAA,CAAAF,IAAA,CAAAT,OAAA;IACA;IACAY,mBAAA,WAAAA,oBAAAC,GAAA;MACA,KAAAC,KAAA,kBAAAC,UAAA;MACA,IAAAF,GAAA,CAAAG,IAAA;QACA,KAAAX,YAAA;MACA;QACA,KAAAK,QAAA,CAAAC,KAAA,CAAAE,GAAA,CAAAb,OAAA;MACA;IACA;IACAiB,aAAA,WAAAA,cAAA;MACA,KAAAP,QAAA,CAAAQ,OAAA;IACA;IACAC,uBAAA,WAAAA,wBAAAV,IAAA;MACA;MACA,KAAAW,MAAA,CAAAT,KAAA;QACAlF,KAAA;QACA4F,OAAA,UAAAZ,IAAA,CAAAxF,IAAA;QACAqG,MAAA;MACA;IACA;IACAC,UAAA,WAAAA,WAAA;MACA,KAAAC,aAAA;MACA;MACA,IAAAC,eAAA,QAAAX,KAAA,CAAAW,eAAA;MACA,IAAAzF,SAAA,QAAAA,SAAA;QAAAqB,UAAA,QAAAA,UAAA;MACA,IAAAqE,WAAA,GAAArE,UAAA,CAAAsE,OAAA;MACA,IAAAF,eAAA,EAAAA,eAAA,CAAAG,UAAA,CACA5F,SAAA,CAAA6F,MAAA,WAAAC,CAAA;QAAA,OAAAJ,WAAA,CAAAK,QAAA,CAAAD,CAAA,CAAApC,EAAA;MAAA,GAAAsC,GAAA,WAAAF,CAAA;QAAA;UAAA7G,IAAA,EAAA6G,CAAA,CAAAG,QAAA;UAAAvC,EAAA,EAAAoC,CAAA,CAAApC;QAAA;MAAA,EACA;MACA,KAAAxD,aAAA;IACA;IACA;IACAgG,aAAA,WAAAA,cAAA;MAAA,IAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAA5F,SAAA,GAAA4F,SAAA;MACA,KAAApG,SAAA,GAAAmG,IAAA,CAAAG,SAAA;IACA;IACAC,SAAA,WAAAA,UAAA;MAAA,IAAAnE,GAAA,GAAAgE,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAA5F,SAAA,GAAA4F,SAAA;MACA,KAAAnG,UAAA,GAAAuG,aAAA,KAAApE,GAAA;IACA;IACA;IACAqE,mBAAA,WAAAA,oBAAAxH,IAAA;MAAA,IAAAmD,GAAA,GAAAgE,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAA5F,SAAA,GAAA4F,SAAA;MACA,KAAAM,QAAA,GAAAF,aAAA,KAAApE,GAAA;MACA;MACA,IAAAuE,MAAA,QAAArH,OAAA,CAAAsH,IAAA,WAAAC,GAAA;QAAA,OAAAA,GAAA,CAAAtH,IAAA,KAAAN,IAAA;MAAA;MACA,IAAA0H,MAAA,SAAAG,gBAAA,CAAAH,MAAA,EAAAvE,GAAA;QACA,KAAAsC,QAAA,CAAAQ,OAAA;QACA;MACA;MAEA,QAAAjG,IAAA;QACA;UACA,KAAAU,QAAA,CAAAyC,GAAA;UACA;QACA;UACA,KAAAxC,QAAA,CAAAwC,GAAA;UACA;QACA;UACA,KAAAvC,OAAA,CAAAuC,GAAA;UACA;QACA;UACA,KAAAtC,UAAA,CAAAsC,GAAA;UACA;QACA;MACA;IACA;IACA;IACA2E,kBAAA,WAAAA,mBAAA;MACA,YAAAzH,OAAA;IACA;IACA;IACAwH,gBAAA,WAAAA,iBAAAH,MAAA,EAAAvE,GAAA;MACA;MACA,IAAAA,GAAA,CAAA4E,UAAA,UAAA5E,GAAA,CAAAV,MAAA,UAAAU,GAAA,CAAAV,MAAA,UAAAU,GAAA,CAAAV,MAAA;QACA,0BAAAqE,QAAA,CAAAY,MAAA,CAAApH,IAAA;MACA;MACA;IACA;IACA;IACA0H,mBAAA,WAAAA,oBAAAD,UAAA,EAAAtF,MAAA;MACA,QAAAsF,UAAA;QACA;UACA;QACA;UACA,IAAAtF,MAAA;YACA;UACA,WAAAA,MAAA;YACA;UACA,WAAAA,MAAA;YACA;UACA;UACA;QACA;UACA;MACA;IACA;IACA;IACAwF,oBAAA,WAAAA,qBAAAF,UAAA;MACA,QAAAA,UAAA;QACA;UACA;QACA;UACA;QACA;UACA;MACA;IACA;IACApH,QAAA,WAAAA,SAAAwC,GAAA;MACA;MACA;MACA;MACA;MACA;MACA,KAAAiB,WAAA;MACA,IAAA8D,eAAA,QAAArC,KAAA,CAAAqC,eAAA;MACA,IAAAA,eAAA,EAAAA,eAAA,CAAAvB,UAAA,CAAAxD,GAAA;IACA;IACAzC,QAAA,WAAAA,SAAAyC,GAAA;MACA,KAAAiB,WAAA;MACA,IAAA8D,eAAA,QAAArC,KAAA,CAAAqC,eAAA;MACA,IAAAA,eAAA,EAAAA,eAAA,CAAAvB,UAAA,CAAAxD,GAAA;IACA;IACAvC,OAAA,WAAAA,QAAAuC,GAAA;MAAA,IAAAgF,KAAA;MACA;MACA;MACA;MACA;MACA;MACA,KAAArH,OAAA;MACA,KAAAqF,MAAA,CAAAiC,OAAA;QACA5H,KAAA;QACA4F,OAAA;QACAiC,IAAA,WAAAA,KAAA;UACA1I,QAAA,CAAA2I,MAAA;YAAA,OAAAnF,GAAA,CAAAsB;UAAA,GAAA8D,IAAA,WAAA3C,GAAA;YACA,IAAAA,GAAA,IAAAA,GAAA;cACAuC,KAAA,CAAA/C,YAAA;YACA;cACA+C,KAAA,CAAAK,QAAA,CAAA9C,KAAA,CAAAE,GAAA;YACA;UACA,GAAA6C,OAAA;YAAAN,KAAA,CAAArH,OAAA;UAAA;QACA;MACA;IACA;IACAD,UAAA,WAAAA,WAAAsC,GAAA;MAAA,IAAAuF,MAAA;MACA;MACA;MACA;MACA;MACA;MACA,KAAA5H,OAAA;MACAnB,QAAA,CAAAgJ,MAAA;QAAA,OAAAxF,GAAA,CAAAsB;MAAA,GAAA8D,IAAA,WAAA3C,GAAA;QACA,IAAAA,GAAA,IAAAA,GAAA;UACA8C,MAAA,CAAAtD,YAAA;QACA;UACAsD,MAAA,CAAAF,QAAA,CAAA9C,KAAA,CAAAE,GAAA;QACA;MACA,GAAA6C,OAAA;QAAAC,MAAA,CAAA5H,OAAA;MAAA;IACA;IACA8H,sBAAA,WAAAA,uBAAA;MAAA;MACA,IAAAtH,mBAAA,QAAAA,mBAAA;MACA,IAAAuH,uBAAA,QAAAhD,KAAA,CAAAgD,uBAAA;MACA,KAAAxH,mBAAA;MACA,KAAAC,mBAAA;MACA,IAAA8E,OAAA,GAAA9E,mBAAA,GAAAA,mBAAA,CAAAwH,IAAA,GAAAC,OAAA;MACA,KAAA3H,oBAAA,GAAAgF,OAAA,CAAA4C,KAAA,OAAApC,MAAA,WAAAC,CAAA;QAAA,SAAAA,CAAA;MAAA;MACA,KAAAzF,oBAAA,GAAA6H,kBAAA,KAAAC,GAAA,MAAA9H,oBAAA;MACA,IAAAyH,uBAAA,IAAAA,uBAAA,CAAAM,aAAA;QACAN,uBAAA,CAAAM,aAAA,MAAA/H,oBAAA;MACA;IACA;IACAgI,wBAAA,WAAAA,yBAAA;MAAA;MACA,IAAA1H,qBAAA,QAAAA,qBAAA;MACA,IAAA2H,yBAAA,QAAAxD,KAAA,CAAAwD,yBAAA;MACA,KAAA5H,qBAAA;MACA,KAAAC,qBAAA;MACA,IAAA0E,OAAA,GAAA1E,qBAAA,GAAAA,qBAAA,CAAAoH,IAAA,GAAAC,OAAA;MACA,KAAAvH,sBAAA,GAAA4E,OAAA,CAAA4C,KAAA,OAAApC,MAAA,WAAAC,CAAA;QAAA,SAAAA,CAAA;MAAA;MACA,KAAArF,sBAAA,GAAAyH,kBAAA,KAAAC,GAAA,MAAA1H,sBAAA;MACA,IAAA6H,yBAAA,IAAAA,yBAAA,CAAAF,aAAA;QACAE,yBAAA,CAAAF,aAAA,MAAA3H,sBAAA;MACA;IACA;IACA8H,QAAA,WAAAA,SAAA;MACA,IAAAC,MAAA,GAAAhC,aAAA,KACA,KAAAnF,UAAA,CACA;MACA,OAAAmH,MAAA,CAAAhH,KAAA;MACA,IAAAiH,MAAA,YAAAA,OAAApG,KAAA;QAAA,OAAAA,KAAA,IAAAqG,KAAA,CAAAC,OAAA,CAAAtG,KAAA,IAAAA,KAAA,CAAAuG,IAAA,UAAApI,SAAA;MAAA;MACA,SAAAH,oBAAA,CAAAgG,MAAA;QACAmC,MAAA,iBAAAC,MAAA,MAAApI,oBAAA;MACA;MACA,SAAAI,sBAAA,CAAA4F,MAAA;QACAmC,MAAA,mBAAAC,MAAA,MAAAhI,sBAAA;MACA;MACA+H,MAAA,gBAAAC,MAAA,MAAApH,UAAA,CAAAsE,OAAA;MACA6C,MAAA,cAAAC,MAAA,MAAApH,UAAA,CAAAG,KAAA;MACAgH,MAAA,WAAAC,MAAA,MAAApH,UAAA,CAAAI,IAAA;MACA,OAAA+G,MAAA;IACA;IACAK,UAAA,WAAAA,WAAAzH,IAAA;MACA,IAAA7C,OAAA,CAAA6C,IAAA;QACA,KAAAC,UAAA,CAAAC,SAAA;QACA,KAAAD,UAAA,CAAAE,OAAA;MACA;QACA,KAAAF,UAAA,CAAAC,SAAA,GAAAF,IAAA;QACA,KAAAC,UAAA,CAAAE,OAAA,GAAAH,IAAA;MACA;IACA;IACA;IACAiD,YAAA,WAAAA,aAAA;MAAA,IAAAyE,MAAA;MACA,IAAAN,MAAA,QAAAD,QAAA;MACA,KAAAxI,OAAA;MACAnB,QAAA,CAAAmK,QAAA,CAAAP,MAAA,EAAAhB,IAAA,WAAA3C,GAAA;QACA,IAAAA,GAAA,IAAAA,GAAA;UACAiE,MAAA,CAAAjH,SAAA,GAAAgD,GAAA,CAAA1F,IAAA,CAAA6J,OAAA;UACA;UACAF,MAAA,CAAAjH,SAAA,CAAAoH,OAAA,WAAAC,IAAA;YACA,IAAAA,IAAA,CAAAlC,UAAA,UAAAkC,IAAA,CAAAxH,MAAA;cACA,IAAAwH,IAAA,CAAAC,UAAA,IAAAT,KAAA,CAAAC,OAAA,CAAAO,IAAA,CAAAC,UAAA;gBACAD,IAAA,CAAAC,UAAA,CAAAF,OAAA,WAAAG,MAAA;kBACAA,MAAA,CAAAC,SAAA;gBACA;cACA;YACA;UACA;UACAP,MAAA,CAAAzH,UAAA,CAAAiI,KAAA,GAAAC,QAAA,CAAA1E,GAAA,CAAA1F,IAAA,CAAAmK,KAAA;QACA;MACA,GAAA5B,OAAA;QACAoB,MAAA,CAAA/I,OAAA;MACA;IACA;IACAyJ,WAAA,WAAAA,YAAA;MACA;MACA,KAAA1E,KAAA,kBAAA2E,WAAA;MACA,KAAApI,UAAA,CAAAG,KAAA;MACA,KAAAH,UAAA,CAAAI,IAAA;MACA,KAAAJ,UAAA,CAAAC,SAAA;MACA,KAAAD,UAAA,CAAAE,OAAA;MACA,KAAAH,IAAA;MACA,KAAAoE,aAAA;IACA;IACAA,aAAA,WAAAA,cAAA;MAAA,IAAAkE,QAAA,GAAAtD,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAA5F,SAAA,GAAA4F,SAAA;MACA,IAAAsD,QAAA;QACA,KAAArJ,oBAAA;QACA,IAAAyH,uBAAA,QAAAhD,KAAA,CAAAgD,uBAAA;QACA,IAAAA,uBAAA,IAAAA,uBAAA,CAAAM,aAAA;UACAN,uBAAA,CAAAM,aAAA;QACA;QACA,KAAA3H,sBAAA;QACA,IAAA6H,yBAAA,QAAAxD,KAAA,CAAAwD,yBAAA;QACA,IAAAA,yBAAA,IAAAA,yBAAA,CAAAF,aAAA;UACAE,yBAAA,CAAAF,aAAA;QACA;MACA;MACA,KAAA7H,mBAAA,GAAAC,SAAA;MACA,KAAAF,mBAAA;MACA,KAAAK,qBAAA,GAAAH,SAAA;MACA,KAAAE,qBAAA;IACA;IACAiJ,UAAA,WAAAA,WAAAC,OAAA;MACA,KAAAvI,UAAA,CAAAM,IAAA,GAAAiI,OAAA;MACA,KAAAvF,YAAA;IACA;IACAwF,cAAA,WAAAA,eAAAC,IAAA;MACA,KAAAzI,UAAA,CAAAO,KAAA,GAAAkI,IAAA;MACA,KAAAzF,YAAA;IACA;IACA0F,cAAA,WAAAA,eAAAC,SAAA;MACA,KAAA9I,UAAA,GAAA8I,SAAA;IACA;IACAC,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAAnK,OAAA;MACA,IAAAyI,MAAA;MACAA,MAAA;MACA5J,QAAA,CAAAqL,YAAA,CAAAzB,MAAA;QACA0B,MAAA,CAAAnK,OAAA;MACA;IACA;IACAoK,SAAA,WAAAA,UAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,GAAA,QAAAnJ,UAAA,CAAA8E,GAAA,WAAAkD,IAAA;QAAA,OAAAA,IAAA;MAAA,GAAAN,IAAA;MACA,KAAAyB,GAAA;QACA,KAAA5C,QAAA,CAAA9C,KAAA;QACA;MACA;MACA,IAAA2F,aAAA,QAAApJ,UAAA,CAAA2E,MAAA,WAAAqD,IAAA;QAAA,OAAAA,IAAA;MAAA,GAAAlD,GAAA,WAAAkD,IAAA;QAAA,OAAAA,IAAA;MAAA,GAAAN,IAAA;MACA,KAAAxD,MAAA,CAAAiC,OAAA;QACA5H,KAAA;QACA4F,OAAA,sBAAAiF,aAAA,qBAAAA,aAAA;QACAhD,IAAA,WAAAA,KAAA;UACA8C,MAAA,CAAArK,OAAA;UACAnB,QAAA,CAAA2I,MAAA;YAAA8C,GAAA,EAAAA;UAAA,GAAA7C,IAAA,WAAA3C,GAAA;YACA,IAAAA,GAAA;cACAuF,MAAA,CAAA1F,QAAA,CAAA6F,OAAA;cACAH,MAAA,CAAA/F,YAAA;YACA;cACA+F,MAAA,CAAA1F,QAAA,CAAAC,KAAA,CAAAE,GAAA;YACA;UACA,GAAA6C,OAAA;YAAA,OAAA0C,MAAA,CAAArK,OAAA;UAAA;QACA;MACA;IACA;IACAyK,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAA1K,OAAA;MACA,IAAAyI,MAAA,QAAAD,QAAA;MACAC,MAAA,6BAAAkC,IAAA,GAAAC,eAAA;MACA/L,QAAA,CAAAgM,UAAA,CAAApC,MAAA;QACAiC,MAAA,CAAA1K,OAAA;MACA;IACA;IACA8K,WAAA,WAAAA,YAAA;MACA,KAAAxH,WAAA;MACA,IAAA8D,eAAA,QAAArC,KAAA,CAAAqC,eAAA;MACA,IAAAA,eAAA,EAAAA,eAAA,CAAAvB,UAAA;IACA;IACAkF,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,IAAAV,GAAA,QAAAnJ,UAAA,CAAA8E,GAAA,WAAAkD,IAAA;QAAA,OAAAA,IAAA;MAAA,GAAAN,IAAA;MACA,KAAAyB,GAAA;QACA,KAAA5C,QAAA,CAAA9C,KAAA;QACA;MACA;MACA,KAAA5E,OAAA;MACAnB,QAAA,CAAAoM,YAAA;QAAAX,GAAA,EAAAA;MAAA,GAAA7C,IAAA,WAAA3C,GAAA;QACA,IAAAA,GAAA;UACAkG,MAAA,CAAArG,QAAA,CAAA6F,OAAA;QACA;UACAQ,MAAA,CAAArG,QAAA,CAAAC,KAAA,CAAAE,GAAA;QACA;MACA,GAAA6C,OAAA;QAAA,OAAAqD,MAAA,CAAAhL,OAAA;MAAA;IACA;IACAkL,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACA,IAAAb,GAAA,QAAAnJ,UAAA,CAAA8E,GAAA,WAAAkD,IAAA;QAAA,OAAAA,IAAA;MAAA,GAAAN,IAAA;MACA,KAAA7I,OAAA;MACAnB,QAAA,CAAAqM,aAAA;QAAAZ,GAAA,EAAAA;MAAA,GAAA7C,IAAA,WAAA3C,GAAA;QACA,IAAAA,GAAA;UACAqG,MAAA,CAAAxG,QAAA,CAAA6F,OAAA;QACA;UACAW,MAAA,CAAAxG,QAAA,CAAAC,KAAA,CAAAE,GAAA;QACA;MACA,GAAA6C,OAAA;QAAA,OAAAwD,MAAA,CAAAnL,OAAA;MAAA;IACA;IACA;IACAoL,WAAA,WAAAA,YAAA/I,GAAA;MAAA,IAAAgJ,MAAA;MACA,KAAAhG,MAAA,CAAAiC,OAAA;QACA5H,KAAA;QACA4F,OAAA,4CAAAgG,MAAA,CAAAjJ,GAAA,CAAAuB,GAAA;QACA2D,IAAA,WAAAA,KAAA;UACA8D,MAAA,CAAAE,IAAA,CAAAlJ,GAAA;UACA;UACAxD,QAAA,CAAA2M,KAAA;YAAA7H,EAAA,EAAAtB,GAAA,CAAAsB;UAAA,GAAA8D,IAAA,WAAA3C,GAAA;YACA,IAAAA,GAAA,IAAAA,GAAA,CAAAG,IAAA;cACAoG,MAAA,CAAA1G,QAAA,CAAA6F,OAAA;cACAa,MAAA,CAAA/G,YAAA;YACA;cACA+G,MAAA,CAAA1G,QAAA,CAAAC,KAAA,CAAAE,GAAA,CAAAb,OAAA;YACA;UACA,GAAAwH,KAAA,WAAAhH,GAAA;YACA4G,MAAA,CAAA1G,QAAA,CAAAC,KAAA,cAAAH,GAAA,CAAAR,OAAA;UACA,GAAA0D,OAAA;YACA0D,MAAA,CAAAE,IAAA,CAAAlJ,GAAA;UACA;QACA;MACA;IACA;IACA;IACAqJ,YAAA,WAAAA,aAAArJ,GAAA;MACA;MACA,KAAAsJ,eAAA,CAAAtJ,GAAA;IACA;IAEAsJ,eAAA,WAAAA,gBAAAtJ,GAAA;MACA,KAAAqB,UAAA;QACAC,EAAA,EAAAtB,GAAA,CAAAsB,EAAA;QACAC,GAAA,EAAAvB,GAAA,CAAAuB,GAAA;QACAC,KAAA;QAAA;QACAC,OAAA;MACA;;MACA,KAAAN,kBAAA;IACA;IAEAoI,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,OAAA;MACA,KAAA9G,KAAA,CAAArB,UAAA,CAAAoI,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAF,OAAA,CAAApI,aAAA;UACA,IAAAgF,MAAA;YACA9E,EAAA,EAAAkI,OAAA,CAAAnI,UAAA,CAAAC,EAAA;YACAqI,SAAA;cACAnI,KAAA,EAAAgI,OAAA,CAAAnI,UAAA,CAAAG,KAAA;cAAA;cACAC,OAAA,EAAA+H,OAAA,CAAAnI,UAAA,CAAAI;YACA;UACA;UAEAhF,QAAA,CAAAmN,MAAA,CAAAxD,MAAA,EAAAhB,IAAA,WAAA3C,GAAA;YACA,IAAAA,GAAA,IAAAA,GAAA,CAAAG,IAAA;cACA4G,OAAA,CAAAlH,QAAA,CAAA6F,OAAA;cACAqB,OAAA,CAAArI,kBAAA;cACAqI,OAAA,CAAAvH,YAAA;YACA;cACAuH,OAAA,CAAAlH,QAAA,CAAAC,KAAA,CAAAE,GAAA,CAAAb,OAAA;YACA;UACA,GAAAwH,KAAA,WAAAhH,GAAA;YACAoH,OAAA,CAAAlH,QAAA,CAAAC,KAAA,cAAAH,GAAA,CAAAR,OAAA;UACA,GAAA0D,OAAA;YACAkE,OAAA,CAAApI,aAAA;UACA;QACA;MACA;IACA;IAEAyI,kBAAA,WAAAA,mBAAA;MACA,KAAA1I,kBAAA;MACA,KAAAE,UAAA;QACAC,EAAA;QACAC,GAAA;QACAC,KAAA;QAAA;QACAC,OAAA;MACA;IACA;EACA;AACA"}]}