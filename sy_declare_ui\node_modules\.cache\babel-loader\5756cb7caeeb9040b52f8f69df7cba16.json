{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\custom\\base\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\custom\\base\\index.vue", "mtime": 1752737748521}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\.babelrc", "mtime": 1752737748316}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IEN1c3RvbUNsYXNzIGZyb20gJy4vY3VzdG9tQ2xhc3MvaW5kZXgudnVlJzsgLy/miqXlhbPnsbvnm67mqKHlnZcKaW1wb3J0IFZhdE5vIGZyb20gJy4vdmF0Tm8vaW5kZXgudnVlJzsgLy/muIXlhbPnqI7lj7fnu7TmiqTooagKaW1wb3J0IFdoQWRkcmVzcyBmcm9tICcuL3doQWRkcmVzcy9pbmRleC52dWUnOyAvL+eJqea1geWVhua4oOmBkwppbXBvcnQgQ2xlYXJhbmNlTGluayBmcm9tICcuL2NsZWFyYW5jZUxpbmsvaW5kZXgudnVlJzsgLy/muIXlhbPov57mjqUKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdjdXN0b21CYXNlJywKICBjb21wb25lbnRzOiB7CiAgICBDdXN0b21DbGFzczogQ3VzdG9tQ2xhc3MsCiAgICBWYXRObzogVmF0Tm8sCiAgICBXaEFkZHJlc3M6IFdoQWRkcmVzcywKICAgIENsZWFyYW5jZUxpbms6IENsZWFyYW5jZUxpbmsKICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICB0YWJUeXBlOiAnY2F0ZWdvcnknLAogICAgICBjdXN0b21DbGFzc1Nob3c6IHRydWUsCiAgICAgIHZhdE5vU2hvdzogZmFsc2UsCiAgICAgIGNsZWFyYW5jZUxpbmtTaG93OiBmYWxzZSwKICAgICAgd2hBZGRyZXNzU0hvdzogZmFsc2UKICAgIH07CiAgfSwKICBtb3VudGVkOiBmdW5jdGlvbiBtb3VudGVkKCkge30sCiAgbWV0aG9kczogewogICAgY2hhbmdlVGFiczogZnVuY3Rpb24gY2hhbmdlVGFicyhuYW1lKSB7CiAgICAgIGlmIChuYW1lID09PSAiY2F0ZWdvcnkiICYmIHRoaXMuY3VzdG9tQ2xhc3NTaG93ID09PSBmYWxzZSkgewogICAgICAgIHRoaXMuY3VzdG9tQ2xhc3NTaG93ID0gdHJ1ZTsKICAgICAgfQogICAgICBpZiAobmFtZSA9PT0gImNsZWFyYW5jZUxpbmsiICYmIHRoaXMuY2xlYXJhbmNlTGlua1Nob3cgPT09IGZhbHNlKSB7CiAgICAgICAgdGhpcy5jbGVhcmFuY2VMaW5rU2hvdyA9IHRydWU7CiAgICAgIH0KICAgICAgaWYgKG5hbWUgPT09ICJ2YXRObyIgJiYgdGhpcy52YXROb1Nob3cgPT09IGZhbHNlKSB7CiAgICAgICAgdGhpcy52YXROb1Nob3cgPSB0cnVlOwogICAgICB9CiAgICAgIGlmIChuYW1lID09PSAid2hBZGRyZXNzIiAmJiB0aGlzLndoQWRkcmVzc1NIb3cgPT09IGZhbHNlKSB7CiAgICAgICAgdGhpcy53aEFkZHJlc3NTSG93ID0gdHJ1ZTsKICAgICAgfQogICAgfQogIH0KfTs="}, {"version": 3, "names": ["CustomClass", "VatNo", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ClearanceLink", "name", "components", "data", "tabType", "customClassShow", "vatNoShow", "clearanceLinkShow", "whAddressSHow", "mounted", "methods", "changeTabs"], "sources": ["src/view/module/custom/base/index.vue"], "sourcesContent": ["<!--\r\n@create date 2020-09-04\r\n<AUTHOR>\r\n@desc 报关基础设置\r\n-->\r\n<template>\r\n    <div class=\"FinanceBasics_box\">\r\n        <Tabs type=\"card\" name=\"home\"  v-model=\"tabType\" @on-click=\"changeTabs\">\r\n            <TabPane label=\"报关类目\"  name=\"category\"><CustomClass v-if=\"customClassShow\"/></TabPane>\r\n            <TabPane label=\"清关连接\"  name=\"clearanceLink\"><ClearanceLink v-if=\"clearanceLinkShow\"/></TabPane>\r\n            <TabPane label=\"清关税号维护表\"  name=\"vatNo\"><VatNo v-if=\"vatNoShow\"/></TabPane>\r\n            <TabPane label=\"仓库地址\"  name=\"whAddress\" ><WhAddress v-if=\"whAddressSHow\"/></TabPane>\r\n        </Tabs>\r\n    </div>\r\n</template>\r\n<script>\r\nimport CustomClass from './customClass/index.vue'; //报关类目模块\r\nimport VatNo from './vatNo/index.vue'; //清关税号维护表\r\nimport WhAddress from  './whAddress/index.vue';//物流商渠道\r\nimport ClearanceLink from './clearanceLink/index.vue';//清关连接\r\nexport default {\r\n    name:'customBase',\r\n    components:{CustomClass,VatNo,WhAddress,ClearanceLink},\r\n    data(){\r\n        return{\r\n          tabType:'category',\r\n          customClassShow:true,\r\n          vatNoShow:false,\r\n          clearanceLinkShow:false,\r\n          whAddressSHow:false,\r\n        }\r\n    },\r\n    mounted(){\r\n    },\r\n    methods:{\r\n      changeTabs(name){\r\n        if (name === \"category\" && this.customClassShow === false) {\r\n          this.customClassShow = true;\r\n        }\r\n        if (name === \"clearanceLink\" && this.clearanceLinkShow === false) {\r\n          this.clearanceLinkShow = true;\r\n        }\r\n        if (name === \"vatNo\" && this.vatNoShow === false) {\r\n          this.vatNoShow = true;\r\n        }\r\n        if (name === \"whAddress\" && this.whAddressSHow === false) {\r\n          this.whAddressSHow = true;\r\n        }\r\n      },\r\n    }\r\n}\r\n</script>\r\n<style  lang=\"less\">\r\n.FinanceBasics_box{\r\n  .ivu-tabs.ivu-tabs-card{\r\n    .ivu-tabs-bar{\r\n      margin-bottom: 0;\r\n    }\r\n    .ivu-tabs-nav-prev{\r\n        left:-12px;\r\n    }\r\n    .ivu-tabs-nav-next{\r\n        right: -8px;\r\n    }\r\n  }\r\n}\r\n\r\n</style>\r\n\r\n"], "mappings": "AAgBA,OAAAA,WAAA;AACA,OAAAC,KAAA;AACA,OAAAC,SAAA;AACA,OAAAC,aAAA;AACA;EACAC,IAAA;EACAC,UAAA;IAAAL,WAAA,EAAAA,WAAA;IAAAC,KAAA,EAAAA,KAAA;IAAAC,SAAA,EAAAA,SAAA;IAAAC,aAAA,EAAAA;EAAA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,eAAA;MACAC,SAAA;MACAC,iBAAA;MACAC,aAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA,GACA;EACAC,OAAA;IACAC,UAAA,WAAAA,WAAAV,IAAA;MACA,IAAAA,IAAA,wBAAAI,eAAA;QACA,KAAAA,eAAA;MACA;MACA,IAAAJ,IAAA,6BAAAM,iBAAA;QACA,KAAAA,iBAAA;MACA;MACA,IAAAN,IAAA,qBAAAK,SAAA;QACA,KAAAA,SAAA;MACA;MACA,IAAAL,IAAA,yBAAAO,aAAA;QACA,KAAAA,aAAA;MACA;IACA;EACA;AACA"}]}